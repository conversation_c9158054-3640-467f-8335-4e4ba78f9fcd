module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/data/defaultTemplate.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "defaultTemplateData": ()=>defaultTemplateData
});
const defaultTemplateData = {
    nl: {
        title: "OFFERTE",
        companyName: "A.S.Allround klussen",
        client: {
            name: "Holtara B.V.",
            address: "Vierwindenstraat 149",
            city: "1013 LA Amsterdam"
        },
        quote: {
            number: "OFF-2025-0013",
            date: "14 juli 2025",
            validUntil: "13 augustus 2025"
        },
        greeting: "Geachte Holtara B.V.,",
        introduction: "Hierbij ontvangt u onze offerte voor de professionele installatie van uw nieuwe vaatwasser inclusief demontage van het oude apparaat. Alle werkzaamheden worden uitgevoerd door ervaren monteurs met volledige garantie.",
        items: [
            {
                id: "1",
                itemNumber: 1,
                description: "Installatie nieuwe vaatwasser",
                subDescription: "Professionele installatie inclusief aansluiting",
                quantity: 2,
                unit: "uur",
                price: 75.00,
                vatRate: 21,
                total: 150.00
            },
            {
                id: "2",
                itemNumber: 2,
                description: "Aan- en afvoer aansluiten",
                subDescription: "Watertoevoer en afvoer op bestaande leidingen",
                quantity: 1,
                unit: "stuk",
                price: 99.00,
                vatRate: 21,
                total: 99.00
            },
            {
                id: "3",
                itemNumber: 3,
                description: "Demontage & afvoer oude vaatwasser",
                subDescription: "Vakkundige demontage en milieuvriendelijke afvoer",
                quantity: 1,
                unit: "stuk",
                price: 75.00,
                vatRate: 21,
                total: 75.00
            },
            {
                id: "4",
                itemNumber: 4,
                description: "Transport & levering",
                subDescription: "Ophalen en leveren vaatwasser op locatie",
                quantity: 1,
                unit: "stuk",
                price: 80.00,
                vatRate: 21,
                total: 80.00
            },
            {
                id: "5",
                itemNumber: 5,
                description: "Bosch SBV4ECX Vaatwasser",
                subDescription: "A+ energielabel, 12 couverts, 2 jaar fabrieksgarantie",
                quantity: 1,
                unit: "stuk",
                price: 686.00,
                vatRate: 21,
                total: 686.00
            }
        ],
        notes: "Bij contante betaling ontvangt u 5% korting op het totaalbedrag van € 1.199,11 (besparing € 59,96). Alle benodigde materialen zijn inbegrepen in deze offerte. De werkzaamheden worden naar verwachting binnen 2-3 uur afgerond. Na akkoord kunnen we de installatie binnen 2-3 werkdagen inplannen, afhankelijk van uw beschikbaarheid.",
        conditions: "Een aanbetaling van 30% (€ 359,73) is vereist voor het reserveren van materialen en het inplannen van de werkzaamheden. Deze offerte geldt alleen voor de vermelde werkzaamheden. Eventueel meerwerk wordt berekend tegen € 75,00/uur excl. BTW en alleen uitgevoerd na uw akkoord. Wij bieden 3 maanden garantie op het montagewerk en 2 jaar fabrieksgarantie op de vaatwasser. Kosteloos annuleren is mogelijk tot 7 dagen voor de geplande uitvoering, daarna wordt de aanbetaling ingehouden voor gemaakte kosten.",
        nextSteps: {
            step1: "Offerte Accorderen - Download bijlage, onderteken en mail <NAME_EMAIL> of stuur een e-mail met: \"Ik verklaar hierbij akkoord te gaan met de voorwaarden van offerte OFF-2025-0013.\"",
            step2: "Aanbetaling - Betaal € 359,73 voor materiaalreservering en planning",
            step3: "Afspraak Inplannen - Wij nemen z.s.m. contact op voor planning",
            step4: "Restbetaling - € 839,38 direct na afronding via contant, Tikkie, QR-code of factuur (binnen 14 dagen)"
        },
        company: {
            name: "A.S.Allround klussen",
            kvk: "********",
            btw: "NL002490282B64",
            address: "Kelspelstraat 3",
            city: "3641 JS Mijdrecht",
            website: "www.ASklussen.nl",
            email: "<EMAIL>",
            phone: "06-********"
        },
        payment: {
            iban: "NL54 INGB 0006 9210 65",
            accountName: "A.S.Allround klussen",
            depositPercentage: 30,
            hourlyRate: 75.00,
            cashDiscountPercentage: 5,
            paymentLink: "https://betaal.asklussen.nl/pay/"
        },
        contactTitle: "CONTACT & ONDERSTEUNING",
        contactDescription: "Heeft u vragen over deze offerte of wilt u een aangepaste begroting? Neem gerust contact met ons op via onderstaande kanalen. Wij helpen u graag verder en staan klaar om al uw vragen te beantwoorden."
    },
    en: {
        title: "QUOTE",
        companyName: "A.S.Allround Services",
        client: {
            name: "Holtara B.V.",
            address: "Vierwindenstraat 149",
            city: "1013 LA Amsterdam"
        },
        quote: {
            number: "QUO-2025-0013",
            date: "July 14, 2025",
            validUntil: "August 13, 2025"
        },
        greeting: "Dear Holtara B.V.,",
        introduction: "Please find our quote for the professional installation of your new dishwasher including removal of the old appliance. All work will be carried out by experienced technicians with full warranty.",
        items: [
            {
                id: "1",
                itemNumber: 1,
                description: "New dishwasher installation",
                subDescription: "Professional installation including connection",
                quantity: 2,
                unit: "hour",
                price: 75.00,
                vatRate: 21,
                total: 150.00
            },
            {
                id: "2",
                itemNumber: 2,
                description: "Water supply and drainage connection",
                subDescription: "Water supply and drainage to existing pipes",
                quantity: 1,
                unit: "piece",
                price: 99.00,
                vatRate: 21,
                total: 99.00
            },
            {
                id: "3",
                itemNumber: 3,
                description: "Removal & disposal of old dishwasher",
                subDescription: "Professional removal and environmentally friendly disposal",
                quantity: 1,
                unit: "piece",
                price: 75.00,
                vatRate: 21,
                total: 75.00
            },
            {
                id: "4",
                itemNumber: 4,
                description: "Transport & delivery",
                subDescription: "Pick up and deliver dishwasher to location",
                quantity: 1,
                unit: "piece",
                price: 80.00,
                vatRate: 21,
                total: 80.00
            },
            {
                id: "5",
                itemNumber: 5,
                description: "Bosch SBV4ECX Dishwasher",
                subDescription: "A+ energy label, 12 place settings, 2 year manufacturer warranty",
                quantity: 1,
                unit: "piece",
                price: 686.00,
                vatRate: 21,
                total: 686.00
            }
        ],
        notes: "With cash payment you receive 5% discount on the total amount of € 1,199.11 (saving € 59.96). All necessary materials are included in this quote. The work is expected to be completed within 2-3 hours. After approval, we can schedule the installation within 2-3 working days, depending on your availability.",
        conditions: "A deposit of 30% (€ 359.73) is required for reserving materials and scheduling the work. This quote applies only to the mentioned work. Any additional work will be charged at € 75.00/hour excl. VAT and only carried out after your approval. We offer 3 months warranty on installation work and 2 years manufacturer warranty on the dishwasher. Free cancellation is possible up to 7 days before scheduled execution, after which the deposit will be retained for costs incurred.",
        nextSteps: {
            step1: "Approve Quote - Download attachment, sign and email <NAME_EMAIL> or send an email with: \"I hereby declare to agree with the conditions of quote QUO-2025-0013.\"",
            step2: "Deposit - Pay € 359.73 for material reservation and planning",
            step3: "Schedule Appointment - We will contact you as soon as possible for planning",
            step4: "Final Payment - € 839.38 directly after completion via cash, Tikkie, QR-code or invoice (within 14 days)"
        },
        company: {
            name: "A.S.Allround Services",
            kvk: "********",
            btw: "NL002490282B64",
            address: "Kelspelstraat 3",
            city: "3641 JS Mijdrecht",
            website: "www.ASklussen.nl",
            email: "<EMAIL>",
            phone: "06-********"
        },
        payment: {
            iban: "NL54 INGB 0006 9210 65",
            accountName: "A.S.Allround Services",
            depositPercentage: 30,
            hourlyRate: 75.00,
            cashDiscountPercentage: 5,
            paymentLink: "https://betaal.asklussen.nl/pay/"
        },
        contactTitle: "CONTACT & SUPPORT",
        contactDescription: "Do you have questions about this quote or would you like a customized estimate? Please feel free to contact us through the channels below. We are happy to help you and are ready to answer all your questions."
    }
};
}),
"[project]/src/components/FormEditor.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "FormEditor": ()=>FormEditor
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/plus.js [app-ssr] (ecmascript) <export default as Plus>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trash-2.js [app-ssr] (ecmascript) <export default as Trash2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-right.js [app-ssr] (ecmascript) <export default as ChevronRight>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$left$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronLeft$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-left.js [app-ssr] (ecmascript) <export default as ChevronLeft>");
'use client';
;
;
;
const FORM_STEPS = [
    {
        id: 'header',
        titleNL: 'Header & Basis',
        titleEN: 'Header & Basic'
    },
    {
        id: 'client',
        titleNL: 'Klant & Offerte',
        titleEN: 'Client & Quote'
    },
    {
        id: 'items',
        titleNL: 'Offerte Items',
        titleEN: 'Quote Items'
    },
    {
        id: 'notes',
        titleNL: 'Opmerkingen',
        titleEN: 'Notes'
    },
    {
        id: 'steps',
        titleNL: 'Vervolgstappen',
        titleEN: 'Next Steps'
    },
    {
        id: 'company',
        titleNL: 'Bedrijfsgegevens',
        titleEN: 'Company Info'
    },
    {
        id: 'payment',
        titleNL: 'Betaalgegevens',
        titleEN: 'Payment Info'
    }
];
function FormEditor({ data, language, onChange }) {
    const [currentStep, setCurrentStep] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const nextStep = ()=>{
        if (currentStep < FORM_STEPS.length - 1) {
            setCurrentStep(currentStep + 1);
        }
    };
    const prevStep = ()=>{
        if (currentStep > 0) {
            setCurrentStep(currentStep - 1);
        }
    };
    const updateData = (updates)=>{
        onChange({
            ...data,
            ...updates
        });
    };
    const updateNestedData = (key, updates)=>{
        onChange({
            ...data,
            [key]: {
                ...data[key],
                ...updates
            }
        });
    };
    const addItem = ()=>{
        const newItemNumber = Math.max(...data.items.map((item)=>item.itemNumber), 0) + 1;
        const newItem = {
            id: Date.now().toString(),
            itemNumber: newItemNumber,
            description: language === 'nl' ? 'Nieuwe service' : 'New service',
            subDescription: language === 'nl' ? 'Beschrijving van de service' : 'Service description',
            quantity: 1,
            unit: language === 'nl' ? 'stuk' : 'piece',
            price: 0,
            vatRate: 21,
            total: 0
        };
        updateData({
            items: [
                ...data.items,
                newItem
            ]
        });
    };
    const updateItem = (index, updates)=>{
        const updatedItems = data.items.map((item, i)=>{
            if (i === index) {
                const updatedItem = {
                    ...item,
                    ...updates
                };
                // Recalculate total when price, quantity, or VAT changes - total should be EXCLUDING VAT
                if ('price' in updates || 'quantity' in updates) {
                    updatedItem.total = updatedItem.price * updatedItem.quantity;
                }
                return updatedItem;
            }
            return item;
        });
        updateData({
            items: updatedItems
        });
    };
    const removeItem = (index)=>{
        const updatedItems = data.items.filter((_, i)=>i !== index);
        updateData({
            items: updatedItems
        });
    };
    const currentStepData = FORM_STEPS[currentStep];
    const stepTitle = language === 'nl' ? currentStepData.titleNL : currentStepData.titleEN;
    // Simple input component that doesn't lose focus
    const SimpleInput = ({ label, defaultValue, onChange, type = 'text', placeholder = '' })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                    className: "block text-xs font-medium text-gray-700 mb-1",
                    children: label
                }, void 0, false, {
                    fileName: "[project]/src/components/FormEditor.tsx",
                    lineNumber: 102,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                    type: type,
                    defaultValue: String(defaultValue || ''),
                    onBlur: (e)=>{
                        const val = e.target.value;
                        if (type === 'number') {
                            onChange(val === '' ? 0 : parseFloat(val) || 0);
                        } else {
                            onChange(val);
                        }
                    },
                    placeholder: placeholder,
                    className: "w-full px-3 py-2 border border-gray-300 rounded-md text-sm bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                }, void 0, false, {
                    fileName: "[project]/src/components/FormEditor.tsx",
                    lineNumber: 103,
                    columnNumber: 7
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/FormEditor.tsx",
            lineNumber: 101,
            columnNumber: 5
        }, this);
    const SimpleTextArea = ({ label, defaultValue, onChange, rows = 2 })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                    className: "block text-xs font-medium text-gray-700 mb-1",
                    children: label
                }, void 0, false, {
                    fileName: "[project]/src/components/FormEditor.tsx",
                    lineNumber: 127,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                    defaultValue: String(defaultValue || ''),
                    onBlur: (e)=>onChange(e.target.value),
                    rows: rows,
                    className: "w-full px-3 py-2 border border-gray-300 rounded-md text-sm bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                }, void 0, false, {
                    fileName: "[project]/src/components/FormEditor.tsx",
                    lineNumber: 128,
                    columnNumber: 7
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/FormEditor.tsx",
            lineNumber: 126,
            columnNumber: 5
        }, this);
    const SimpleSelect = ({ label, defaultValue, onChange, options })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                    className: "block text-xs font-medium text-gray-700 mb-1",
                    children: label
                }, void 0, false, {
                    fileName: "[project]/src/components/FormEditor.tsx",
                    lineNumber: 144,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                    defaultValue: String(defaultValue || ''),
                    onChange: (e)=>{
                        const val = e.target.value;
                        const num = parseFloat(val);
                        onChange(isNaN(num) ? val : num);
                    },
                    className: "w-full px-3 py-2 border border-gray-300 rounded-md text-sm bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    children: options.map((option)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                            defaultValue: option.value,
                            children: option.label
                        }, option.value, false, {
                            fileName: "[project]/src/components/FormEditor.tsx",
                            lineNumber: 155,
                            columnNumber: 11
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/src/components/FormEditor.tsx",
                    lineNumber: 145,
                    columnNumber: 7
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/FormEditor.tsx",
            lineNumber: 143,
            columnNumber: 5
        }, this);
    const renderStepContent = ()=>{
        switch(currentStepData.id){
            case 'header':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                            label: language === 'nl' ? 'Titel' : 'Title',
                            defaultValue: data.title,
                            onChange: (value)=>updateData({
                                    title: value
                                })
                        }, void 0, false, {
                            fileName: "[project]/src/components/FormEditor.tsx",
                            lineNumber: 168,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                            label: language === 'nl' ? 'Bedrijfsnaam' : 'Company Name',
                            defaultValue: data.companyName,
                            onChange: (value)=>updateData({
                                    companyName: value
                                })
                        }, void 0, false, {
                            fileName: "[project]/src/components/FormEditor.tsx",
                            lineNumber: 173,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/FormEditor.tsx",
                    lineNumber: 167,
                    columnNumber: 11
                }, this);
            case 'client':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-2 gap-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                                    label: language === 'nl' ? 'Klant Naam' : 'Client Name',
                                    defaultValue: data.client.name,
                                    onChange: (value)=>updateNestedData('client', {
                                            name: value
                                        })
                                }, void 0, false, {
                                    fileName: "[project]/src/components/FormEditor.tsx",
                                    lineNumber: 185,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                                    label: language === 'nl' ? 'Offerte Nummer' : 'Quote Number',
                                    defaultValue: data.quote.number,
                                    onChange: (value)=>updateNestedData('quote', {
                                            number: value
                                        })
                                }, void 0, false, {
                                    fileName: "[project]/src/components/FormEditor.tsx",
                                    lineNumber: 190,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/FormEditor.tsx",
                            lineNumber: 184,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-2 gap-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                                    label: language === 'nl' ? 'Adres' : 'Address',
                                    defaultValue: data.client.address,
                                    onChange: (value)=>updateNestedData('client', {
                                            address: value
                                        })
                                }, void 0, false, {
                                    fileName: "[project]/src/components/FormEditor.tsx",
                                    lineNumber: 197,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                                    label: language === 'nl' ? 'Stad' : 'City',
                                    defaultValue: data.client.city,
                                    onChange: (value)=>updateNestedData('client', {
                                            city: value
                                        })
                                }, void 0, false, {
                                    fileName: "[project]/src/components/FormEditor.tsx",
                                    lineNumber: 202,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/FormEditor.tsx",
                            lineNumber: 196,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-2 gap-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                                    label: language === 'nl' ? 'Datum' : 'Date',
                                    defaultValue: data.quote.date,
                                    onChange: (value)=>updateNestedData('quote', {
                                            date: value
                                        })
                                }, void 0, false, {
                                    fileName: "[project]/src/components/FormEditor.tsx",
                                    lineNumber: 209,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                                    label: language === 'nl' ? 'Geldig tot' : 'Valid Until',
                                    defaultValue: data.quote.validUntil,
                                    onChange: (value)=>updateNestedData('quote', {
                                            validUntil: value
                                        })
                                }, void 0, false, {
                                    fileName: "[project]/src/components/FormEditor.tsx",
                                    lineNumber: 214,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/FormEditor.tsx",
                            lineNumber: 208,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleTextArea, {
                            label: language === 'nl' ? 'Begroeting' : 'Greeting',
                            defaultValue: data.greeting,
                            onChange: (value)=>updateData({
                                    greeting: value
                                }),
                            rows: 1
                        }, void 0, false, {
                            fileName: "[project]/src/components/FormEditor.tsx",
                            lineNumber: 220,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleTextArea, {
                            label: language === 'nl' ? 'Introductie' : 'Introduction',
                            defaultValue: data.introduction,
                            onChange: (value)=>updateData({
                                    introduction: value
                                }),
                            rows: 2
                        }, void 0, false, {
                            fileName: "[project]/src/components/FormEditor.tsx",
                            lineNumber: 226,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/FormEditor.tsx",
                    lineNumber: 183,
                    columnNumber: 11
                }, this);
            case 'items':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-3",
                    children: [
                        data.items.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "border border-gray-200 rounded p-3 bg-gray-50",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex justify-between items-center mb-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                className: "text-sm font-medium text-gray-900",
                                                children: [
                                                    language === 'nl' ? 'Item' : 'Item',
                                                    " ",
                                                    item.itemNumber
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/FormEditor.tsx",
                                                lineNumber: 241,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>removeItem(index),
                                                className: "text-red-500 hover:text-red-700",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__["Trash2"], {
                                                    className: "h-3 w-3"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/FormEditor.tsx",
                                                    lineNumber: 248,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/FormEditor.tsx",
                                                lineNumber: 244,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/FormEditor.tsx",
                                        lineNumber: 240,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "grid grid-cols-3 gap-2 mb-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                                                label: "#",
                                                defaultValue: item.itemNumber,
                                                onChange: (value)=>updateItem(index, {
                                                        itemNumber: value
                                                    }),
                                                type: "number"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/FormEditor.tsx",
                                                lineNumber: 253,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                                                label: language === 'nl' ? 'Aantal' : 'Qty',
                                                defaultValue: item.quantity,
                                                onChange: (value)=>updateItem(index, {
                                                        quantity: value
                                                    }),
                                                type: "number"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/FormEditor.tsx",
                                                lineNumber: 259,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                                                label: language === 'nl' ? 'Eenheid' : 'Unit',
                                                defaultValue: item.unit,
                                                onChange: (value)=>updateItem(index, {
                                                        unit: value
                                                    })
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/FormEditor.tsx",
                                                lineNumber: 265,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/FormEditor.tsx",
                                        lineNumber: 252,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "grid grid-cols-1 gap-2 mb-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                                                label: language === 'nl' ? 'Beschrijving' : 'Description',
                                                defaultValue: item.description,
                                                onChange: (value)=>updateItem(index, {
                                                        description: value
                                                    })
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/FormEditor.tsx",
                                                lineNumber: 273,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                                                label: language === 'nl' ? 'Sub-beschrijving' : 'Sub-description',
                                                defaultValue: item.subDescription,
                                                onChange: (value)=>updateItem(index, {
                                                        subDescription: value
                                                    })
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/FormEditor.tsx",
                                                lineNumber: 278,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/FormEditor.tsx",
                                        lineNumber: 272,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "grid grid-cols-3 gap-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                                                label: language === 'nl' ? 'Prijs (€)' : 'Price (€)',
                                                defaultValue: item.price,
                                                onChange: (value)=>updateItem(index, {
                                                        price: value
                                                    }),
                                                type: "number"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/FormEditor.tsx",
                                                lineNumber: 286,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleSelect, {
                                                label: language === 'nl' ? 'BTW' : 'VAT',
                                                defaultValue: item.vatRate,
                                                onChange: (value)=>updateItem(index, {
                                                        vatRate: value
                                                    }),
                                                options: [
                                                    {
                                                        value: 21,
                                                        label: '21%'
                                                    },
                                                    {
                                                        value: 9,
                                                        label: '9%'
                                                    }
                                                ]
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/FormEditor.tsx",
                                                lineNumber: 292,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-end",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-xs text-gray-600 pb-1",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                        children: [
                                                            language === 'nl' ? 'Totaal excl.:' : 'Total excl.:',
                                                            " €",
                                                            item.total.toFixed(2)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/FormEditor.tsx",
                                                        lineNumber: 303,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/FormEditor.tsx",
                                                    lineNumber: 302,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/FormEditor.tsx",
                                                lineNumber: 301,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/FormEditor.tsx",
                                        lineNumber: 285,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, item.id, true, {
                                fileName: "[project]/src/components/FormEditor.tsx",
                                lineNumber: 239,
                                columnNumber: 15
                            }, this)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: addItem,
                            className: "w-full flex items-center justify-center px-3 py-2 border border-dashed border-gray-300 rounded text-sm text-gray-600 hover:text-gray-900 hover:border-gray-400 transition-colors",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"], {
                                    className: "h-4 w-4 mr-2"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/FormEditor.tsx",
                                    lineNumber: 314,
                                    columnNumber: 15
                                }, this),
                                language === 'nl' ? 'Item Toevoegen' : 'Add Item'
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/FormEditor.tsx",
                            lineNumber: 310,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/FormEditor.tsx",
                    lineNumber: 237,
                    columnNumber: 11
                }, this);
            case 'notes':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleTextArea, {
                            label: language === 'nl' ? 'Opmerkingen' : 'Notes',
                            defaultValue: data.notes,
                            onChange: (value)=>updateData({
                                    notes: value
                                }),
                            rows: 3
                        }, void 0, false, {
                            fileName: "[project]/src/components/FormEditor.tsx",
                            lineNumber: 323,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleTextArea, {
                            label: language === 'nl' ? 'Voorwaarden' : 'Conditions',
                            defaultValue: data.conditions,
                            onChange: (value)=>updateData({
                                    conditions: value
                                }),
                            rows: 3
                        }, void 0, false, {
                            fileName: "[project]/src/components/FormEditor.tsx",
                            lineNumber: 329,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/FormEditor.tsx",
                    lineNumber: 322,
                    columnNumber: 11
                }, this);
            case 'steps':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleTextArea, {
                            label: language === 'nl' ? 'Stap 1: Offerte Accorderen' : 'Step 1: Approve Quote',
                            defaultValue: data.nextSteps.step1,
                            onChange: (value)=>updateNestedData('nextSteps', {
                                    step1: value
                                }),
                            rows: 2
                        }, void 0, false, {
                            fileName: "[project]/src/components/FormEditor.tsx",
                            lineNumber: 341,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleTextArea, {
                            label: language === 'nl' ? 'Stap 2: Aanbetaling' : 'Step 2: Deposit',
                            defaultValue: data.nextSteps.step2,
                            onChange: (value)=>updateNestedData('nextSteps', {
                                    step2: value
                                }),
                            rows: 1
                        }, void 0, false, {
                            fileName: "[project]/src/components/FormEditor.tsx",
                            lineNumber: 347,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleTextArea, {
                            label: language === 'nl' ? 'Stap 3: Afspraak Inplannen' : 'Step 3: Schedule Appointment',
                            defaultValue: data.nextSteps.step3,
                            onChange: (value)=>updateNestedData('nextSteps', {
                                    step3: value
                                }),
                            rows: 1
                        }, void 0, false, {
                            fileName: "[project]/src/components/FormEditor.tsx",
                            lineNumber: 353,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleTextArea, {
                            label: language === 'nl' ? 'Stap 4: Restbetaling' : 'Step 4: Final Payment',
                            defaultValue: data.nextSteps.step4,
                            onChange: (value)=>updateNestedData('nextSteps', {
                                    step4: value
                                }),
                            rows: 1
                        }, void 0, false, {
                            fileName: "[project]/src/components/FormEditor.tsx",
                            lineNumber: 359,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/FormEditor.tsx",
                    lineNumber: 340,
                    columnNumber: 11
                }, this);
            case 'company':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-2 gap-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                                    label: language === 'nl' ? 'Bedrijfsnaam' : 'Company Name',
                                    defaultValue: data.company.name,
                                    onChange: (value)=>updateNestedData('company', {
                                            name: value
                                        })
                                }, void 0, false, {
                                    fileName: "[project]/src/components/FormEditor.tsx",
                                    lineNumber: 372,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                                    label: "Website",
                                    defaultValue: data.company.website,
                                    onChange: (value)=>updateNestedData('company', {
                                            website: value
                                        })
                                }, void 0, false, {
                                    fileName: "[project]/src/components/FormEditor.tsx",
                                    lineNumber: 377,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/FormEditor.tsx",
                            lineNumber: 371,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-2 gap-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                                    label: "KvK",
                                    defaultValue: data.company.kvk,
                                    onChange: (value)=>updateNestedData('company', {
                                            kvk: value
                                        })
                                }, void 0, false, {
                                    fileName: "[project]/src/components/FormEditor.tsx",
                                    lineNumber: 384,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                                    label: "BTW",
                                    defaultValue: data.company.btw,
                                    onChange: (value)=>updateNestedData('company', {
                                            btw: value
                                        })
                                }, void 0, false, {
                                    fileName: "[project]/src/components/FormEditor.tsx",
                                    lineNumber: 389,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/FormEditor.tsx",
                            lineNumber: 383,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-2 gap-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                                    label: language === 'nl' ? 'Adres' : 'Address',
                                    defaultValue: data.company.address,
                                    onChange: (value)=>updateNestedData('company', {
                                            address: value
                                        })
                                }, void 0, false, {
                                    fileName: "[project]/src/components/FormEditor.tsx",
                                    lineNumber: 396,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                                    label: language === 'nl' ? 'Stad' : 'City',
                                    defaultValue: data.company.city,
                                    onChange: (value)=>updateNestedData('company', {
                                            city: value
                                        })
                                }, void 0, false, {
                                    fileName: "[project]/src/components/FormEditor.tsx",
                                    lineNumber: 401,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/FormEditor.tsx",
                            lineNumber: 395,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-2 gap-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                                    label: "Email",
                                    defaultValue: data.company.email,
                                    onChange: (value)=>updateNestedData('company', {
                                            email: value
                                        })
                                }, void 0, false, {
                                    fileName: "[project]/src/components/FormEditor.tsx",
                                    lineNumber: 408,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                                    label: language === 'nl' ? 'Telefoon' : 'Phone',
                                    defaultValue: data.company.phone,
                                    onChange: (value)=>updateNestedData('company', {
                                            phone: value
                                        })
                                }, void 0, false, {
                                    fileName: "[project]/src/components/FormEditor.tsx",
                                    lineNumber: 413,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/FormEditor.tsx",
                            lineNumber: 407,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/FormEditor.tsx",
                    lineNumber: 370,
                    columnNumber: 11
                }, this);
            case 'payment':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-2 gap-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                                    label: "IBAN",
                                    defaultValue: data.payment.iban,
                                    onChange: (value)=>updateNestedData('payment', {
                                            iban: value
                                        })
                                }, void 0, false, {
                                    fileName: "[project]/src/components/FormEditor.tsx",
                                    lineNumber: 426,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                                    label: language === 'nl' ? 'Rekeninghouder' : 'Account Name',
                                    defaultValue: data.payment.accountName,
                                    onChange: (value)=>updateNestedData('payment', {
                                            accountName: value
                                        })
                                }, void 0, false, {
                                    fileName: "[project]/src/components/FormEditor.tsx",
                                    lineNumber: 431,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/FormEditor.tsx",
                            lineNumber: 425,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                            label: language === 'nl' ? 'Betaallink' : 'Payment Link',
                            defaultValue: data.payment.paymentLink,
                            onChange: (value)=>updateNestedData('payment', {
                                    paymentLink: value
                                }),
                            placeholder: "https://betaal.example.com/pay/"
                        }, void 0, false, {
                            fileName: "[project]/src/components/FormEditor.tsx",
                            lineNumber: 437,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-2 gap-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                                    label: language === 'nl' ? 'Aanbetaling (%)' : 'Deposit (%)',
                                    defaultValue: data.payment.depositPercentage,
                                    onChange: (value)=>updateNestedData('payment', {
                                            depositPercentage: value
                                        }),
                                    type: "number",
                                    placeholder: "30"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/FormEditor.tsx",
                                    lineNumber: 444,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                                    label: language === 'nl' ? 'Contantkorting (%)' : 'Cash Discount (%)',
                                    defaultValue: data.payment.cashDiscountPercentage,
                                    onChange: (value)=>updateNestedData('payment', {
                                            cashDiscountPercentage: value
                                        }),
                                    type: "number",
                                    placeholder: "5"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/FormEditor.tsx",
                                    lineNumber: 451,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/FormEditor.tsx",
                            lineNumber: 443,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-1 gap-3",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SimpleInput, {
                                label: language === 'nl' ? 'Uurtarief (€)' : 'Hourly Rate (€)',
                                defaultValue: data.payment.hourlyRate,
                                onChange: (value)=>updateNestedData('payment', {
                                        hourlyRate: value
                                    }),
                                type: "number",
                                placeholder: "75.00"
                            }, void 0, false, {
                                fileName: "[project]/src/components/FormEditor.tsx",
                                lineNumber: 460,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/FormEditor.tsx",
                            lineNumber: 459,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/FormEditor.tsx",
                    lineNumber: 424,
                    columnNumber: 11
                }, this);
            default:
                return null;
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-4",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-between mb-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center space-x-2",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-sm text-gray-500",
                            children: [
                                language === 'nl' ? 'Stap' : 'Step',
                                " ",
                                currentStep + 1,
                                " ",
                                language === 'nl' ? 'van' : 'of',
                                " ",
                                FORM_STEPS.length
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/FormEditor.tsx",
                            lineNumber: 481,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/FormEditor.tsx",
                        lineNumber: 480,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex space-x-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: prevStep,
                                disabled: currentStep === 0,
                                className: "flex items-center px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$left$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronLeft$3e$__["ChevronLeft"], {
                                        className: "h-4 w-4 mr-1"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/FormEditor.tsx",
                                        lineNumber: 491,
                                        columnNumber: 13
                                    }, this),
                                    language === 'nl' ? 'Vorige' : 'Previous'
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/FormEditor.tsx",
                                lineNumber: 486,
                                columnNumber: 11
                            }, this),
                            currentStep === FORM_STEPS.length - 1 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: "flex items-center px-4 py-2 text-sm bg-green-600 text-white rounded hover:bg-green-700 font-medium",
                                children: [
                                    "✓ ",
                                    language === 'nl' ? 'Voltooien' : 'Complete'
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/FormEditor.tsx",
                                lineNumber: 495,
                                columnNumber: 13
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: nextStep,
                                className: "flex items-center px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700",
                                children: [
                                    language === 'nl' ? 'Volgende' : 'Next',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__["ChevronRight"], {
                                        className: "h-4 w-4 ml-1"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/FormEditor.tsx",
                                        lineNumber: 506,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/FormEditor.tsx",
                                lineNumber: 501,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/FormEditor.tsx",
                        lineNumber: 485,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/FormEditor.tsx",
                lineNumber: 479,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center space-x-2 mb-4",
                children: FORM_STEPS.map((step, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>setCurrentStep(index),
                                className: `w-8 h-8 rounded-full text-xs font-medium ${index === currentStep ? 'bg-blue-600 text-white' : index < currentStep ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-600'}`,
                                children: index + 1
                            }, void 0, false, {
                                fileName: "[project]/src/components/FormEditor.tsx",
                                lineNumber: 516,
                                columnNumber: 13
                            }, this),
                            index < FORM_STEPS.length - 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `w-8 h-0.5 ${index < currentStep ? 'bg-green-600' : 'bg-gray-200'}`
                            }, void 0, false, {
                                fileName: "[project]/src/components/FormEditor.tsx",
                                lineNumber: 529,
                                columnNumber: 15
                            }, this)
                        ]
                    }, step.id, true, {
                        fileName: "[project]/src/components/FormEditor.tsx",
                        lineNumber: 515,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/components/FormEditor.tsx",
                lineNumber: 513,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white border border-gray-200 rounded-lg p-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-semibold text-gray-900 mb-4",
                        children: stepTitle
                    }, void 0, false, {
                        fileName: "[project]/src/components/FormEditor.tsx",
                        lineNumber: 537,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "max-h-[calc(100vh-400px)] overflow-y-auto",
                        children: renderStepContent()
                    }, void 0, false, {
                        fileName: "[project]/src/components/FormEditor.tsx",
                        lineNumber: 538,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/FormEditor.tsx",
                lineNumber: 536,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/FormEditor.tsx",
        lineNumber: 477,
        columnNumber: 5
    }, this);
}
}),
"[project]/src/components/TemplatePreview.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "TemplatePreview": ()=>TemplatePreview
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
'use client';
;
function TemplatePreview({ data, language }) {
    // Calculate totals - items.total should be excluding VAT
    const subtotal = data.items.reduce((sum, item)=>sum + item.total, 0);
    // Group VAT by rate
    const vatByRate = data.items.reduce((acc, item)=>{
        const vatAmount = item.total * item.vatRate / 100;
        if (!acc[item.vatRate]) {
            acc[item.vatRate] = 0;
        }
        acc[item.vatRate] += vatAmount;
        return acc;
    }, {});
    const totalVatAmount = Object.values(vatByRate).reduce((sum, vat)=>sum + vat, 0);
    const total = subtotal + totalVatAmount;
    const depositAmount = total * (data.payment.depositPercentage / 100);
    const remainingAmount = total - depositAmount;
    const cashDiscountAmount = total * (data.payment.cashDiscountPercentage / 100);
    const totalWithCashDiscount = total - cashDiscountAmount;
    // Generate QR code URL from payment link
    const generateQRCode = ()=>{
        if (!data.payment.paymentLink) return '';
        const paymentUrl = `${data.payment.paymentLink}${data.quote.number}?amount=${depositAmount.toFixed(2)}`;
        return `https://api.qrserver.com/v1/create-qr-code/?size=50x50&data=${encodeURIComponent(paymentUrl)}`;
    };
    // Replace dynamic values in text
    const replaceDynamicValues = (text)=>{
        let result = text;
        // OPMERKINGEN: Replace cash discount calculations
        if (text.includes('contante betaling') || text.includes('cash payment')) {
            // Replace specific patterns step by step
            // 1. Replace total amount after "totaalbedrag van"
            result = result.replace(/(totaalbedrag van\s+)€\s*[\d.,]+/gi, `$1€ ${total.toFixed(2)}`);
            result = result.replace(/(total amount of\s+)€\s*[\d.,]+/gi, `$1€ ${total.toFixed(2)}`);
            // 2. Replace savings amount after "besparing"
            result = result.replace(/(besparing\s+)€\s*[\d.,]+/gi, `$1€ ${cashDiscountAmount.toFixed(2)}`);
            result = result.replace(/(saving\s+)€\s*[\d.,]+/gi, `$1€ ${cashDiscountAmount.toFixed(2)}`);
            // 3. Replace placeholder text
            result = result.replace(/hier moet %?5 van bedraag van offerte incl btw hier plaatsen €/gi, `€ ${cashDiscountAmount.toFixed(2)}`);
            result = result.replace(/here must %?5 of quote amount incl vat here place €/gi, `€ ${cashDiscountAmount.toFixed(2)}`);
        }
        // VOORWAARDEN: Replace deposit and hourly rate
        if (text.includes('aanbetaling van') || text.includes('deposit of')) {
            // Replace deposit amount after "aanbetaling van"
            result = result.replace(/(aanbetaling van\s+\d+%\s+\()€\s*[\d.,]+/gi, `$1€ ${depositAmount.toFixed(2)}`);
            result = result.replace(/(deposit of\s+\d+%\s+\()€\s*[\d.,]+/gi, `$1€ ${depositAmount.toFixed(2)}`);
            // Replace hourly rate after "tegen" or "at"
            result = result.replace(/(tegen\s+)€\s*[\d.,]+/gi, `$1€ ${data.payment.hourlyRate.toFixed(2)}`);
            result = result.replace(/(at\s+)€\s*[\d.,]+/gi, `$1€ ${data.payment.hourlyRate.toFixed(2)}`);
            // Add "excl. BTW" after hourly rate if not present
            if (!result.includes('excl. BTW') && !result.includes('excl. VAT')) {
                result = result.replace(/(€\s*[\d.,]+\/uur)/, '$1 excl. BTW');
            }
        }
        // VERVOLGSTAPPEN: Replace deposit and remaining amounts
        if (text.includes('Betaal') || text.includes('Pay') || text.includes('Restbetaling')) {
            result = result.replace(/€\s*[\d.,]+/g, (match, offset)=>{
                const beforeMatch = text.substring(0, offset);
                // Deposit payment (30% of total incl. BTW)
                if (beforeMatch.includes('Betaal') || beforeMatch.includes('Pay')) {
                    return `€ ${depositAmount.toFixed(2)}`;
                }
                // Remaining payment (70% of total incl. BTW)
                if (beforeMatch.includes('Restbetaling') || beforeMatch.includes('Final payment')) {
                    return `€ ${remainingAmount.toFixed(2)}`;
                }
                return match;
            });
        }
        // Replace percentage values dynamically
        result = result.replace(/(\d+)%/g, (match, percentage)=>{
            if (text.includes('aanbetaling') || text.includes('deposit')) {
                return `${data.payment.depositPercentage}%`;
            }
            if (text.includes('korting') || text.includes('discount')) {
                return `${data.payment.cashDiscountPercentage}%`;
            }
            return match;
        });
        // Replace quote numbers everywhere
        const oldQuotePattern = /OFF-2025-\d+|QUO-2025-\d+/g;
        result = result.replace(oldQuotePattern, data.quote.number);
        return result;
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "max-w-2xl mx-auto bg-white shadow-lg rounded-lg overflow-hidden",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            style: {
                backgroundColor: '#f3f4f6',
                padding: '25px 10px'
            },
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    maxWidth: '580px',
                    margin: '0 auto',
                    backgroundColor: '#ffffff',
                    borderRadius: '10px',
                    overflow: 'hidden',
                    boxShadow: '0 4px 15px rgba(0,0,0,0.08)'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            backgroundColor: '#111827',
                            color: '#ffffff',
                            padding: '30px 25px'
                        },
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center'
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                            style: {
                                                margin: 0,
                                                fontSize: '32px',
                                                fontWeight: 800,
                                                color: '#ffffff',
                                                letterSpacing: '1px'
                                            },
                                            children: data.title
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/TemplatePreview.tsx",
                                            lineNumber: 127,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            style: {
                                                margin: '8px 0 0 0',
                                                fontSize: '15px',
                                                color: '#d1d5db',
                                                fontWeight: 500
                                            },
                                            children: data.companyName
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/TemplatePreview.tsx",
                                            lineNumber: 130,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/TemplatePreview.tsx",
                                    lineNumber: 126,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    style: {
                                        textAlign: 'right'
                                    },
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        style: {
                                            background: 'linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%)',
                                            padding: '3px',
                                            borderRadius: '8px',
                                            display: 'inline-block',
                                            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
                                        },
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            style: {
                                                backgroundColor: '#1e40af',
                                                color: '#ffffff',
                                                padding: '10px 18px',
                                                borderRadius: '6px',
                                                fontSize: '15px',
                                                fontWeight: 700,
                                                letterSpacing: '0.5px'
                                            },
                                            children: data.quote.number
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/TemplatePreview.tsx",
                                            lineNumber: 142,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                        lineNumber: 135,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/TemplatePreview.tsx",
                                    lineNumber: 134,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/TemplatePreview.tsx",
                            lineNumber: 125,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/TemplatePreview.tsx",
                        lineNumber: 124,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            padding: '25px'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                style: {
                                    display: 'flex',
                                    gap: '4%',
                                    marginBottom: '25px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        style: {
                                            width: '48%',
                                            backgroundColor: '#f9fafb',
                                            padding: '15px',
                                            borderRadius: '8px',
                                            borderLeft: '3px solid #3b82f6'
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                style: {
                                                    margin: '0 0 10px 0',
                                                    color: '#111827',
                                                    fontSize: '14px',
                                                    fontWeight: 600
                                                },
                                                children: language === 'nl' ? 'OPDRACHTGEVER' : 'CLIENT'
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                lineNumber: 170,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                style: {
                                                    fontSize: '13px',
                                                    color: '#4b5563',
                                                    lineHeight: 1.5
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                        style: {
                                                            color: '#111827'
                                                        },
                                                        children: data.client.name
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 174,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 174,
                                                        columnNumber: 83
                                                    }, this),
                                                    data.client.address,
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 175,
                                                        columnNumber: 40
                                                    }, this),
                                                    data.client.city
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                lineNumber: 173,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                        lineNumber: 163,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        style: {
                                            width: '48%',
                                            backgroundColor: '#f9fafb',
                                            padding: '15px',
                                            borderRadius: '8px',
                                            borderLeft: '3px solid #f59e0b'
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                style: {
                                                    margin: '0 0 10px 0',
                                                    color: '#111827',
                                                    fontSize: '14px',
                                                    fontWeight: 600
                                                },
                                                children: language === 'nl' ? 'OFFERTE DETAILS' : 'QUOTE DETAILS'
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                lineNumber: 187,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                style: {
                                                    fontSize: '13px',
                                                    color: '#4b5563',
                                                    lineHeight: 1.5
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                        style: {
                                                            color: '#111827'
                                                        },
                                                        children: language === 'nl' ? 'Nummer:' : 'Number:'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 191,
                                                        columnNumber: 19
                                                    }, this),
                                                    " ",
                                                    data.quote.number,
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 193,
                                                        columnNumber: 48
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                        style: {
                                                            color: '#111827'
                                                        },
                                                        children: language === 'nl' ? 'Datum:' : 'Date:'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 194,
                                                        columnNumber: 19
                                                    }, this),
                                                    " ",
                                                    data.quote.date,
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 196,
                                                        columnNumber: 46
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                        style: {
                                                            color: '#111827'
                                                        },
                                                        children: language === 'nl' ? 'Geldig tot:' : 'Valid until:'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 197,
                                                        columnNumber: 19
                                                    }, this),
                                                    " ",
                                                    data.quote.validUntil
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                lineNumber: 190,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                        lineNumber: 180,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                lineNumber: 162,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                style: {
                                    backgroundColor: '#eff6ff',
                                    borderRadius: '8px',
                                    margin: '25px 0',
                                    borderLeft: '3px solid #3b82f6',
                                    padding: '15px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        style: {
                                            margin: '0 0 8px 0',
                                            fontSize: '14px',
                                            color: '#1e40af',
                                            fontWeight: 600
                                        },
                                        children: data.greeting
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                        lineNumber: 212,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        style: {
                                            margin: 0,
                                            fontSize: '13px',
                                            color: '#1e40af',
                                            lineHeight: 1.6
                                        },
                                        children: data.introduction
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                        lineNumber: 215,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                lineNumber: 205,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                style: {
                                    margin: '30px 0'
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                        style: {
                                            color: '#0c4a6e',
                                            fontSize: '16px',
                                            fontWeight: 700,
                                            margin: '0 0 12px 0',
                                            textAlign: 'center',
                                            textTransform: 'uppercase',
                                            letterSpacing: '0.5px',
                                            borderBottom: '2px solid #0ea5e9',
                                            paddingBottom: '8px'
                                        },
                                        children: language === 'nl' ? 'Kostenoverzicht' : 'Cost Overview'
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                        lineNumber: 222,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                                        style: {
                                            width: '100%',
                                            borderCollapse: 'collapse',
                                            borderRadius: '8px',
                                            overflow: 'hidden',
                                            boxShadow: '0 2px 8px rgba(0,0,0,0.08)'
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                            style: {
                                                                backgroundColor: '#0c4a6e',
                                                                color: '#ffffff',
                                                                padding: '10px 6px',
                                                                textAlign: 'center',
                                                                fontSize: '11px',
                                                                fontWeight: 600,
                                                                letterSpacing: '0.5px',
                                                                textTransform: 'uppercase'
                                                            },
                                                            children: "#"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/TemplatePreview.tsx",
                                                            lineNumber: 246,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                            style: {
                                                                backgroundColor: '#0c4a6e',
                                                                color: '#ffffff',
                                                                padding: '10px 12px',
                                                                textAlign: 'left',
                                                                fontSize: '11px',
                                                                fontWeight: 600,
                                                                letterSpacing: '0.5px',
                                                                textTransform: 'uppercase'
                                                            },
                                                            children: language === 'nl' ? 'Omschrijving' : 'Description'
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/TemplatePreview.tsx",
                                                            lineNumber: 247,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                            style: {
                                                                backgroundColor: '#0c4a6e',
                                                                color: '#ffffff',
                                                                padding: '10px 6px',
                                                                textAlign: 'center',
                                                                fontSize: '11px',
                                                                fontWeight: 600,
                                                                letterSpacing: '0.5px',
                                                                textTransform: 'uppercase'
                                                            },
                                                            children: language === 'nl' ? 'Aantal' : 'Qty'
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/TemplatePreview.tsx",
                                                            lineNumber: 250,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                            style: {
                                                                backgroundColor: '#0c4a6e',
                                                                color: '#ffffff',
                                                                padding: '10px 6px',
                                                                textAlign: 'center',
                                                                fontSize: '11px',
                                                                fontWeight: 600,
                                                                letterSpacing: '0.5px',
                                                                textTransform: 'uppercase'
                                                            },
                                                            children: language === 'nl' ? 'Eenh.' : 'Unit'
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/TemplatePreview.tsx",
                                                            lineNumber: 253,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                            style: {
                                                                backgroundColor: '#0c4a6e',
                                                                color: '#ffffff',
                                                                padding: '10px 10px',
                                                                textAlign: 'right',
                                                                fontSize: '11px',
                                                                fontWeight: 600,
                                                                letterSpacing: '0.5px',
                                                                textTransform: 'uppercase'
                                                            },
                                                            children: language === 'nl' ? 'Prijs' : 'Price'
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/TemplatePreview.tsx",
                                                            lineNumber: 256,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                            style: {
                                                                backgroundColor: '#0c4a6e',
                                                                color: '#ffffff',
                                                                padding: '10px 6px',
                                                                textAlign: 'center',
                                                                fontSize: '11px',
                                                                fontWeight: 600,
                                                                letterSpacing: '0.5px',
                                                                textTransform: 'uppercase'
                                                            },
                                                            children: language === 'nl' ? 'BTW' : 'VAT'
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/TemplatePreview.tsx",
                                                            lineNumber: 259,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                            style: {
                                                                backgroundColor: '#0c4a6e',
                                                                color: '#ffffff',
                                                                padding: '10px 10px',
                                                                textAlign: 'right',
                                                                fontSize: '11px',
                                                                fontWeight: 600,
                                                                letterSpacing: '0.5px',
                                                                textTransform: 'uppercase'
                                                            },
                                                            children: language === 'nl' ? 'Totaal' : 'Total'
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/TemplatePreview.tsx",
                                                            lineNumber: 262,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/TemplatePreview.tsx",
                                                    lineNumber: 245,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                lineNumber: 244,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                                                children: data.items.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                        style: {
                                                            backgroundColor: index % 2 === 0 ? '#ffffff' : '#f0f9ff'
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                style: {
                                                                    padding: '10px 6px',
                                                                    borderBottom: '1px solid #e5e7eb',
                                                                    textAlign: 'center',
                                                                    fontSize: '12px',
                                                                    color: '#6b7280'
                                                                },
                                                                children: item.itemNumber
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                lineNumber: 272,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                style: {
                                                                    padding: '10px 12px',
                                                                    borderBottom: '1px solid #e5e7eb'
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        style: {
                                                                            fontWeight: 600,
                                                                            color: '#0c4a6e',
                                                                            fontSize: '12px'
                                                                        },
                                                                        children: item.description
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                        lineNumber: 276,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        style: {
                                                                            color: '#6b7280',
                                                                            fontSize: '10px'
                                                                        },
                                                                        children: item.subDescription
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                        lineNumber: 279,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                lineNumber: 275,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                style: {
                                                                    padding: '10px 6px',
                                                                    borderBottom: '1px solid #e5e7eb',
                                                                    textAlign: 'center',
                                                                    fontSize: '12px',
                                                                    color: '#4b5563'
                                                                },
                                                                children: item.quantity
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                lineNumber: 283,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                style: {
                                                                    padding: '10px 6px',
                                                                    borderBottom: '1px solid #e5e7eb',
                                                                    textAlign: 'center',
                                                                    fontSize: '12px',
                                                                    color: '#4b5563'
                                                                },
                                                                children: item.unit
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                lineNumber: 286,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                style: {
                                                                    padding: '10px 10px',
                                                                    borderBottom: '1px solid #e5e7eb',
                                                                    textAlign: 'right',
                                                                    fontSize: '12px',
                                                                    color: '#4b5563'
                                                                },
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    style: {
                                                                        fontFamily: 'monospace',
                                                                        whiteSpace: 'nowrap'
                                                                    },
                                                                    children: [
                                                                        "€ ",
                                                                        item.price.toFixed(2)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                    lineNumber: 290,
                                                                    columnNumber: 25
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                lineNumber: 289,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                style: {
                                                                    padding: '10px 6px',
                                                                    borderBottom: '1px solid #e5e7eb',
                                                                    textAlign: 'center',
                                                                    fontSize: '12px',
                                                                    color: '#4b5563'
                                                                },
                                                                children: [
                                                                    item.vatRate,
                                                                    "%"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                lineNumber: 292,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                style: {
                                                                    padding: '10px 10px',
                                                                    borderBottom: '1px solid #e5e7eb',
                                                                    textAlign: 'right',
                                                                    fontWeight: 600,
                                                                    color: '#0c4a6e',
                                                                    fontSize: '12px'
                                                                },
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    style: {
                                                                        fontFamily: 'monospace',
                                                                        whiteSpace: 'nowrap'
                                                                    },
                                                                    children: [
                                                                        "€ ",
                                                                        item.total.toFixed(2)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                    lineNumber: 296,
                                                                    columnNumber: 25
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                lineNumber: 295,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, item.id, true, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 271,
                                                        columnNumber: 21
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                lineNumber: 269,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tfoot", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                        style: {
                                                            backgroundColor: '#f0f9ff'
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                colSpan: 6,
                                                                style: {
                                                                    padding: '10px 12px',
                                                                    textAlign: 'right',
                                                                    fontWeight: 600,
                                                                    fontSize: '12px',
                                                                    color: '#0c4a6e',
                                                                    letterSpacing: '0.3px'
                                                                },
                                                                children: language === 'nl' ? 'Subtotaal (excl. BTW)' : 'Subtotal (excl. VAT)'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                lineNumber: 305,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                style: {
                                                                    padding: '10px 10px',
                                                                    textAlign: 'right',
                                                                    fontWeight: 600,
                                                                    color: '#0c4a6e',
                                                                    fontSize: '12px'
                                                                },
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    style: {
                                                                        fontFamily: 'monospace',
                                                                        whiteSpace: 'nowrap'
                                                                    },
                                                                    children: [
                                                                        "€ ",
                                                                        subtotal.toFixed(2)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                    lineNumber: 309,
                                                                    columnNumber: 23
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                lineNumber: 308,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 304,
                                                        columnNumber: 19
                                                    }, this),
                                                    Object.entries(vatByRate).map(([rate, amount])=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                            style: {
                                                                backgroundColor: '#f0f9ff'
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                    colSpan: 6,
                                                                    style: {
                                                                        padding: '6px 12px',
                                                                        textAlign: 'right',
                                                                        fontSize: '12px',
                                                                        color: '#4b5563'
                                                                    },
                                                                    children: language === 'nl' ? `BTW (${rate}%)` : `VAT (${rate}%)`
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                    lineNumber: 314,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                    style: {
                                                                        padding: '6px 10px',
                                                                        textAlign: 'right',
                                                                        fontWeight: 600,
                                                                        color: '#4b5563',
                                                                        fontSize: '12px'
                                                                    },
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        style: {
                                                                            fontFamily: 'monospace',
                                                                            whiteSpace: 'nowrap'
                                                                        },
                                                                        children: [
                                                                            "€ ",
                                                                            amount.toFixed(2)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                        lineNumber: 318,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                    lineNumber: 317,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, rate, true, {
                                                            fileName: "[project]/src/components/TemplatePreview.tsx",
                                                            lineNumber: 313,
                                                            columnNumber: 21
                                                        }, this)),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                        style: {
                                                            background: 'linear-gradient(90deg, #0ea5e9 0%, #0c4a6e 100%)'
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                colSpan: 6,
                                                                style: {
                                                                    padding: '12px',
                                                                    textAlign: 'right',
                                                                    color: '#ffffff',
                                                                    fontSize: '13px',
                                                                    fontWeight: 700,
                                                                    letterSpacing: '0.5px'
                                                                },
                                                                children: language === 'nl' ? 'TOTAAL INCL. BTW' : 'TOTAL INCL. VAT'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                lineNumber: 323,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                style: {
                                                                    padding: '12px 10px',
                                                                    textAlign: 'right',
                                                                    color: '#ffffff',
                                                                    fontSize: '15px',
                                                                    fontWeight: 700
                                                                },
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    style: {
                                                                        fontFamily: 'monospace',
                                                                        whiteSpace: 'nowrap'
                                                                    },
                                                                    children: [
                                                                        "€ ",
                                                                        total.toFixed(2)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                    lineNumber: 327,
                                                                    columnNumber: 23
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                lineNumber: 326,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 322,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                lineNumber: 303,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                        lineNumber: 236,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                lineNumber: 221,
                                columnNumber: 13
                            }, this),
                            data.notes && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                style: {
                                    backgroundColor: '#fffbeb',
                                    borderRadius: '8px',
                                    marginBottom: '15px',
                                    borderLeft: '3px solid #f59e0b',
                                    padding: '15px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        style: {
                                            margin: '0 0 8px 0',
                                            color: '#92400e',
                                            fontSize: '14px',
                                            fontWeight: 600
                                        },
                                        children: language === 'nl' ? 'OPMERKINGEN' : 'NOTES'
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                        lineNumber: 343,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        style: {
                                            fontSize: '12px',
                                            color: '#92400e',
                                            lineHeight: 1.5,
                                            margin: 0
                                        },
                                        children: replaceDynamicValues(data.notes)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                        lineNumber: 346,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                lineNumber: 336,
                                columnNumber: 15
                            }, this),
                            data.conditions && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                style: {
                                    backgroundColor: '#fef2f2',
                                    borderRadius: '8px',
                                    marginBottom: '15px',
                                    borderLeft: '3px solid #ef4444',
                                    padding: '15px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        style: {
                                            margin: '0 0 8px 0',
                                            color: '#991b1b',
                                            fontSize: '14px',
                                            fontWeight: 600
                                        },
                                        children: language === 'nl' ? 'VOORWAARDEN' : 'CONDITIONS'
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                        lineNumber: 361,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        style: {
                                            fontSize: '12px',
                                            color: '#991b1b',
                                            lineHeight: 1.5,
                                            margin: 0
                                        },
                                        children: replaceDynamicValues(data.conditions)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                        lineNumber: 364,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                lineNumber: 354,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                style: {
                                    backgroundColor: '#f0fff4',
                                    borderRadius: '8px',
                                    margin: '25px 0',
                                    borderLeft: '3px solid #10b981',
                                    padding: '15px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        style: {
                                            margin: '0 0 10px 0',
                                            color: '#065f46',
                                            fontSize: '14px',
                                            fontWeight: 600
                                        },
                                        children: language === 'nl' ? 'VERVOLGSTAPPEN' : 'NEXT STEPS'
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                        lineNumber: 378,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        style: {
                                            display: 'flex',
                                            flexDirection: 'column',
                                            gap: '12px'
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                style: {
                                                    display: 'flex',
                                                    alignItems: 'flex-start'
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        style: {
                                                            backgroundColor: '#10b981',
                                                            color: '#ffffff',
                                                            width: '28px',
                                                            height: '28px',
                                                            borderRadius: '50%',
                                                            textAlign: 'center',
                                                            lineHeight: '28px',
                                                            fontSize: '12px',
                                                            fontWeight: 700,
                                                            marginRight: '15px',
                                                            flexShrink: 0
                                                        },
                                                        children: "1"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 383,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        style: {
                                                            fontSize: '12px',
                                                            color: '#065f46',
                                                            lineHeight: 1.5
                                                        },
                                                        children: replaceDynamicValues(data.nextSteps.step1)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 396,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                lineNumber: 382,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                style: {
                                                    display: 'flex',
                                                    alignItems: 'flex-start'
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        style: {
                                                            backgroundColor: '#10b981',
                                                            color: '#ffffff',
                                                            width: '28px',
                                                            height: '28px',
                                                            borderRadius: '50%',
                                                            textAlign: 'center',
                                                            lineHeight: '28px',
                                                            fontSize: '12px',
                                                            fontWeight: 700,
                                                            marginRight: '15px',
                                                            flexShrink: 0
                                                        },
                                                        children: "2"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 401,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        style: {
                                                            fontSize: '12px',
                                                            color: '#065f46',
                                                            lineHeight: 1.5
                                                        },
                                                        children: replaceDynamicValues(data.nextSteps.step2)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 414,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                lineNumber: 400,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                style: {
                                                    display: 'flex',
                                                    alignItems: 'flex-start'
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        style: {
                                                            backgroundColor: '#10b981',
                                                            color: '#ffffff',
                                                            width: '28px',
                                                            height: '28px',
                                                            borderRadius: '50%',
                                                            textAlign: 'center',
                                                            lineHeight: '28px',
                                                            fontSize: '12px',
                                                            fontWeight: 700,
                                                            marginRight: '15px',
                                                            flexShrink: 0
                                                        },
                                                        children: "3"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 419,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        style: {
                                                            fontSize: '12px',
                                                            color: '#065f46',
                                                            lineHeight: 1.5
                                                        },
                                                        children: replaceDynamicValues(data.nextSteps.step3)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 432,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                lineNumber: 418,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                style: {
                                                    display: 'flex',
                                                    alignItems: 'flex-start'
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        style: {
                                                            backgroundColor: '#10b981',
                                                            color: '#ffffff',
                                                            width: '28px',
                                                            height: '28px',
                                                            borderRadius: '50%',
                                                            textAlign: 'center',
                                                            lineHeight: '28px',
                                                            fontSize: '12px',
                                                            fontWeight: 700,
                                                            marginRight: '15px',
                                                            flexShrink: 0
                                                        },
                                                        children: "4"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 437,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        style: {
                                                            fontSize: '12px',
                                                            color: '#065f46',
                                                            lineHeight: 1.5
                                                        },
                                                        children: replaceDynamicValues(data.nextSteps.step4)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 450,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                lineNumber: 436,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                        lineNumber: 381,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                lineNumber: 371,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                style: {
                                    backgroundColor: '#f0f9ff',
                                    borderRadius: '8px',
                                    margin: '25px 0',
                                    borderLeft: '3px solid #0ea5e9',
                                    padding: '15px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        style: {
                                            margin: '0 0 10px 0',
                                            color: '#0c4a6e',
                                            fontSize: '14px',
                                            fontWeight: 600,
                                            textAlign: 'center'
                                        },
                                        children: [
                                            language === 'nl' ? 'BETAALOPTIES AANBETALING' : 'DEPOSIT PAYMENT OPTIONS',
                                            " (€ ",
                                            depositAmount.toFixed(2),
                                            ")"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                        lineNumber: 465,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        style: {
                                            backgroundColor: '#ffffff',
                                            borderRadius: '6px',
                                            boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                                            display: 'flex'
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                style: {
                                                    width: '30%',
                                                    padding: '12px',
                                                    textAlign: 'center',
                                                    borderRight: '1px solid #f3f4f6'
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                        style: {
                                                            margin: '0 0 5px 0',
                                                            color: '#111827',
                                                            fontSize: '12px',
                                                            fontWeight: 600
                                                        },
                                                        children: language === 'nl' ? 'iDEAL Betaling' : 'iDEAL Payment'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 481,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                                        href: data.payment.paymentLink ? `${data.payment.paymentLink}${data.quote.number}?amount=${depositAmount.toFixed(2)}` : '#',
                                                        style: {
                                                            backgroundColor: '#10b981',
                                                            color: '#ffffff',
                                                            padding: '8px 15px',
                                                            fontSize: '12px',
                                                            fontWeight: 600,
                                                            display: 'inline-block',
                                                            borderRadius: '4px',
                                                            margin: '5px 0',
                                                            textDecoration: 'none'
                                                        },
                                                        children: language === 'nl' ? 'NU BETALEN' : 'PAY NOW'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 484,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        style: {
                                                            margin: '5px 0 0 0',
                                                            fontSize: '10px',
                                                            color: '#6b7280'
                                                        },
                                                        children: language === 'nl' ? 'Snel en veilig online betalen' : 'Fast and secure online payment'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 500,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                lineNumber: 475,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                style: {
                                                    width: '30%',
                                                    padding: '12px',
                                                    textAlign: 'center',
                                                    borderRight: '1px solid #f3f4f6'
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                        style: {
                                                            margin: '0 0 5px 0',
                                                            color: '#111827',
                                                            fontSize: '12px',
                                                            fontWeight: 600
                                                        },
                                                        children: language === 'nl' ? 'QR Code Betaling' : 'QR Code Payment'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 510,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        style: {
                                                            border: '2px solid #10b981',
                                                            borderRadius: '8px',
                                                            padding: '5px',
                                                            backgroundColor: '#ffffff',
                                                            boxShadow: '0 2px 6px rgba(16, 185, 129, 0.2)',
                                                            width: '60px',
                                                            height: '60px',
                                                            margin: '0 auto',
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            justifyContent: 'center'
                                                        },
                                                        children: data.payment.paymentLink ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                            src: generateQRCode(),
                                                            width: "50",
                                                            height: "50",
                                                            alt: "QR Code",
                                                            style: {
                                                                display: 'block'
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/TemplatePreview.tsx",
                                                            lineNumber: 527,
                                                            columnNumber: 23
                                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            style: {
                                                                width: '50px',
                                                                height: '50px',
                                                                backgroundColor: '#f3f4f6',
                                                                display: 'flex',
                                                                alignItems: 'center',
                                                                justifyContent: 'center',
                                                                fontSize: '8px',
                                                                color: '#6b7280'
                                                            },
                                                            children: "QR"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/TemplatePreview.tsx",
                                                            lineNumber: 535,
                                                            columnNumber: 23
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 513,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        style: {
                                                            margin: '5px 0 0 0',
                                                            fontSize: '10px',
                                                            color: '#6b7280'
                                                        },
                                                        children: language === 'nl' ? 'Scan met uw bank app' : 'Scan with your bank app'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 547,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                lineNumber: 504,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                style: {
                                                    width: '40%',
                                                    padding: '12px',
                                                    textAlign: 'center'
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                        style: {
                                                            margin: '0 0 5px 0',
                                                            color: '#111827',
                                                            fontSize: '12px',
                                                            fontWeight: 600
                                                        },
                                                        children: language === 'nl' ? 'Handmatig Overmaken' : 'Manual Transfer'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 552,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        style: {
                                                            fontSize: '11px'
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                style: {
                                                                    display: 'flex',
                                                                    justifyContent: 'space-between',
                                                                    padding: '2px 0'
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        style: {
                                                                            fontWeight: 600,
                                                                            color: '#111827'
                                                                        },
                                                                        children: "IBAN:"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                        lineNumber: 557,
                                                                        columnNumber: 23
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        style: {
                                                                            color: '#4b5563'
                                                                        },
                                                                        children: data.payment.iban
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                        lineNumber: 558,
                                                                        columnNumber: 23
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                lineNumber: 556,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                style: {
                                                                    display: 'flex',
                                                                    justifyContent: 'space-between',
                                                                    padding: '2px 0'
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        style: {
                                                                            fontWeight: 600,
                                                                            color: '#111827'
                                                                        },
                                                                        children: language === 'nl' ? 'T.n.v.:' : 'To:'
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                        lineNumber: 561,
                                                                        columnNumber: 23
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        style: {
                                                                            color: '#4b5563'
                                                                        },
                                                                        children: data.payment.accountName
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                        lineNumber: 564,
                                                                        columnNumber: 23
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                lineNumber: 560,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                style: {
                                                                    display: 'flex',
                                                                    justifyContent: 'space-between',
                                                                    padding: '2px 0'
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        style: {
                                                                            fontWeight: 600,
                                                                            color: '#111827'
                                                                        },
                                                                        children: language === 'nl' ? 'O.v.v.:' : 'Ref:'
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                        lineNumber: 567,
                                                                        columnNumber: 23
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        style: {
                                                                            color: '#3b82f6',
                                                                            fontWeight: 600
                                                                        },
                                                                        children: data.quote.number
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                        lineNumber: 570,
                                                                        columnNumber: 23
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                                lineNumber: 566,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 555,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                lineNumber: 551,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                        lineNumber: 469,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                lineNumber: 458,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                style: {
                                    backgroundColor: '#ecfdf5',
                                    borderRadius: '8px',
                                    margin: '25px 0',
                                    borderLeft: '3px solid #10b981',
                                    padding: '15px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        style: {
                                            margin: '0 0 10px 0',
                                            color: '#065f46',
                                            fontSize: '14px',
                                            fontWeight: 600,
                                            textAlign: 'center'
                                        },
                                        children: data.contactTitle
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                        lineNumber: 585,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        style: {
                                            fontSize: '12px',
                                            color: '#065f46',
                                            lineHeight: 1.5,
                                            margin: '0 0 12px 0',
                                            textAlign: 'center'
                                        },
                                        children: data.contactDescription
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                        lineNumber: 588,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        style: {
                                            display: 'flex',
                                            justifyContent: 'space-around',
                                            textAlign: 'center'
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        style: {
                                                            fontWeight: 600,
                                                            marginBottom: '3px',
                                                            fontSize: '12px',
                                                            color: '#065f46'
                                                        },
                                                        children: language === 'nl' ? 'Telefoon' : 'Phone'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 593,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        style: {
                                                            color: '#065f46',
                                                            fontWeight: 600,
                                                            fontSize: '12px'
                                                        },
                                                        children: data.company.phone
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 596,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                lineNumber: 592,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        style: {
                                                            fontWeight: 600,
                                                            marginBottom: '3px',
                                                            fontSize: '12px',
                                                            color: '#065f46'
                                                        },
                                                        children: "Email"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 601,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        style: {
                                                            color: '#065f46',
                                                            fontWeight: 600,
                                                            fontSize: '12px'
                                                        },
                                                        children: data.company.email
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 604,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                lineNumber: 600,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        style: {
                                                            fontWeight: 600,
                                                            marginBottom: '3px',
                                                            fontSize: '12px',
                                                            color: '#065f46'
                                                        },
                                                        children: "WhatsApp"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 609,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        style: {
                                                            color: '#065f46',
                                                            fontWeight: 600,
                                                            fontSize: '12px'
                                                        },
                                                        children: data.company.phone
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                                        lineNumber: 612,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                                lineNumber: 608,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                        lineNumber: 591,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                lineNumber: 578,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/TemplatePreview.tsx",
                        lineNumber: 159,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            backgroundColor: '#111827',
                            color: '#9ca3af',
                            padding: '20px',
                            textAlign: 'center',
                            fontSize: '12px',
                            lineHeight: 1.5
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                style: {
                                    color: '#ffffff',
                                    fontWeight: 600,
                                    marginBottom: '5px',
                                    fontSize: '14px'
                                },
                                children: data.company.name
                            }, void 0, false, {
                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                lineNumber: 622,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    "KvK: ",
                                    data.company.kvk,
                                    " | BTW: ",
                                    data.company.btw,
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                        lineNumber: 626,
                                        columnNumber: 64
                                    }, this),
                                    data.company.address,
                                    ", ",
                                    data.company.city,
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                        lineNumber: 627,
                                        columnNumber: 58
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        style: {
                                            color: '#60a5fa',
                                            fontWeight: 600
                                        },
                                        children: data.company.website
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/TemplatePreview.tsx",
                                        lineNumber: 628,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/TemplatePreview.tsx",
                                lineNumber: 625,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/TemplatePreview.tsx",
                        lineNumber: 621,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/TemplatePreview.tsx",
                lineNumber: 114,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/TemplatePreview.tsx",
            lineNumber: 113,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/TemplatePreview.tsx",
        lineNumber: 111,
        columnNumber: 5
    }, this);
}
}),
"[project]/src/components/CodeGenerator.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "CodeGenerator": ()=>CodeGenerator
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$copy$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Copy$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/copy.js [app-ssr] (ecmascript) <export default as Copy>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/download.js [app-ssr] (ecmascript) <export default as Download>");
'use client';
;
;
;
function CodeGenerator({ data, language }) {
    const [copied, setCopied] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Calculate totals - items.total should be excluding VAT
    const subtotal = data.items.reduce((sum, item)=>sum + item.total, 0);
    // Group VAT by rate
    const vatByRate = data.items.reduce((acc, item)=>{
        const vatAmount = item.total * item.vatRate / 100;
        if (!acc[item.vatRate]) {
            acc[item.vatRate] = 0;
        }
        acc[item.vatRate] += vatAmount;
        return acc;
    }, {});
    const totalVatAmount = Object.values(vatByRate).reduce((sum, vat)=>sum + vat, 0);
    const total = subtotal + totalVatAmount;
    const depositAmount = total * (data.payment.depositPercentage / 100);
    const remainingAmount = total - depositAmount;
    const cashDiscountAmount = total * (data.payment.cashDiscountPercentage / 100);
    const totalWithCashDiscount = total - cashDiscountAmount;
    // Replace dynamic values in text
    const replaceDynamicValues = (text)=>{
        let result = text;
        // OPMERKINGEN: Replace cash discount calculations
        if (text.includes('contante betaling') || text.includes('cash payment')) {
            // Replace specific patterns step by step
            // 1. Replace total amount after "totaalbedrag van"
            result = result.replace(/(totaalbedrag van\s+)€\s*[\d.,]+/gi, `$1€ ${total.toFixed(2)}`);
            result = result.replace(/(total amount of\s+)€\s*[\d.,]+/gi, `$1€ ${total.toFixed(2)}`);
            // 2. Replace savings amount after "besparing"
            result = result.replace(/(besparing\s+)€\s*[\d.,]+/gi, `$1€ ${cashDiscountAmount.toFixed(2)}`);
            result = result.replace(/(saving\s+)€\s*[\d.,]+/gi, `$1€ ${cashDiscountAmount.toFixed(2)}`);
            // 3. Replace placeholder text
            result = result.replace(/hier moet %?5 van bedraag van offerte incl btw hier plaatsen €/gi, `€ ${cashDiscountAmount.toFixed(2)}`);
            result = result.replace(/here must %?5 of quote amount incl vat here place €/gi, `€ ${cashDiscountAmount.toFixed(2)}`);
        }
        // VOORWAARDEN: Replace deposit and hourly rate
        if (text.includes('aanbetaling van') || text.includes('deposit of')) {
            // Replace deposit amount after "aanbetaling van"
            result = result.replace(/(aanbetaling van\s+\d+%\s+\()€\s*[\d.,]+/gi, `$1€ ${depositAmount.toFixed(2)}`);
            result = result.replace(/(deposit of\s+\d+%\s+\()€\s*[\d.,]+/gi, `$1€ ${depositAmount.toFixed(2)}`);
            // Replace hourly rate after "tegen" or "at"
            result = result.replace(/(tegen\s+)€\s*[\d.,]+/gi, `$1€ ${data.payment.hourlyRate.toFixed(2)}`);
            result = result.replace(/(at\s+)€\s*[\d.,]+/gi, `$1€ ${data.payment.hourlyRate.toFixed(2)}`);
            // Add "excl. BTW" after hourly rate if not present
            if (!result.includes('excl. BTW') && !result.includes('excl. VAT')) {
                result = result.replace(/(€\s*[\d.,]+\/uur)/, '$1 excl. BTW');
            }
        }
        // VERVOLGSTAPPEN: Replace deposit and remaining amounts
        if (text.includes('Betaal') || text.includes('Pay') || text.includes('Restbetaling')) {
            result = result.replace(/€\s*[\d.,]+/g, (match, offset)=>{
                const beforeMatch = text.substring(0, offset);
                // Deposit payment (30% of total incl. BTW)
                if (beforeMatch.includes('Betaal') || beforeMatch.includes('Pay')) {
                    return `€ ${depositAmount.toFixed(2)}`;
                }
                // Remaining payment (70% of total incl. BTW)
                if (beforeMatch.includes('Restbetaling') || beforeMatch.includes('Final payment')) {
                    return `€ ${remainingAmount.toFixed(2)}`;
                }
                return match;
            });
        }
        // Replace percentage values dynamically
        result = result.replace(/(\d+)%/g, (match, percentage)=>{
            if (text.includes('aanbetaling') || text.includes('deposit')) {
                return `${data.payment.depositPercentage}%`;
            }
            if (text.includes('korting') || text.includes('discount')) {
                return `${data.payment.cashDiscountPercentage}%`;
            }
            return match;
        });
        // Replace quote numbers everywhere
        const oldQuotePattern = /OFF-2025-\d+|QUO-2025-\d+/g;
        result = result.replace(oldQuotePattern, data.quote.number);
        return result;
    };
    const generateHtmlCode = ()=>{
        return `<!DOCTYPE html>
<html lang="${language}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="color-scheme" content="light">
    <meta name="supported-color-schemes" content="light">
    <meta name="format-detection" content="telephone=yes">
    <meta name="x-apple-disable-message-reformatting">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="msapplication-TileColor" content="#0ea5e9">
    <meta name="theme-color" content="#0ea5e9">
    <title>${data.title} ${data.quote.number} | ${data.companyName}</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <!--[if !mso]><!------>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" type="text/css">
    <!--<![endif]-->
</head>
<body style="margin: 0; padding: 0; font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; background-color: #f3f4f6; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;">
    <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #f3f4f6;">
        <tr>
            <td align="center" style="padding: 25px 10px;">
                <table border="0" cellpadding="0" cellspacing="0" width="580" style="max-width: 580px; background-color: #ffffff; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 15px rgba(0,0,0,0.08);">
                    
                    <!-- Modern Header -->
                    <tr>
                        <td style="background-color: #111827; color: #ffffff; padding: 30px 25px;">
                            <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <td style="vertical-align: middle;">
                                        <h1 style="margin: 0; font-size: 32px; font-weight: 800; color: #ffffff; letter-spacing: 1px;">${data.title}</h1>
                                        <p style="margin: 8px 0 0 0; font-size: 15px; color: #d1d5db; font-weight: 500;">${data.companyName}</p>
                                    </td>
                                    <td style="text-align: right; vertical-align: middle;">
                                        <div style="background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); padding: 3px; border-radius: 8px; display: inline-block; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                                            <div style="background-color: #1e40af; color: #ffffff; padding: 10px 18px; border-radius: 6px; font-size: 15px; font-weight: 700; letter-spacing: 0.5px; text-align: center;">
                                                ${data.quote.number}
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    
                    <!-- Content -->
                    <tr>
                        <td style="padding: 25px;">
                            
                            <!-- Client & Quote Info -->
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" style="margin-bottom: 25px;">
                                <tr>
                                    <td width="48%" style="background-color: #f9fafb; padding: 15px; border-radius: 8px; vertical-align: top; border-left: 3px solid #3b82f6;">
                                        <h3 style="margin: 0 0 10px 0; color: #111827; font-size: 14px; font-weight: 600;">${language === 'nl' ? 'OPDRACHTGEVER' : 'CLIENT'}</h3>
                                        <p style="margin: 0; font-size: 13px; color: #4b5563; line-height: 1.5;">
                                            <strong style="color: #111827;">${data.client.name}</strong><br>
                                            ${data.client.address}<br>
                                            ${data.client.city}
                                        </p>
                                    </td>
                                    <td width="4%"></td>
                                    <td width="48%" style="background-color: #f9fafb; padding: 15px; border-radius: 8px; vertical-align: top; border-left: 3px solid #f59e0b;">
                                        <h3 style="margin: 0 0 10px 0; color: #111827; font-size: 14px; font-weight: 600;">${language === 'nl' ? 'OFFERTE DETAILS' : 'QUOTE DETAILS'}</h3>
                                        <p style="margin: 0; font-size: 13px; color: #4b5563; line-height: 1.5;">
                                            <strong style="color: #111827;">${language === 'nl' ? 'Nummer:' : 'Number:'}</strong> ${data.quote.number}<br>
                                            <strong style="color: #111827;">${language === 'nl' ? 'Datum:' : 'Date:'}</strong> ${data.quote.date}<br>
                                            <strong style="color: #111827;">${language === 'nl' ? 'Geldig tot:' : 'Valid until:'}</strong> ${data.quote.validUntil}
                                        </p>
                                    </td>
                                </tr>
                            </table>

                            <!-- Introduction -->
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #eff6ff; border-radius: 8px; margin: 25px 0; border-left: 3px solid #3b82f6;">
                                <tr>
                                    <td style="padding: 15px;">
                                        <p style="margin: 0 0 8px 0; font-size: 14px; color: #1e40af; font-weight: 600;">${data.greeting}</p>
                                        <p style="margin: 0; font-size: 13px; color: #1e40af; line-height: 1.6;">
                                            ${data.introduction}
                                        </p>
                                    </td>
                                </tr>
                            </table>

                            <!-- Cost Breakdown -->
                            <div style="margin: 30px 0;">
                                <h2 style="color: #0c4a6e; font-size: 16px; font-weight: 700; margin: 0 0 12px 0; text-align: center; text-transform: uppercase; letter-spacing: 0.5px; border-bottom: 2px solid #0ea5e9; padding-bottom: 8px;">
                                    ${language === 'nl' ? 'Kostenoverzicht' : 'Cost Overview'}
                                </h2>
                                
                                <table border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.08);">
                                    <!-- Header -->
                                    <thead>
                                        <tr>
                                            <th style="background-color: #0c4a6e; color: #ffffff; padding: 10px 6px; text-align: center; font-size: 11px; font-weight: 600; letter-spacing: 0.5px; text-transform: uppercase;">#</th>
                                            <th style="background-color: #0c4a6e; color: #ffffff; padding: 10px 12px; text-align: left; font-size: 11px; font-weight: 600; letter-spacing: 0.5px; text-transform: uppercase;">${language === 'nl' ? 'Omschrijving' : 'Description'}</th>
                                            <th style="background-color: #0c4a6e; color: #ffffff; padding: 10px 6px; text-align: center; font-size: 11px; font-weight: 600; letter-spacing: 0.5px; text-transform: uppercase;">${language === 'nl' ? 'Aantal' : 'Qty'}</th>
                                            <th style="background-color: #0c4a6e; color: #ffffff; padding: 10px 6px; text-align: center; font-size: 11px; font-weight: 600; letter-spacing: 0.5px; text-transform: uppercase;">${language === 'nl' ? 'Eenh.' : 'Unit'}</th>
                                            <th style="background-color: #0c4a6e; color: #ffffff; padding: 10px 10px; text-align: right; font-size: 11px; font-weight: 600; letter-spacing: 0.5px; text-transform: uppercase;">${language === 'nl' ? 'Prijs' : 'Price'}</th>
                                            <th style="background-color: #0c4a6e; color: #ffffff; padding: 10px 6px; text-align: center; font-size: 11px; font-weight: 600; letter-spacing: 0.5px; text-transform: uppercase;">${language === 'nl' ? 'BTW' : 'VAT'}</th>
                                            <th style="background-color: #0c4a6e; color: #ffffff; padding: 10px 10px; text-align: right; font-size: 11px; font-weight: 600; letter-spacing: 0.5px; text-transform: uppercase;">${language === 'nl' ? 'Totaal' : 'Total'}</th>
                                        </tr>
                                    </thead>
                                    <!-- Items -->
                                    <tbody>
${data.items.map((item, index)=>`                                        <tr style="background-color: ${index % 2 === 0 ? '#ffffff' : '#f0f9ff'};">
                                            <td style="padding: 10px 6px; border-bottom: 1px solid #e5e7eb; text-align: center; font-size: 12px; color: #6b7280; vertical-align: middle;">${item.itemNumber}</td>
                                            <td style="padding: 10px 12px; border-bottom: 1px solid #e5e7eb; vertical-align: middle;">
                                                <div style="font-weight: 600; color: #0c4a6e; font-size: 12px;">${item.description}</div>
                                                <div style="color: #6b7280; font-size: 10px;">${item.subDescription}</div>
                                            </td>
                                            <td style="padding: 10px 6px; border-bottom: 1px solid #e5e7eb; text-align: center; font-size: 12px; color: #4b5563; vertical-align: middle;">${item.quantity}</td>
                                            <td style="padding: 10px 6px; border-bottom: 1px solid #e5e7eb; text-align: center; font-size: 12px; color: #4b5563; vertical-align: middle;">${item.unit}</td>
                                            <td style="padding: 10px 10px; border-bottom: 1px solid #e5e7eb; text-align: right; font-size: 12px; color: #4b5563; vertical-align: middle;"><span style="font-family: monospace; white-space: nowrap;">€&nbsp;${item.price.toFixed(2)}</span></td>
                                            <td style="padding: 10px 6px; border-bottom: 1px solid #e5e7eb; text-align: center; font-size: 12px; color: #4b5563; vertical-align: middle;">${item.vatRate}%</td>
                                            <td style="padding: 10px 10px; border-bottom: 1px solid #e5e7eb; text-align: right; font-weight: 600; color: #0c4a6e; font-size: 12px; vertical-align: middle;"><span style="font-family: monospace; white-space: nowrap;">€&nbsp;${item.total.toFixed(2)}</span></td>
                                        </tr>`).join('\n')}
                                    </tbody>
                                    <!-- Totals -->
                                    <tfoot>
                                        <tr style="background-color: #f0f9ff;">
                                            <td colspan="6" style="padding: 10px 12px; text-align: right; font-weight: 600; font-size: 12px; color: #0c4a6e; letter-spacing: 0.3px;">${language === 'nl' ? 'Subtotaal (excl. BTW)' : 'Subtotal (excl. VAT)'}</td>
                                            <td style="padding: 10px 10px; text-align: right; font-weight: 600; color: #0c4a6e; font-size: 12px;"><span style="font-family: monospace; white-space: nowrap;">€&nbsp;${subtotal.toFixed(2)}</span></td>
                                        </tr>
${Object.entries(vatByRate).map(([rate, amount])=>`                                        <tr style="background-color: #f0f9ff;">
                                            <td colspan="6" style="padding: 6px 12px; text-align: right; font-size: 12px; color: #4b5563;">${language === 'nl' ? `BTW (${rate}%)` : `VAT (${rate}%)`}</td>
                                            <td style="padding: 6px 10px; text-align: right; font-weight: 600; color: #4b5563; font-size: 12px;"><span style="font-family: monospace; white-space: nowrap;">€&nbsp;${amount.toFixed(2)}</span></td>
                                        </tr>`).join('\n')}
                                        <tr style="background: linear-gradient(90deg, #0ea5e9 0%, #0c4a6e 100%);">
                                            <td colspan="6" style="padding: 12px; text-align: right; color: #ffffff; font-size: 13px; font-weight: 700; letter-spacing: 0.5px;">${language === 'nl' ? 'TOTAAL INCL. BTW' : 'TOTAL INCL. VAT'}</td>
                                            <td style="padding: 12px 10px; text-align: right; color: #ffffff; font-size: 15px; font-weight: 700;"><span style="font-family: monospace; white-space: nowrap;">€&nbsp;${total.toFixed(2)}</span></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>

                            <!-- Compact Info Sections -->
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" style="margin: 25px 0;">
                                <tr>
                                    <td width="100%">
${data.notes ? `                                        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #fffbeb; border-radius: 8px; margin-bottom: 15px; border-left: 3px solid #f59e0b;">
                                            <tr>
                                                <td style="padding: 15px;">
                                                    <h3 style="margin: 0 0 8px 0; color: #92400e; font-size: 14px; font-weight: 600;">${language === 'nl' ? 'OPMERKINGEN' : 'NOTES'}</h3>
                                                    <p style="font-size: 12px; color: #92400e; line-height: 1.5; margin: 0;">
                                                        ${replaceDynamicValues(data.notes)}
                                                    </p>
                                                </td>
                                            </tr>
                                        </table>` : ''}
                                        
${data.conditions ? `                                        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #fef2f2; border-radius: 8px; margin-bottom: 15px; border-left: 3px solid #ef4444;">
                                            <tr>
                                                <td style="padding: 15px;">
                                                    <h3 style="margin: 0 0 8px 0; color: #991b1b; font-size: 14px; font-weight: 600;">${language === 'nl' ? 'VOORWAARDEN' : 'CONDITIONS'}</h3>
                                                    <p style="font-size: 12px; color: #991b1b; line-height: 1.5; margin: 0;">
                                                        ${replaceDynamicValues(data.conditions)}
                                                    </p>
                                                </td>
                                            </tr>
                                        </table>` : ''}
                                    </td>
                                </tr>
                            </table>

                            <!-- Steps -->
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #f0fff4; border-radius: 8px; margin: 25px 0; border-left: 3px solid #10b981;">
                                <tr>
                                    <td style="padding: 15px;">
                                        <h3 style="margin: 0 0 10px 0; color: #065f46; font-size: 14px; font-weight: 600;">${language === 'nl' ? 'VERVOLGSTAPPEN' : 'NEXT STEPS'}</h3>
                                        <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                            <tr>
                                                <td width="40" style="vertical-align: top; padding-right: 15px;">
                                                    <div style="background-color: #10b981; color: #ffffff; width: 28px; height: 28px; border-radius: 50%; text-align: center; line-height: 28px; font-size: 12px; font-weight: 700;">1</div>
                                                </td>
                                                <td style="vertical-align: top; padding-bottom: 12px;">
                                                    <div style="font-size: 12px; color: #065f46; line-height: 1.5;">
                                                        ${replaceDynamicValues(data.nextSteps.step1)}
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td width="40" style="vertical-align: top; padding-right: 15px;">
                                                    <div style="background-color: #10b981; color: #ffffff; width: 28px; height: 28px; border-radius: 50%; text-align: center; line-height: 28px; font-size: 12px; font-weight: 700;">2</div>
                                                </td>
                                                <td style="vertical-align: top; padding-bottom: 12px;">
                                                    <div style="font-size: 12px; color: #065f46; line-height: 1.5;">
                                                        ${replaceDynamicValues(data.nextSteps.step2)}
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td width="40" style="vertical-align: top; padding-right: 15px;">
                                                    <div style="background-color: #10b981; color: #ffffff; width: 28px; height: 28px; border-radius: 50%; text-align: center; line-height: 28px; font-size: 12px; font-weight: 700;">3</div>
                                                </td>
                                                <td style="vertical-align: top; padding-bottom: 12px;">
                                                    <div style="font-size: 12px; color: #065f46; line-height: 1.5;">
                                                        ${replaceDynamicValues(data.nextSteps.step3)}
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td width="40" style="vertical-align: top; padding-right: 15px;">
                                                    <div style="background-color: #10b981; color: #ffffff; width: 28px; height: 28px; border-radius: 50%; text-align: center; line-height: 28px; font-size: 12px; font-weight: 700;">4</div>
                                                </td>
                                                <td style="vertical-align: top;">
                                                    <div style="font-size: 12px; color: #065f46; line-height: 1.5;">
                                                        ${replaceDynamicValues(data.nextSteps.step4)}
                                                    </div>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>

                            <!-- Payment -->
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #f0f9ff; border-radius: 8px; margin: 25px 0; border-left: 3px solid #0ea5e9;">
                                <tr>
                                    <td style="padding: 15px;">
                                        <h3 style="margin: 0 0 10px 0; color: #0c4a6e; font-size: 14px; font-weight: 600; text-align: center;">${language === 'nl' ? 'BETAALOPTIES AANBETALING' : 'DEPOSIT PAYMENT OPTIONS'} (€ ${depositAmount.toFixed(2)})</h3>

                                        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #ffffff; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                            <tr>
                                                <td width="30%" style="padding: 12px; text-align: center; vertical-align: top; border-right: 1px solid #f3f4f6;">
                                                    <h4 style="margin: 0 0 5px 0; color: #111827; font-size: 12px; font-weight: 600;">${language === 'nl' ? 'iDEAL Betaling' : 'iDEAL Payment'}</h4>
                                                    <a href="${data.payment.paymentLink ? `${data.payment.paymentLink}${data.quote.number}?amount=${depositAmount.toFixed(2)}` : '#'}" style="background-color: #10b981; color: #ffffff; padding: 8px 15px; text-decoration: none; font-size: 12px; font-weight: 600; display: inline-block; border-radius: 4px; margin: 5px 0;">${language === 'nl' ? 'NU BETALEN' : 'PAY NOW'}</a>
                                                    <p style="margin: 5px 0 0 0; font-size: 10px; color: #6b7280;">${language === 'nl' ? 'Snel en veilig online betalen' : 'Fast and secure online payment'}</p>
                                                </td>
                                                <td width="30%" style="padding: 12px; text-align: center; vertical-align: top; border-right: 1px solid #f3f4f6;">
                                                    <h4 style="margin: 0 0 5px 0; color: #111827; font-size: 12px; font-weight: 600;">${language === 'nl' ? 'QR Code Betaling' : 'QR Code Payment'}</h4>
                                                    <div style="border: 2px solid #10b981; border-radius: 8px; padding: 5px; display: table; background: #ffffff; box-shadow: 0 2px 6px rgba(16, 185, 129, 0.2); width: 60px; height: 60px; margin: 0 auto;">
                                                        <div style="display: table-cell; vertical-align: middle; text-align: center;">
                                                            ${data.payment.paymentLink ? `<img src="https://api.qrserver.com/v1/create-qr-code/?size=50x50&data=${encodeURIComponent(`${data.payment.paymentLink}${data.quote.number}?amount=${depositAmount.toFixed(2)}`)}" width="50" height="50" alt="QR Code" style="display: inline-block;">` : `<div style="width: 50px; height: 50px; background-color: #f3f4f6; display: flex; align-items: center; justify-content: center; font-size: 8px; color: #6b7280;">QR</div>`}
                                                        </div>
                                                    </div>
                                                    <p style="margin: 5px 0 0 0; font-size: 10px; color: #6b7280;">${language === 'nl' ? 'Scan met uw bank app' : 'Scan with your bank app'}</p>
                                                </td>
                                                <td width="40%" style="padding: 12px; text-align: center; vertical-align: top;">
                                                    <h4 style="margin: 0 0 5px 0; color: #111827; font-size: 12px; font-weight: 600;">${language === 'nl' ? 'Handmatig Overmaken' : 'Manual Transfer'}</h4>
                                                    <table border="0" cellpadding="0" cellspacing="0" width="100%" style="font-size: 11px;">
                                                        <tr>
                                                            <td width="30%" style="padding: 2px 0; text-align: right; font-weight: 600; color: #111827; padding-right: 5px;">IBAN:</td>
                                                            <td width="70%" style="padding: 2px 0; text-align: left; color: #4b5563;">${data.payment.iban}</td>
                                                        </tr>
                                                        <tr>
                                                            <td width="30%" style="padding: 2px 0; text-align: right; font-weight: 600; color: #111827; padding-right: 5px;">${language === 'nl' ? 'T.n.v.:' : 'To:'}</td>
                                                            <td width="70%" style="padding: 2px 0; text-align: left; color: #4b5563;">${data.payment.accountName}</td>
                                                        </tr>
                                                        <tr>
                                                            <td width="30%" style="padding: 2px 0; text-align: right; font-weight: 600; color: #111827; padding-right: 5px;">${language === 'nl' ? 'O.v.v.:' : 'Ref:'}</td>
                                                            <td width="70%" style="padding: 2px 0; text-align: left; color: #3b82f6; font-weight: 600;">${data.quote.number}</td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>

                            <!-- Contact -->
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #ecfdf5; border-radius: 8px; margin: 25px 0; border-left: 3px solid #10b981;">
                                <tr>
                                    <td style="padding: 15px;">
                                        <h3 style="margin: 0 0 10px 0; color: #065f46; font-size: 14px; font-weight: 600; text-align: center;">${data.contactTitle}</h3>
                                        <p style="font-size: 12px; color: #065f46; line-height: 1.5; margin: 0 0 12px 0; text-align: center;">
                                            ${data.contactDescription}
                                        </p>
                                        <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                            <tr>
                                                <td width="33%" style="text-align: center; vertical-align: top;">
                                                    <div style="font-weight: 600; margin-bottom: 3px; font-size: 12px; color: #065f46;">${language === 'nl' ? 'Telefoon' : 'Phone'}</div>
                                                    <div><a href="tel:${data.company.phone.replace(/[^0-9+]/g, '')}" style="color: #065f46; text-decoration: none; font-weight: 600; font-size: 12px;">${data.company.phone}</a></div>
                                                </td>
                                                <td width="33%" style="text-align: center; vertical-align: top;">
                                                    <div style="font-weight: 600; margin-bottom: 3px; font-size: 12px; color: #065f46;">Email</div>
                                                    <div><a href="mailto:${data.company.email}?subject=${language === 'nl' ? 'Vraag over offerte' : 'Question about quote'} ${data.quote.number}" style="color: #065f46; text-decoration: none; font-weight: 600; font-size: 12px;">${data.company.email}</a></div>
                                                </td>
                                                <td width="34%" style="text-align: center; vertical-align: top;">
                                                    <div style="font-weight: 600; margin-bottom: 3px; font-size: 12px; color: #065f46;">WhatsApp</div>
                                                    <div><a href="https://wa.me/${data.company.phone.replace(/[^0-9]/g, '')}?text=${language === 'nl' ? 'Hallo, ik heb een vraag over offerte' : 'Hello, I have a question about quote'} ${data.quote.number}" style="color: #065f46; text-decoration: none; font-weight: 600; font-size: 12px;">${data.company.phone}</a></div>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>

                    <!-- Footer -->
                    <tr>
                        <td style="background-color: #111827; color: #9ca3af; padding: 20px; text-align: center; font-size: 12px; line-height: 1.5;">
                            <div style="color: #ffffff; font-weight: 600; margin-bottom: 5px; font-size: 14px;">${data.company.name}</div>
                            <div>
                                KvK: ${data.company.kvk} | BTW: ${data.company.btw}<br>
                                ${data.company.address}, ${data.company.city}<br>
                                <a href="http://${data.company.website}" style="color: #60a5fa; text-decoration: none; font-weight: 600;">${data.company.website}</a>
                            </div>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>`;
    };
    const copyToClipboard = async ()=>{
        const htmlCode = generateHtmlCode();
        try {
            await navigator.clipboard.writeText(htmlCode);
            setCopied(true);
            setTimeout(()=>setCopied(false), 2000);
        } catch (err) {
            console.error('Failed to copy: ', err);
        }
    };
    const downloadHtml = ()=>{
        const htmlCode = generateHtmlCode();
        const blob = new Blob([
            htmlCode
        ], {
            type: 'text/html'
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${data.quote.number}-template.html`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-4",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-between items-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-semibold text-gray-900",
                        children: language === 'nl' ? 'Gegenereerde HTML Code' : 'Generated HTML Code'
                    }, void 0, false, {
                        fileName: "[project]/src/components/CodeGenerator.tsx",
                        lineNumber: 457,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex space-x-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: copyToClipboard,
                                className: `inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${copied ? 'bg-green-50 border-green-300 text-green-700' : ''}`,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$copy$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Copy$3e$__["Copy"], {
                                        className: "h-4 w-4 mr-2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/CodeGenerator.tsx",
                                        lineNumber: 467,
                                        columnNumber: 13
                                    }, this),
                                    copied ? language === 'nl' ? 'Gekopieerd!' : 'Copied!' : language === 'nl' ? 'Kopiëren' : 'Copy'
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/CodeGenerator.tsx",
                                lineNumber: 461,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: downloadHtml,
                                className: "inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__["Download"], {
                                        className: "h-4 w-4 mr-2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/CodeGenerator.tsx",
                                        lineNumber: 478,
                                        columnNumber: 13
                                    }, this),
                                    language === 'nl' ? 'Download' : 'Download'
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/CodeGenerator.tsx",
                                lineNumber: 474,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/CodeGenerator.tsx",
                        lineNumber: 460,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/CodeGenerator.tsx",
                lineNumber: 456,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-gray-900 rounded-lg p-4 max-h-96 overflow-auto",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("pre", {
                    className: "text-green-400 text-xs font-mono whitespace-pre-wrap",
                    children: generateHtmlCode()
                }, void 0, false, {
                    fileName: "[project]/src/components/CodeGenerator.tsx",
                    lineNumber: 485,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/CodeGenerator.tsx",
                lineNumber: 484,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-sm text-gray-600",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    children: language === 'nl' ? 'Deze HTML code is geoptimaliseerd voor email clients en kan direct gebruikt worden in uw email templates.' : 'This HTML code is optimized for email clients and can be used directly in your email templates.'
                }, void 0, false, {
                    fileName: "[project]/src/components/CodeGenerator.tsx",
                    lineNumber: 491,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/CodeGenerator.tsx",
                lineNumber: 490,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/CodeGenerator.tsx",
        lineNumber: 455,
        columnNumber: 5
    }, this);
}
}),
"[project]/src/components/TemplateEditor.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "TemplateEditor": ()=>TemplateEditor
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$languages$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Languages$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/languages.js [app-ssr] (ecmascript) <export default as Languages>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$copy$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Copy$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/copy.js [app-ssr] (ecmascript) <export default as Copy>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Eye$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/eye.js [app-ssr] (ecmascript) <export default as Eye>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$defaultTemplate$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/defaultTemplate.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$FormEditor$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/FormEditor.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$TemplatePreview$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/TemplatePreview.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$CodeGenerator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/CodeGenerator.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
function TemplateEditor() {
    const [language, setLanguage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('nl');
    const [templateData, setTemplateData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(()=>{
        // Ensure all fields have values
        return {
            ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$defaultTemplate$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["defaultTemplateData"][language]
        };
    });
    const [activeTab, setActiveTab] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('preview');
    const handleLanguageChange = (newLanguage)=>{
        setLanguage(newLanguage);
    // Don't overwrite existing data, just change language
    };
    const handleDataChange = (newData)=>{
        // Ensure all fields have proper values and force re-render
        const cleanedData = {
            ...newData,
            // Ensure strings are never null/undefined
            title: newData.title || '',
            companyName: newData.companyName || '',
            greeting: newData.greeting || '',
            introduction: newData.introduction || '',
            notes: newData.notes || '',
            conditions: newData.conditions || '',
            contactTitle: newData.contactTitle || '',
            contactDescription: newData.contactDescription || ''
        };
        setTemplateData(cleanedData);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gray-50",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                className: "bg-white shadow-sm border-b",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex justify-between items-center h-16",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                        className: "text-2xl font-bold text-gray-900",
                                        children: language === 'nl' ? 'Offerte Template Editor' : 'Quote Template Editor'
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/TemplateEditor.tsx",
                                        lineNumber: 50,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm text-gray-600",
                                        children: language === 'nl' ? 'Bewerk uw template en genereer HTML code' : 'Edit your template and generate HTML code'
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/TemplateEditor.tsx",
                                        lineNumber: 53,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/TemplateEditor.tsx",
                                lineNumber: 49,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-4",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center space-x-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$languages$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Languages$3e$__["Languages"], {
                                            className: "h-5 w-5 text-gray-500"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/TemplateEditor.tsx",
                                            lineNumber: 64,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                            value: language,
                                            onChange: (e)=>handleLanguageChange(e.target.value),
                                            className: "border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                    value: "nl",
                                                    children: "Nederlands"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/TemplateEditor.tsx",
                                                    lineNumber: 70,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                    value: "en",
                                                    children: "English"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/TemplateEditor.tsx",
                                                    lineNumber: 71,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/TemplateEditor.tsx",
                                            lineNumber: 65,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/TemplateEditor.tsx",
                                    lineNumber: 63,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/TemplateEditor.tsx",
                                lineNumber: 61,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/TemplateEditor.tsx",
                        lineNumber: 48,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/TemplateEditor.tsx",
                    lineNumber: 47,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/TemplateEditor.tsx",
                lineNumber: 46,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "grid grid-cols-1 lg:grid-cols-2 gap-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "space-y-6",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white rounded-lg shadow-sm border",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "px-6 py-4 border-b border-gray-200",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            className: "text-lg font-semibold text-gray-900",
                                            children: language === 'nl' ? 'Template Bewerken' : 'Edit Template'
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/TemplateEditor.tsx",
                                            lineNumber: 88,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/TemplateEditor.tsx",
                                        lineNumber: 87,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "p-6",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$FormEditor$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormEditor"], {
                                            data: templateData,
                                            language: language,
                                            onChange: handleDataChange
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/TemplateEditor.tsx",
                                            lineNumber: 93,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/TemplateEditor.tsx",
                                        lineNumber: 92,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/TemplateEditor.tsx",
                                lineNumber: 86,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/TemplateEditor.tsx",
                            lineNumber: 85,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "space-y-6",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white rounded-lg shadow-sm border",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "border-b border-gray-200",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                                            className: "-mb-px flex",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: ()=>setActiveTab('preview'),
                                                    className: `py-2 px-4 border-b-2 font-medium text-sm ${activeTab === 'preview' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Eye$3e$__["Eye"], {
                                                            className: "h-4 w-4 inline mr-2"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/TemplateEditor.tsx",
                                                            lineNumber: 116,
                                                            columnNumber: 21
                                                        }, this),
                                                        language === 'nl' ? 'Voorbeeld' : 'Preview'
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/TemplateEditor.tsx",
                                                    lineNumber: 108,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: ()=>setActiveTab('code'),
                                                    className: `py-2 px-4 border-b-2 font-medium text-sm ${activeTab === 'code' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$copy$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Copy$3e$__["Copy"], {
                                                            className: "h-4 w-4 inline mr-2"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/TemplateEditor.tsx",
                                                            lineNumber: 127,
                                                            columnNumber: 21
                                                        }, this),
                                                        language === 'nl' ? 'HTML Code' : 'HTML Code'
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/TemplateEditor.tsx",
                                                    lineNumber: 119,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/TemplateEditor.tsx",
                                            lineNumber: 107,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/TemplateEditor.tsx",
                                        lineNumber: 106,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "p-6",
                                        children: activeTab === 'preview' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$TemplatePreview$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TemplatePreview"], {
                                            data: templateData,
                                            language: language
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/TemplateEditor.tsx",
                                            lineNumber: 135,
                                            columnNumber: 19
                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$CodeGenerator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CodeGenerator"], {
                                            data: templateData,
                                            language: language
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/TemplateEditor.tsx",
                                            lineNumber: 137,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/TemplateEditor.tsx",
                                        lineNumber: 133,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/TemplateEditor.tsx",
                                lineNumber: 105,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/TemplateEditor.tsx",
                            lineNumber: 103,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/TemplateEditor.tsx",
                    lineNumber: 83,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/TemplateEditor.tsx",
                lineNumber: 82,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/TemplateEditor.tsx",
        lineNumber: 44,
        columnNumber: 5
    }, this);
}
}),
"[project]/src/app/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>Home
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$TemplateEditor$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/TemplateEditor.tsx [app-ssr] (ecmascript)");
'use client';
;
;
function Home() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gray-50",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$TemplateEditor$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TemplateEditor"], {}, void 0, false, {
            fileName: "[project]/src/app/page.tsx",
            lineNumber: 8,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/page.tsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__05b9e518._.js.map