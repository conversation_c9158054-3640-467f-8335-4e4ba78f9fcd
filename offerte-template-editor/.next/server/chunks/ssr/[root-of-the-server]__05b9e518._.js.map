{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/offerte%20email%20template%20editor%20/offerte-template-editor/src/data/defaultTemplate.ts"], "sourcesContent": ["import { Translations } from '@/types/template';\n\nexport const defaultTemplateData: Translations = {\n  nl: {\n    title: \"OFFERTE\",\n    companyName: \"A.S.Allround klussen\",\n    \n    client: {\n      name: \"Holtara B.V.\",\n      address: \"Vierwindenstraat 149\",\n      city: \"1013 LA Amsterdam\"\n    },\n    \n    quote: {\n      number: \"OFF-2025-0013\",\n      date: \"14 juli 2025\",\n      validUntil: \"13 augustus 2025\"\n    },\n    \n    greeting: \"Geachte Holtara B.V.,\",\n    introduction: \"<PERSON><PERSON><PERSON><PERSON> ontvangt u onze offerte voor de professionele installatie van uw nieuwe vaatwasser inclusief demontage van het oude apparaat. Alle werkzaamheden worden uitgevoerd door ervaren monteurs met volledige garantie.\",\n    \n    items: [\n      {\n        id: \"1\",\n        itemNumber: 1,\n        description: \"Installatie nieuwe vaatwasser\",\n        subDescription: \"Professionele installatie inclusief aansluiting\",\n        quantity: 2,\n        unit: \"uur\",\n        price: 75.00,\n        vatRate: 21,\n        total: 150.00\n      },\n      {\n        id: \"2\",\n        itemNumber: 2,\n        description: \"Aan- en afvoer aansluiten\",\n        subDescription: \"Watertoevoer en afvoer op bestaande leidingen\",\n        quantity: 1,\n        unit: \"stuk\",\n        price: 99.00,\n        vatRate: 21,\n        total: 99.00\n      },\n      {\n        id: \"3\",\n        itemNumber: 3,\n        description: \"Demontage & afvoer oude vaatwasser\",\n        subDescription: \"Vakkundige demontage en milieuvriendelijke afvoer\",\n        quantity: 1,\n        unit: \"stuk\",\n        price: 75.00,\n        vatRate: 21,\n        total: 75.00\n      },\n      {\n        id: \"4\",\n        itemNumber: 4,\n        description: \"Transport & levering\",\n        subDescription: \"Ophalen en leveren vaatwasser op locatie\",\n        quantity: 1,\n        unit: \"stuk\",\n        price: 80.00,\n        vatRate: 21,\n        total: 80.00\n      },\n      {\n        id: \"5\",\n        itemNumber: 5,\n        description: \"Bosch SBV4ECX Vaatwasser\",\n        subDescription: \"A+ energielabel, 12 couverts, 2 jaar fabrieksgarantie\",\n        quantity: 1,\n        unit: \"stuk\",\n        price: 686.00,\n        vatRate: 21,\n        total: 686.00\n      }\n    ],\n    \n    notes: \"Bij contante betaling ontvangt u 5% korting op het totaalbedrag van € 1.199,11 (besparing € 59,96). Alle benodigde materialen zijn inbegrepen in deze offerte. De werkzaamheden worden naar verwachting binnen 2-3 uur afgerond. Na akkoord kunnen we de installatie binnen 2-3 werkdagen inplannen, afhankelijk van uw beschikbaarheid.\",\n\n    conditions: \"Een aanbetaling van 30% (€ 359,73) is vereist voor het reserveren van materialen en het inplannen van de werkzaamheden. Deze offerte geldt alleen voor de vermelde werkzaamheden. Eventueel meerwerk wordt berekend tegen € 75,00/uur excl. BTW en alleen uitgevoerd na uw akkoord. Wij bieden 3 maanden garantie op het montagewerk en 2 jaar fabrieksgarantie op de vaatwasser. Kosteloos annuleren is mogelijk tot 7 dagen voor de geplande uitvoering, daarna wordt de aanbetaling ingehouden voor gemaakte kosten.\",\n    \n    nextSteps: {\n      step1: \"Offerte Accorderen - Download bijlage, onderteken en mail <NAME_EMAIL> of stuur een e-mail met: \\\"Ik verklaar hierbij akkoord te gaan met de voorwaarden van offerte OFF-2025-0013.\\\"\",\n      step2: \"Aanbetaling - Betaal € 359,73 voor materiaalreservering en planning\",\n      step3: \"Afspraak Inplannen - Wij nemen z.s.m. contact op voor planning\",\n      step4: \"Restbetaling - € 839,38 direct na afronding via contant, Tikkie, QR-code of factuur (binnen 14 dagen)\"\n    },\n    \n    company: {\n      name: \"A.S.Allround klussen\",\n      kvk: \"********\",\n      btw: \"NL002490282B64\",\n      address: \"Kelspelstraat 3\",\n      city: \"3641 JS Mijdrecht\",\n      website: \"www.ASklussen.nl\",\n      email: \"<EMAIL>\",\n      phone: \"06-********\"\n    },\n\n    payment: {\n      iban: \"NL54 INGB 0006 9210 65\",\n      accountName: \"A.S.Allround klussen\",\n      depositPercentage: 30,\n      hourlyRate: 75.00,\n      cashDiscountPercentage: 5,\n      paymentLink: \"www.asklussen.nl\"\n    },\n\n    contactTitle: \"CONTACT & ONDERSTEUNING\",\n    contactDescription: \"Heeft u vragen over deze offerte of wilt u een aangepaste begroting? Neem gerust contact met ons op via onderstaande kanalen. Wij helpen u graag verder en staan klaar om al uw vragen te beantwoorden.\"\n  },\n  \n  en: {\n    title: \"QUOTE\",\n    companyName: \"A.S.Allround Services\",\n\n    client: {\n      name: \"Holtara B.V.\",\n      address: \"Vierwindenstraat 149\",\n      city: \"1013 LA Amsterdam\"\n    },\n\n    quote: {\n      number: \"QUO-2025-0013\",\n      date: \"July 14, 2025\",\n      validUntil: \"August 13, 2025\"\n    },\n\n    greeting: \"Dear Holtara B.V.,\",\n    introduction: \"Please find our quote for the professional installation of your new dishwasher including removal of the old appliance. All work will be carried out by experienced technicians with full warranty.\",\n    \n    items: [\n      {\n        id: \"1\",\n        itemNumber: 1,\n        description: \"New dishwasher installation\",\n        subDescription: \"Professional installation including connection\",\n        quantity: 2,\n        unit: \"hour\",\n        price: 75.00,\n        vatRate: 21,\n        total: 150.00\n      },\n      {\n        id: \"2\",\n        itemNumber: 2,\n        description: \"Water supply and drainage connection\",\n        subDescription: \"Water supply and drainage to existing pipes\",\n        quantity: 1,\n        unit: \"piece\",\n        price: 99.00,\n        vatRate: 21,\n        total: 99.00\n      },\n      {\n        id: \"3\",\n        itemNumber: 3,\n        description: \"Removal & disposal of old dishwasher\",\n        subDescription: \"Professional removal and environmentally friendly disposal\",\n        quantity: 1,\n        unit: \"piece\",\n        price: 75.00,\n        vatRate: 21,\n        total: 75.00\n      },\n      {\n        id: \"4\",\n        itemNumber: 4,\n        description: \"Transport & delivery\",\n        subDescription: \"Pick up and deliver dishwasher to location\",\n        quantity: 1,\n        unit: \"piece\",\n        price: 80.00,\n        vatRate: 21,\n        total: 80.00\n      },\n      {\n        id: \"5\",\n        itemNumber: 5,\n        description: \"Bosch SBV4ECX Dishwasher\",\n        subDescription: \"A+ energy label, 12 place settings, 2 year manufacturer warranty\",\n        quantity: 1,\n        unit: \"piece\",\n        price: 686.00,\n        vatRate: 21,\n        total: 686.00\n      }\n    ],\n    \n    notes: \"With cash payment you receive 5% discount on the total amount of € 1,199.11 (saving € 59.96). All necessary materials are included in this quote. The work is expected to be completed within 2-3 hours. After approval, we can schedule the installation within 2-3 working days, depending on your availability.\",\n\n    conditions: \"A deposit of 30% (€ 359.73) is required for reserving materials and scheduling the work. This quote applies only to the mentioned work. Any additional work will be charged at € 75.00/hour excl. VAT and only carried out after your approval. We offer 3 months warranty on installation work and 2 years manufacturer warranty on the dishwasher. Free cancellation is possible up to 7 days before scheduled execution, after which the deposit will be retained for costs incurred.\",\n    \n    nextSteps: {\n      step1: \"Approve Quote - Download attachment, sign and email <NAME_EMAIL> or send an email with: \\\"I hereby declare to agree with the conditions of quote QUO-2025-0013.\\\"\",\n      step2: \"Deposit - Pay € 359.73 for material reservation and planning\",\n      step3: \"Schedule Appointment - We will contact you as soon as possible for planning\",\n      step4: \"Final Payment - € 839.38 directly after completion via cash, Tikkie, QR-code or invoice (within 14 days)\"\n    },\n    \n    company: {\n      name: \"A.S.Allround Services\",\n      kvk: \"********\",\n      btw: \"NL002490282B64\",\n      address: \"Kelspelstraat 3\",\n      city: \"3641 JS Mijdrecht\",\n      website: \"www.ASklussen.nl\",\n      email: \"<EMAIL>\",\n      phone: \"06-********\"\n    },\n    \n    payment: {\n      iban: \"NL54 INGB 0006 9210 65\",\n      accountName: \"A.S.Allround Services\",\n      depositPercentage: 30,\n      hourlyRate: 75.00,\n      cashDiscountPercentage: 5,\n      paymentLink: \"www.asklussen.nl\"\n    },\n    \n    contactTitle: \"CONTACT & SUPPORT\",\n    contactDescription: \"Do you have questions about this quote or would you like a customized estimate? Please feel free to contact us through the channels below. We are happy to help you and are ready to answer all your questions.\"\n  }\n};\n"], "names": [], "mappings": ";;;AAEO,MAAM,sBAAoC;IAC/C,IAAI;QACF,OAAO;QACP,aAAa;QAEb,QAAQ;YACN,MAAM;YACN,SAAS;YACT,MAAM;QACR;QAEA,OAAO;YACL,QAAQ;YACR,MAAM;YACN,YAAY;QACd;QAEA,UAAU;QACV,cAAc;QAEd,OAAO;YACL;gBACE,IAAI;gBACJ,YAAY;gBACZ,aAAa;gBACb,gBAAgB;gBAChB,UAAU;gBACV,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,OAAO;YACT;YACA;gBACE,IAAI;gBACJ,YAAY;gBACZ,aAAa;gBACb,gBAAgB;gBAChB,UAAU;gBACV,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,OAAO;YACT;YACA;gBACE,IAAI;gBACJ,YAAY;gBACZ,aAAa;gBACb,gBAAgB;gBAChB,UAAU;gBACV,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,OAAO;YACT;YACA;gBACE,IAAI;gBACJ,YAAY;gBACZ,aAAa;gBACb,gBAAgB;gBAChB,UAAU;gBACV,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,OAAO;YACT;YACA;gBACE,IAAI;gBACJ,YAAY;gBACZ,aAAa;gBACb,gBAAgB;gBAChB,UAAU;gBACV,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,OAAO;YACT;SACD;QAED,OAAO;QAEP,YAAY;QAEZ,WAAW;YACT,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;QACT;QAEA,SAAS;YACP,MAAM;YACN,KAAK;YACL,KAAK;YACL,SAAS;YACT,MAAM;YACN,SAAS;YACT,OAAO;YACP,OAAO;QACT;QAEA,SAAS;YACP,MAAM;YACN,aAAa;YACb,mBAAmB;YACnB,YAAY;YACZ,wBAAwB;YACxB,aAAa;QACf;QAEA,cAAc;QACd,oBAAoB;IACtB;IAEA,IAAI;QACF,OAAO;QACP,aAAa;QAEb,QAAQ;YACN,MAAM;YACN,SAAS;YACT,MAAM;QACR;QAEA,OAAO;YACL,QAAQ;YACR,MAAM;YACN,YAAY;QACd;QAEA,UAAU;QACV,cAAc;QAEd,OAAO;YACL;gBACE,IAAI;gBACJ,YAAY;gBACZ,aAAa;gBACb,gBAAgB;gBAChB,UAAU;gBACV,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,OAAO;YACT;YACA;gBACE,IAAI;gBACJ,YAAY;gBACZ,aAAa;gBACb,gBAAgB;gBAChB,UAAU;gBACV,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,OAAO;YACT;YACA;gBACE,IAAI;gBACJ,YAAY;gBACZ,aAAa;gBACb,gBAAgB;gBAChB,UAAU;gBACV,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,OAAO;YACT;YACA;gBACE,IAAI;gBACJ,YAAY;gBACZ,aAAa;gBACb,gBAAgB;gBAChB,UAAU;gBACV,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,OAAO;YACT;YACA;gBACE,IAAI;gBACJ,YAAY;gBACZ,aAAa;gBACb,gBAAgB;gBAChB,UAAU;gBACV,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,OAAO;YACT;SACD;QAED,OAAO;QAEP,YAAY;QAEZ,WAAW;YACT,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;QACT;QAEA,SAAS;YACP,MAAM;YACN,KAAK;YACL,KAAK;YACL,SAAS;YACT,MAAM;YACN,SAAS;YACT,OAAO;YACP,OAAO;QACT;QAEA,SAAS;YACP,MAAM;YACN,aAAa;YACb,mBAAmB;YACnB,YAAY;YACZ,wBAAwB;YACxB,aAAa;QACf;QAEA,cAAc;QACd,oBAAoB;IACtB;AACF", "debugId": null}}, {"offset": {"line": 224, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/offerte%20email%20template%20editor%20/offerte-template-editor/src/components/FormEditor.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Plus, Trash2, ChevronRight, ChevronLeft } from 'lucide-react';\nimport { TemplateData, QuoteItem, Language } from '@/types/template';\n\ninterface FormEditorProps {\n  data: TemplateData;\n  language: Language;\n  onChange: (data: TemplateData) => void;\n}\n\nconst FORM_STEPS = [\n  { id: 'header', titleNL: 'Header & Basis', titleEN: 'Header & Basic' },\n  { id: 'client', titleNL: 'Klant & Offerte', titleEN: 'Client & Quote' },\n  { id: 'items', titleNL: 'Offerte Items', titleEN: 'Quote Items' },\n  { id: 'notes', titleNL: 'Opmerkingen', titleEN: 'Notes' },\n  { id: 'steps', titleNL: 'Vervolgstappen', titleEN: 'Next Steps' },\n  { id: 'company', titleNL: 'Bedrijfsgegevens', titleEN: 'Company Info' },\n  { id: 'payment', titleNL: 'Betaalgegevens', titleEN: 'Payment Info' }\n];\n\nexport function FormEditor({ data, language, onChange }: FormEditorProps) {\n  const [currentStep, setCurrentStep] = useState(0);\n\n  const nextStep = () => {\n    if (currentStep < FORM_STEPS.length - 1) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n\n  const prevStep = () => {\n    if (currentStep > 0) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n\n  const updateData = (updates: Partial<TemplateData>) => {\n    onChange({ ...data, ...updates });\n  };\n\n  const updateNestedData = <T extends keyof TemplateData>(\n    key: T,\n    updates: Partial<TemplateData[T]>\n  ) => {\n    onChange({\n      ...data,\n      [key]: { ...data[key], ...updates }\n    });\n  };\n\n  const addItem = () => {\n    const newItemNumber = Math.max(...data.items.map(item => item.itemNumber), 0) + 1;\n    const newItem: QuoteItem = {\n      id: Date.now().toString(),\n      itemNumber: newItemNumber,\n      description: language === 'nl' ? 'Nieuwe service' : 'New service',\n      subDescription: language === 'nl' ? 'Beschrijving van de service' : 'Service description',\n      quantity: 1,\n      unit: language === 'nl' ? 'stuk' : 'piece',\n      price: 0,\n      vatRate: 21,\n      total: 0\n    };\n\n    updateData({ items: [...data.items, newItem] });\n  };\n\n  const updateItem = (index: number, updates: Partial<QuoteItem>) => {\n    const updatedItems = data.items.map((item, i) => {\n      if (i === index) {\n        const updatedItem = { ...item, ...updates };\n        // Recalculate total when price, quantity, or VAT changes - total should be EXCLUDING VAT\n        if ('price' in updates || 'quantity' in updates) {\n          updatedItem.total = updatedItem.price * updatedItem.quantity;\n        }\n        return updatedItem;\n      }\n      return item;\n    });\n\n    updateData({ items: updatedItems });\n  };\n\n  const removeItem = (index: number) => {\n    const updatedItems = data.items.filter((_, i) => i !== index);\n    updateData({ items: updatedItems });\n  };\n\n  const currentStepData = FORM_STEPS[currentStep];\n  const stepTitle = language === 'nl' ? currentStepData.titleNL : currentStepData.titleEN;\n\n  // Simple input component that doesn't lose focus\n  const SimpleInput = ({ label, defaultValue, onChange, type = 'text', placeholder = '' }: {\n    label: string;\n    defaultValue: string | number;\n    onChange: (value: string | number) => void;\n    type?: string;\n    placeholder?: string;\n  }) => (\n    <div>\n      <label className=\"block text-xs font-medium text-gray-700 mb-1\">{label}</label>\n      <input\n        type={type}\n        defaultValue={String(defaultValue || '')}\n        onBlur={(e) => {\n          const val = e.target.value;\n          if (type === 'number') {\n            onChange(val === '' ? 0 : parseFloat(val) || 0);\n          } else {\n            onChange(val);\n          }\n        }}\n        placeholder={placeholder}\n        className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n      />\n    </div>\n  );\n\n  const SimpleTextArea = ({ label, defaultValue, onChange, rows = 2 }: {\n    label: string;\n    defaultValue: string;\n    onChange: (value: string) => void;\n    rows?: number;\n  }) => (\n    <div>\n      <label className=\"block text-xs font-medium text-gray-700 mb-1\">{label}</label>\n      <textarea\n        defaultValue={String(defaultValue || '')}\n        onBlur={(e) => onChange(e.target.value)}\n        rows={rows}\n        className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none\"\n      />\n    </div>\n  );\n\n  const SimpleSelect = ({ label, defaultValue, onChange, options }: {\n    label: string;\n    defaultValue: string | number;\n    onChange: (value: string | number) => void;\n    options: { value: string | number; label: string }[];\n  }) => (\n    <div>\n      <label className=\"block text-xs font-medium text-gray-700 mb-1\">{label}</label>\n      <select\n        defaultValue={String(defaultValue || '')}\n        onChange={(e) => {\n          const val = e.target.value;\n          const num = parseFloat(val);\n          onChange(isNaN(num) ? val : num);\n        }}\n        className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n      >\n        {options.map((option) => (\n          <option key={option.value} defaultValue={option.value}>{option.label}</option>\n        ))}\n      </select>\n    </div>\n  );\n\n\n\n  const renderStepContent = () => {\n    switch (currentStepData.id) {\n      case 'header':\n        return (\n          <div className=\"space-y-3\">\n            <SimpleInput\n              label={language === 'nl' ? 'Titel' : 'Title'}\n              defaultValue={data.title}\n              onChange={(value) => updateData({ title: value as string })}\n            />\n            <SimpleInput\n              label={language === 'nl' ? 'Bedrijfsnaam' : 'Company Name'}\n              defaultValue={data.companyName}\n              onChange={(value) => updateData({ companyName: value as string })}\n            />\n          </div>\n        );\n\n      case 'client':\n        return (\n          <div className=\"space-y-3\">\n            <div className=\"grid grid-cols-2 gap-3\">\n              <SimpleInput\n                label={language === 'nl' ? 'Klant Naam' : 'Client Name'}\n                defaultValue={data.client.name}\n                onChange={(value) => updateNestedData('client', { name: value as string })}\n              />\n              <SimpleInput\n                label={language === 'nl' ? 'Offerte Nummer' : 'Quote Number'}\n                defaultValue={data.quote.number}\n                onChange={(value) => updateNestedData('quote', { number: value as string })}\n              />\n            </div>\n            <div className=\"grid grid-cols-2 gap-3\">\n              <SimpleInput\n                label={language === 'nl' ? 'Adres' : 'Address'}\n                defaultValue={data.client.address}\n                onChange={(value) => updateNestedData('client', { address: value as string })}\n              />\n              <SimpleInput\n                label={language === 'nl' ? 'Stad' : 'City'}\n                defaultValue={data.client.city}\n                onChange={(value) => updateNestedData('client', { city: value as string })}\n              />\n            </div>\n            <div className=\"grid grid-cols-2 gap-3\">\n              <SimpleInput\n                label={language === 'nl' ? 'Datum' : 'Date'}\n                defaultValue={data.quote.date}\n                onChange={(value) => updateNestedData('quote', { date: value as string })}\n              />\n              <SimpleInput\n                label={language === 'nl' ? 'Geldig tot' : 'Valid Until'}\n                defaultValue={data.quote.validUntil}\n                onChange={(value) => updateNestedData('quote', { validUntil: value as string })}\n              />\n            </div>\n            <SimpleTextArea\n              label={language === 'nl' ? 'Begroeting' : 'Greeting'}\n              defaultValue={data.greeting}\n              onChange={(value) => updateData({ greeting: value })}\n              rows={1}\n            />\n            <SimpleTextArea\n              label={language === 'nl' ? 'Introductie' : 'Introduction'}\n              defaultValue={data.introduction}\n              onChange={(value) => updateData({ introduction: value })}\n              rows={2}\n            />\n          </div>\n        );\n\n      case 'items':\n        return (\n          <div className=\"space-y-3\">\n            {data.items.map((item, index) => (\n              <div key={item.id} className=\"border border-gray-200 rounded p-3 bg-gray-50\">\n                <div className=\"flex justify-between items-center mb-2\">\n                  <h4 className=\"text-sm font-medium text-gray-900\">\n                    {language === 'nl' ? 'Item' : 'Item'} {item.itemNumber}\n                  </h4>\n                  <button\n                    onClick={() => removeItem(index)}\n                    className=\"text-red-500 hover:text-red-700\"\n                  >\n                    <Trash2 className=\"h-3 w-3\" />\n                  </button>\n                </div>\n\n                <div className=\"grid grid-cols-3 gap-2 mb-2\">\n                  <SimpleInput\n                    label=\"#\"\n                    defaultValue={item.itemNumber}\n                    onChange={(value) => updateItem(index, { itemNumber: value as number })}\n                    type=\"number\"\n                  />\n                  <SimpleInput\n                    label={language === 'nl' ? 'Aantal' : 'Qty'}\n                    defaultValue={item.quantity}\n                    onChange={(value) => updateItem(index, { quantity: value as number })}\n                    type=\"number\"\n                  />\n                  <SimpleInput\n                    label={language === 'nl' ? 'Eenheid' : 'Unit'}\n                    defaultValue={item.unit}\n                    onChange={(value) => updateItem(index, { unit: value as string })}\n                  />\n                </div>\n\n                <div className=\"grid grid-cols-1 gap-2 mb-2\">\n                  <SimpleInput\n                    label={language === 'nl' ? 'Beschrijving' : 'Description'}\n                    defaultValue={item.description}\n                    onChange={(value) => updateItem(index, { description: value as string })}\n                  />\n                  <SimpleInput\n                    label={language === 'nl' ? 'Sub-beschrijving' : 'Sub-description'}\n                    defaultValue={item.subDescription}\n                    onChange={(value) => updateItem(index, { subDescription: value as string })}\n                  />\n                </div>\n\n                <div className=\"grid grid-cols-3 gap-2\">\n                  <SimpleInput\n                    label={language === 'nl' ? 'Prijs (€)' : 'Price (€)'}\n                    defaultValue={item.price}\n                    onChange={(value) => updateItem(index, { price: value as number })}\n                    type=\"number\"\n                  />\n                  <SimpleSelect\n                    label={language === 'nl' ? 'BTW' : 'VAT'}\n                    defaultValue={item.vatRate}\n                    onChange={(value) => updateItem(index, { vatRate: value as number })}\n                    options={[\n                      { value: 21, label: '21%' },\n                      { value: 9, label: '9%' }\n                    ]}\n                  />\n                  <div className=\"flex items-end\">\n                    <div className=\"text-xs text-gray-600 pb-1\">\n                      <strong>{language === 'nl' ? 'Totaal excl.:' : 'Total excl.:'} €{item.total.toFixed(2)}</strong>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n\n            <button\n              onClick={addItem}\n              className=\"w-full flex items-center justify-center px-3 py-2 border border-dashed border-gray-300 rounded text-sm text-gray-600 hover:text-gray-900 hover:border-gray-400 transition-colors\"\n            >\n              <Plus className=\"h-4 w-4 mr-2\" />\n              {language === 'nl' ? 'Item Toevoegen' : 'Add Item'}\n            </button>\n          </div>\n        );\n\n      case 'notes':\n        return (\n          <div className=\"space-y-3\">\n            <SimpleTextArea\n              label={language === 'nl' ? 'Opmerkingen' : 'Notes'}\n              defaultValue={data.notes}\n              onChange={(value) => updateData({ notes: value })}\n              rows={3}\n            />\n            <SimpleTextArea\n              label={language === 'nl' ? 'Voorwaarden' : 'Conditions'}\n              defaultValue={data.conditions}\n              onChange={(value) => updateData({ conditions: value })}\n              rows={3}\n            />\n          </div>\n        );\n\n      case 'steps':\n        return (\n          <div className=\"space-y-3\">\n            <SimpleTextArea\n              label={language === 'nl' ? 'Stap 1: Offerte Accorderen' : 'Step 1: Approve Quote'}\n              defaultValue={data.nextSteps.step1}\n              onChange={(value) => updateNestedData('nextSteps', { step1: value })}\n              rows={2}\n            />\n            <SimpleTextArea\n              label={language === 'nl' ? 'Stap 2: Aanbetaling' : 'Step 2: Deposit'}\n              defaultValue={data.nextSteps.step2}\n              onChange={(value) => updateNestedData('nextSteps', { step2: value })}\n              rows={1}\n            />\n            <SimpleTextArea\n              label={language === 'nl' ? 'Stap 3: Afspraak Inplannen' : 'Step 3: Schedule Appointment'}\n              defaultValue={data.nextSteps.step3}\n              onChange={(value) => updateNestedData('nextSteps', { step3: value })}\n              rows={1}\n            />\n            <SimpleTextArea\n              label={language === 'nl' ? 'Stap 4: Restbetaling' : 'Step 4: Final Payment'}\n              defaultValue={data.nextSteps.step4}\n              onChange={(value) => updateNestedData('nextSteps', { step4: value })}\n              rows={1}\n            />\n          </div>\n        );\n\n      case 'company':\n        return (\n          <div className=\"space-y-3\">\n            <div className=\"grid grid-cols-2 gap-3\">\n              <SimpleInput\n                label={language === 'nl' ? 'Bedrijfsnaam' : 'Company Name'}\n                defaultValue={data.company.name}\n                onChange={(value) => updateNestedData('company', { name: value as string })}\n              />\n              <SimpleInput\n                label=\"Website\"\n                defaultValue={data.company.website}\n                onChange={(value) => updateNestedData('company', { website: value as string })}\n              />\n            </div>\n            <div className=\"grid grid-cols-2 gap-3\">\n              <SimpleInput\n                label=\"KvK\"\n                defaultValue={data.company.kvk}\n                onChange={(value) => updateNestedData('company', { kvk: value as string })}\n              />\n              <SimpleInput\n                label=\"BTW\"\n                defaultValue={data.company.btw}\n                onChange={(value) => updateNestedData('company', { btw: value as string })}\n              />\n            </div>\n            <div className=\"grid grid-cols-2 gap-3\">\n              <SimpleInput\n                label={language === 'nl' ? 'Adres' : 'Address'}\n                defaultValue={data.company.address}\n                onChange={(value) => updateNestedData('company', { address: value as string })}\n              />\n              <SimpleInput\n                label={language === 'nl' ? 'Stad' : 'City'}\n                defaultValue={data.company.city}\n                onChange={(value) => updateNestedData('company', { city: value as string })}\n              />\n            </div>\n            <div className=\"grid grid-cols-2 gap-3\">\n              <SimpleInput\n                label=\"Email\"\n                defaultValue={data.company.email}\n                onChange={(value) => updateNestedData('company', { email: value as string })}\n              />\n              <SimpleInput\n                label={language === 'nl' ? 'Telefoon' : 'Phone'}\n                defaultValue={data.company.phone}\n                onChange={(value) => updateNestedData('company', { phone: value as string })}\n              />\n            </div>\n          </div>\n        );\n\n      case 'payment':\n        return (\n          <div className=\"space-y-3\">\n            <div className=\"grid grid-cols-2 gap-3\">\n              <SimpleInput\n                label=\"IBAN\"\n                defaultValue={data.payment.iban}\n                onChange={(value) => updateNestedData('payment', { iban: value as string })}\n              />\n              <SimpleInput\n                label={language === 'nl' ? 'Rekeninghouder' : 'Account Name'}\n                defaultValue={data.payment.accountName}\n                onChange={(value) => updateNestedData('payment', { accountName: value as string })}\n              />\n            </div>\n            <SimpleInput\n              label={language === 'nl' ? 'Betaallink' : 'Payment Link'}\n              defaultValue={data.payment.paymentLink}\n              onChange={(value) => updateNestedData('payment', { paymentLink: value as string })}\n              placeholder=\"www.asklussen.nl\"\n            />\n            <div className=\"grid grid-cols-2 gap-3\">\n              <SimpleInput\n                label={language === 'nl' ? 'Aanbetaling (%)' : 'Deposit (%)'}\n                defaultValue={data.payment.depositPercentage}\n                onChange={(value) => updateNestedData('payment', { depositPercentage: value as number })}\n                type=\"number\"\n                placeholder=\"30\"\n              />\n              <SimpleInput\n                label={language === 'nl' ? 'Contantkorting (%)' : 'Cash Discount (%)'}\n                defaultValue={data.payment.cashDiscountPercentage}\n                onChange={(value) => updateNestedData('payment', { cashDiscountPercentage: value as number })}\n                type=\"number\"\n                placeholder=\"5\"\n              />\n            </div>\n            <div className=\"grid grid-cols-1 gap-3\">\n              <SimpleInput\n                label={language === 'nl' ? 'Uurtarief (€)' : 'Hourly Rate (€)'}\n                defaultValue={data.payment.hourlyRate}\n                onChange={(value) => updateNestedData('payment', { hourlyRate: value as number })}\n                type=\"number\"\n                placeholder=\"75.00\"\n              />\n            </div>\n          </div>\n        );\n\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Step Navigation */}\n      <div className=\"flex items-center justify-between mb-4\">\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-sm text-gray-500\">\n            {language === 'nl' ? 'Stap' : 'Step'} {currentStep + 1} {language === 'nl' ? 'van' : 'of'} {FORM_STEPS.length}\n          </span>\n        </div>\n        <div className=\"flex space-x-2\">\n          <button\n            onClick={prevStep}\n            disabled={currentStep === 0}\n            className=\"flex items-center px-4 py-2 text-sm border-2 border-blue-500 text-blue-600 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-blue-50 hover:border-blue-600 transition-all duration-200 font-medium\"\n          >\n            <ChevronLeft className=\"h-4 w-4 mr-1\" />\n            {language === 'nl' ? 'Vorige' : 'Previous'}\n          </button>\n          {currentStep === FORM_STEPS.length - 1 ? (\n            <button\n              onClick={() => {\n                alert(language === 'nl'\n                  ? '🎉 Template succesvol voltooid! Alle gegevens zijn opgeslagen.'\n                  : '🎉 Template successfully completed! All data has been saved.'\n                );\n              }}\n              className=\"flex items-center px-6 py-2 text-sm bg-gradient-to-r from-green-600 to-green-700 text-white rounded-lg hover:from-green-700 hover:to-green-800 transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:scale-105\"\n            >\n              ✓ {language === 'nl' ? 'Voltooien' : 'Complete'}\n            </button>\n          ) : (\n            <button\n              onClick={nextStep}\n              className=\"flex items-center px-6 py-2 text-sm bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:scale-105\"\n            >\n              {language === 'nl' ? 'Volgende' : 'Next'}\n              <ChevronRight className=\"h-4 w-4 ml-1\" />\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* Step Indicator */}\n      <div className=\"flex items-center space-x-2 mb-4\">\n        {FORM_STEPS.map((step, index) => (\n          <div key={step.id} className=\"flex items-center\">\n            <button\n              onClick={() => setCurrentStep(index)}\n              className={`w-8 h-8 rounded-full text-xs font-medium ${\n                index === currentStep\n                  ? 'bg-blue-600 text-white'\n                  : index < currentStep\n                  ? 'bg-green-600 text-white'\n                  : 'bg-gray-200 text-gray-600'\n              }`}\n            >\n              {index + 1}\n            </button>\n            {index < FORM_STEPS.length - 1 && (\n              <div className={`w-8 h-0.5 ${index < currentStep ? 'bg-green-600' : 'bg-gray-200'}`} />\n            )}\n          </div>\n        ))}\n      </div>\n\n      {/* Current Step Content */}\n      <div className=\"bg-white border border-gray-200 rounded-lg p-4\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">{stepTitle}</h3>\n        <div className=\"max-h-[calc(100vh-400px)] overflow-y-auto\">\n          {renderStepContent()}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAYA,MAAM,aAAa;IACjB;QAAE,IAAI;QAAU,SAAS;QAAkB,SAAS;IAAiB;IACrE;QAAE,IAAI;QAAU,SAAS;QAAmB,SAAS;IAAiB;IACtE;QAAE,IAAI;QAAS,SAAS;QAAiB,SAAS;IAAc;IAChE;QAAE,IAAI;QAAS,SAAS;QAAe,SAAS;IAAQ;IACxD;QAAE,IAAI;QAAS,SAAS;QAAkB,SAAS;IAAa;IAChE;QAAE,IAAI;QAAW,SAAS;QAAoB,SAAS;IAAe;IACtE;QAAE,IAAI;QAAW,SAAS;QAAkB,SAAS;IAAe;CACrE;AAEM,SAAS,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAmB;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,WAAW;QACf,IAAI,cAAc,WAAW,MAAM,GAAG,GAAG;YACvC,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,WAAW;QACf,IAAI,cAAc,GAAG;YACnB,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,SAAS;YAAE,GAAG,IAAI;YAAE,GAAG,OAAO;QAAC;IACjC;IAEA,MAAM,mBAAmB,CACvB,KACA;QAEA,SAAS;YACP,GAAG,IAAI;YACP,CAAC,IAAI,EAAE;gBAAE,GAAG,IAAI,CAAC,IAAI;gBAAE,GAAG,OAAO;YAAC;QACpC;IACF;IAEA,MAAM,UAAU;QACd,MAAM,gBAAgB,KAAK,GAAG,IAAI,KAAK,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,KAAK,UAAU,GAAG,KAAK;QAChF,MAAM,UAAqB;YACzB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,YAAY;YACZ,aAAa,aAAa,OAAO,mBAAmB;YACpD,gBAAgB,aAAa,OAAO,gCAAgC;YACpE,UAAU;YACV,MAAM,aAAa,OAAO,SAAS;YACnC,OAAO;YACP,SAAS;YACT,OAAO;QACT;QAEA,WAAW;YAAE,OAAO;mBAAI,KAAK,KAAK;gBAAE;aAAQ;QAAC;IAC/C;IAEA,MAAM,aAAa,CAAC,OAAe;QACjC,MAAM,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM;YACzC,IAAI,MAAM,OAAO;gBACf,MAAM,cAAc;oBAAE,GAAG,IAAI;oBAAE,GAAG,OAAO;gBAAC;gBAC1C,yFAAyF;gBACzF,IAAI,WAAW,WAAW,cAAc,SAAS;oBAC/C,YAAY,KAAK,GAAG,YAAY,KAAK,GAAG,YAAY,QAAQ;gBAC9D;gBACA,OAAO;YACT;YACA,OAAO;QACT;QAEA,WAAW;YAAE,OAAO;QAAa;IACnC;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,eAAe,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACvD,WAAW;YAAE,OAAO;QAAa;IACnC;IAEA,MAAM,kBAAkB,UAAU,CAAC,YAAY;IAC/C,MAAM,YAAY,aAAa,OAAO,gBAAgB,OAAO,GAAG,gBAAgB,OAAO;IAEvF,iDAAiD;IACjD,MAAM,cAAc,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,QAAQ,EAAE,OAAO,MAAM,EAAE,cAAc,EAAE,EAMpF,iBACC,8OAAC;;8BACC,8OAAC;oBAAM,WAAU;8BAAgD;;;;;;8BACjE,8OAAC;oBACC,MAAM;oBACN,cAAc,OAAO,gBAAgB;oBACrC,QAAQ,CAAC;wBACP,MAAM,MAAM,EAAE,MAAM,CAAC,KAAK;wBAC1B,IAAI,SAAS,UAAU;4BACrB,SAAS,QAAQ,KAAK,IAAI,WAAW,QAAQ;wBAC/C,OAAO;4BACL,SAAS;wBACX;oBACF;oBACA,aAAa;oBACb,WAAU;;;;;;;;;;;;IAKhB,MAAM,iBAAiB,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,QAAQ,EAAE,OAAO,CAAC,EAKhE,iBACC,8OAAC;;8BACC,8OAAC;oBAAM,WAAU;8BAAgD;;;;;;8BACjE,8OAAC;oBACC,cAAc,OAAO,gBAAgB;oBACrC,QAAQ,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oBACtC,MAAM;oBACN,WAAU;;;;;;;;;;;;IAKhB,MAAM,eAAe,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,QAAQ,EAAE,OAAO,EAK7D,iBACC,8OAAC;;8BACC,8OAAC;oBAAM,WAAU;8BAAgD;;;;;;8BACjE,8OAAC;oBACC,cAAc,OAAO,gBAAgB;oBACrC,UAAU,CAAC;wBACT,MAAM,MAAM,EAAE,MAAM,CAAC,KAAK;wBAC1B,MAAM,MAAM,WAAW;wBACvB,SAAS,MAAM,OAAO,MAAM;oBAC9B;oBACA,WAAU;8BAET,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;4BAA0B,cAAc,OAAO,KAAK;sCAAG,OAAO,KAAK;2BAAvD,OAAO,KAAK;;;;;;;;;;;;;;;;IAQjC,MAAM,oBAAoB;QACxB,OAAQ,gBAAgB,EAAE;YACxB,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,OAAO,aAAa,OAAO,UAAU;4BACrC,cAAc,KAAK,KAAK;4BACxB,UAAU,CAAC,QAAU,WAAW;oCAAE,OAAO;gCAAgB;;;;;;sCAE3D,8OAAC;4BACC,OAAO,aAAa,OAAO,iBAAiB;4BAC5C,cAAc,KAAK,WAAW;4BAC9B,UAAU,CAAC,QAAU,WAAW;oCAAE,aAAa;gCAAgB;;;;;;;;;;;;YAKvE,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,OAAO,aAAa,OAAO,eAAe;oCAC1C,cAAc,KAAK,MAAM,CAAC,IAAI;oCAC9B,UAAU,CAAC,QAAU,iBAAiB,UAAU;4CAAE,MAAM;wCAAgB;;;;;;8CAE1E,8OAAC;oCACC,OAAO,aAAa,OAAO,mBAAmB;oCAC9C,cAAc,KAAK,KAAK,CAAC,MAAM;oCAC/B,UAAU,CAAC,QAAU,iBAAiB,SAAS;4CAAE,QAAQ;wCAAgB;;;;;;;;;;;;sCAG7E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,OAAO,aAAa,OAAO,UAAU;oCACrC,cAAc,KAAK,MAAM,CAAC,OAAO;oCACjC,UAAU,CAAC,QAAU,iBAAiB,UAAU;4CAAE,SAAS;wCAAgB;;;;;;8CAE7E,8OAAC;oCACC,OAAO,aAAa,OAAO,SAAS;oCACpC,cAAc,KAAK,MAAM,CAAC,IAAI;oCAC9B,UAAU,CAAC,QAAU,iBAAiB,UAAU;4CAAE,MAAM;wCAAgB;;;;;;;;;;;;sCAG5E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,OAAO,aAAa,OAAO,UAAU;oCACrC,cAAc,KAAK,KAAK,CAAC,IAAI;oCAC7B,UAAU,CAAC,QAAU,iBAAiB,SAAS;4CAAE,MAAM;wCAAgB;;;;;;8CAEzE,8OAAC;oCACC,OAAO,aAAa,OAAO,eAAe;oCAC1C,cAAc,KAAK,KAAK,CAAC,UAAU;oCACnC,UAAU,CAAC,QAAU,iBAAiB,SAAS;4CAAE,YAAY;wCAAgB;;;;;;;;;;;;sCAGjF,8OAAC;4BACC,OAAO,aAAa,OAAO,eAAe;4BAC1C,cAAc,KAAK,QAAQ;4BAC3B,UAAU,CAAC,QAAU,WAAW;oCAAE,UAAU;gCAAM;4BAClD,MAAM;;;;;;sCAER,8OAAC;4BACC,OAAO,aAAa,OAAO,gBAAgB;4BAC3C,cAAc,KAAK,YAAY;4BAC/B,UAAU,CAAC,QAAU,WAAW;oCAAE,cAAc;gCAAM;4BACtD,MAAM;;;;;;;;;;;;YAKd,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;wBACZ,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC;gCAAkB,WAAU;;kDAC3B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;oDACX,aAAa,OAAO,SAAS;oDAAO;oDAAE,KAAK,UAAU;;;;;;;0DAExD,8OAAC;gDACC,SAAS,IAAM,WAAW;gDAC1B,WAAU;0DAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAItB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,OAAM;gDACN,cAAc,KAAK,UAAU;gDAC7B,UAAU,CAAC,QAAU,WAAW,OAAO;wDAAE,YAAY;oDAAgB;gDACrE,MAAK;;;;;;0DAEP,8OAAC;gDACC,OAAO,aAAa,OAAO,WAAW;gDACtC,cAAc,KAAK,QAAQ;gDAC3B,UAAU,CAAC,QAAU,WAAW,OAAO;wDAAE,UAAU;oDAAgB;gDACnE,MAAK;;;;;;0DAEP,8OAAC;gDACC,OAAO,aAAa,OAAO,YAAY;gDACvC,cAAc,KAAK,IAAI;gDACvB,UAAU,CAAC,QAAU,WAAW,OAAO;wDAAE,MAAM;oDAAgB;;;;;;;;;;;;kDAInE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,OAAO,aAAa,OAAO,iBAAiB;gDAC5C,cAAc,KAAK,WAAW;gDAC9B,UAAU,CAAC,QAAU,WAAW,OAAO;wDAAE,aAAa;oDAAgB;;;;;;0DAExE,8OAAC;gDACC,OAAO,aAAa,OAAO,qBAAqB;gDAChD,cAAc,KAAK,cAAc;gDACjC,UAAU,CAAC,QAAU,WAAW,OAAO;wDAAE,gBAAgB;oDAAgB;;;;;;;;;;;;kDAI7E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,OAAO,aAAa,OAAO,cAAc;gDACzC,cAAc,KAAK,KAAK;gDACxB,UAAU,CAAC,QAAU,WAAW,OAAO;wDAAE,OAAO;oDAAgB;gDAChE,MAAK;;;;;;0DAEP,8OAAC;gDACC,OAAO,aAAa,OAAO,QAAQ;gDACnC,cAAc,KAAK,OAAO;gDAC1B,UAAU,CAAC,QAAU,WAAW,OAAO;wDAAE,SAAS;oDAAgB;gDAClE,SAAS;oDACP;wDAAE,OAAO;wDAAI,OAAO;oDAAM;oDAC1B;wDAAE,OAAO;wDAAG,OAAO;oDAAK;iDACzB;;;;;;0DAEH,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;;4DAAQ,aAAa,OAAO,kBAAkB;4DAAe;4DAAG,KAAK,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;+BAhElF,KAAK,EAAE;;;;;sCAuEnB,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCACf,aAAa,OAAO,mBAAmB;;;;;;;;;;;;;YAKhD,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,OAAO,aAAa,OAAO,gBAAgB;4BAC3C,cAAc,KAAK,KAAK;4BACxB,UAAU,CAAC,QAAU,WAAW;oCAAE,OAAO;gCAAM;4BAC/C,MAAM;;;;;;sCAER,8OAAC;4BACC,OAAO,aAAa,OAAO,gBAAgB;4BAC3C,cAAc,KAAK,UAAU;4BAC7B,UAAU,CAAC,QAAU,WAAW;oCAAE,YAAY;gCAAM;4BACpD,MAAM;;;;;;;;;;;;YAKd,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,OAAO,aAAa,OAAO,+BAA+B;4BAC1D,cAAc,KAAK,SAAS,CAAC,KAAK;4BAClC,UAAU,CAAC,QAAU,iBAAiB,aAAa;oCAAE,OAAO;gCAAM;4BAClE,MAAM;;;;;;sCAER,8OAAC;4BACC,OAAO,aAAa,OAAO,wBAAwB;4BACnD,cAAc,KAAK,SAAS,CAAC,KAAK;4BAClC,UAAU,CAAC,QAAU,iBAAiB,aAAa;oCAAE,OAAO;gCAAM;4BAClE,MAAM;;;;;;sCAER,8OAAC;4BACC,OAAO,aAAa,OAAO,+BAA+B;4BAC1D,cAAc,KAAK,SAAS,CAAC,KAAK;4BAClC,UAAU,CAAC,QAAU,iBAAiB,aAAa;oCAAE,OAAO;gCAAM;4BAClE,MAAM;;;;;;sCAER,8OAAC;4BACC,OAAO,aAAa,OAAO,yBAAyB;4BACpD,cAAc,KAAK,SAAS,CAAC,KAAK;4BAClC,UAAU,CAAC,QAAU,iBAAiB,aAAa;oCAAE,OAAO;gCAAM;4BAClE,MAAM;;;;;;;;;;;;YAKd,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,OAAO,aAAa,OAAO,iBAAiB;oCAC5C,cAAc,KAAK,OAAO,CAAC,IAAI;oCAC/B,UAAU,CAAC,QAAU,iBAAiB,WAAW;4CAAE,MAAM;wCAAgB;;;;;;8CAE3E,8OAAC;oCACC,OAAM;oCACN,cAAc,KAAK,OAAO,CAAC,OAAO;oCAClC,UAAU,CAAC,QAAU,iBAAiB,WAAW;4CAAE,SAAS;wCAAgB;;;;;;;;;;;;sCAGhF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,OAAM;oCACN,cAAc,KAAK,OAAO,CAAC,GAAG;oCAC9B,UAAU,CAAC,QAAU,iBAAiB,WAAW;4CAAE,KAAK;wCAAgB;;;;;;8CAE1E,8OAAC;oCACC,OAAM;oCACN,cAAc,KAAK,OAAO,CAAC,GAAG;oCAC9B,UAAU,CAAC,QAAU,iBAAiB,WAAW;4CAAE,KAAK;wCAAgB;;;;;;;;;;;;sCAG5E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,OAAO,aAAa,OAAO,UAAU;oCACrC,cAAc,KAAK,OAAO,CAAC,OAAO;oCAClC,UAAU,CAAC,QAAU,iBAAiB,WAAW;4CAAE,SAAS;wCAAgB;;;;;;8CAE9E,8OAAC;oCACC,OAAO,aAAa,OAAO,SAAS;oCACpC,cAAc,KAAK,OAAO,CAAC,IAAI;oCAC/B,UAAU,CAAC,QAAU,iBAAiB,WAAW;4CAAE,MAAM;wCAAgB;;;;;;;;;;;;sCAG7E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,OAAM;oCACN,cAAc,KAAK,OAAO,CAAC,KAAK;oCAChC,UAAU,CAAC,QAAU,iBAAiB,WAAW;4CAAE,OAAO;wCAAgB;;;;;;8CAE5E,8OAAC;oCACC,OAAO,aAAa,OAAO,aAAa;oCACxC,cAAc,KAAK,OAAO,CAAC,KAAK;oCAChC,UAAU,CAAC,QAAU,iBAAiB,WAAW;4CAAE,OAAO;wCAAgB;;;;;;;;;;;;;;;;;;YAMpF,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,OAAM;oCACN,cAAc,KAAK,OAAO,CAAC,IAAI;oCAC/B,UAAU,CAAC,QAAU,iBAAiB,WAAW;4CAAE,MAAM;wCAAgB;;;;;;8CAE3E,8OAAC;oCACC,OAAO,aAAa,OAAO,mBAAmB;oCAC9C,cAAc,KAAK,OAAO,CAAC,WAAW;oCACtC,UAAU,CAAC,QAAU,iBAAiB,WAAW;4CAAE,aAAa;wCAAgB;;;;;;;;;;;;sCAGpF,8OAAC;4BACC,OAAO,aAAa,OAAO,eAAe;4BAC1C,cAAc,KAAK,OAAO,CAAC,WAAW;4BACtC,UAAU,CAAC,QAAU,iBAAiB,WAAW;oCAAE,aAAa;gCAAgB;4BAChF,aAAY;;;;;;sCAEd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,OAAO,aAAa,OAAO,oBAAoB;oCAC/C,cAAc,KAAK,OAAO,CAAC,iBAAiB;oCAC5C,UAAU,CAAC,QAAU,iBAAiB,WAAW;4CAAE,mBAAmB;wCAAgB;oCACtF,MAAK;oCACL,aAAY;;;;;;8CAEd,8OAAC;oCACC,OAAO,aAAa,OAAO,uBAAuB;oCAClD,cAAc,KAAK,OAAO,CAAC,sBAAsB;oCACjD,UAAU,CAAC,QAAU,iBAAiB,WAAW;4CAAE,wBAAwB;wCAAgB;oCAC3F,MAAK;oCACL,aAAY;;;;;;;;;;;;sCAGhB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,OAAO,aAAa,OAAO,kBAAkB;gCAC7C,cAAc,KAAK,OAAO,CAAC,UAAU;gCACrC,UAAU,CAAC,QAAU,iBAAiB,WAAW;wCAAE,YAAY;oCAAgB;gCAC/E,MAAK;gCACL,aAAY;;;;;;;;;;;;;;;;;YAMtB;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;;gCACb,aAAa,OAAO,SAAS;gCAAO;gCAAE,cAAc;gCAAE;gCAAE,aAAa,OAAO,QAAQ;gCAAK;gCAAE,WAAW,MAAM;;;;;;;;;;;;kCAGjH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU,gBAAgB;gCAC1B,WAAU;;kDAEV,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,aAAa,OAAO,WAAW;;;;;;;4BAEjC,gBAAgB,WAAW,MAAM,GAAG,kBACnC,8OAAC;gCACC,SAAS;oCACP,MAAM,aAAa,OACf,mEACA;gCAEN;gCACA,WAAU;;oCACX;oCACI,aAAa,OAAO,cAAc;;;;;;qDAGvC,8OAAC;gCACC,SAAS;gCACT,WAAU;;oCAET,aAAa,OAAO,aAAa;kDAClC,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0BAOhC,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC;wBAAkB,WAAU;;0CAC3B,8OAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAW,CAAC,yCAAyC,EACnD,UAAU,cACN,2BACA,QAAQ,cACR,4BACA,6BACJ;0CAED,QAAQ;;;;;;4BAEV,QAAQ,WAAW,MAAM,GAAG,mBAC3B,8OAAC;gCAAI,WAAW,CAAC,UAAU,EAAE,QAAQ,cAAc,iBAAiB,eAAe;;;;;;;uBAd7E,KAAK,EAAE;;;;;;;;;;0BAqBrB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA4C;;;;;;kCAC1D,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 1322, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/offerte%20email%20template%20editor%20/offerte-template-editor/src/components/TemplatePreview.tsx"], "sourcesContent": ["'use client';\n\nimport { TemplateData, Language } from '@/types/template';\n\ninterface TemplatePreviewProps {\n  data: TemplateData;\n  language: Language;\n}\n\nexport function TemplatePreview({ data, language }: TemplatePreviewProps) {\n  // Calculate totals - items.total should be excluding VAT\n  const subtotal = data.items.reduce((sum, item) => sum + item.total, 0);\n\n  // Group VAT by rate\n  const vatByRate = data.items.reduce((acc, item) => {\n    const vatAmount = item.total * item.vatRate / 100;\n    if (!acc[item.vatRate]) {\n      acc[item.vatRate] = 0;\n    }\n    acc[item.vatRate] += vatAmount;\n    return acc;\n  }, {} as Record<number, number>);\n\n  const totalVatAmount = Object.values(vatByRate).reduce((sum, vat) => sum + vat, 0);\n  const total = subtotal + totalVatAmount;\n  const depositAmount = total * (data.payment.depositPercentage / 100);\n  const remainingAmount = total - depositAmount;\n  const cashDiscountAmount = total * (data.payment.cashDiscountPercentage / 100);\n  const totalWithCashDiscount = total - cashDiscountAmount;\n\n  // Payment link logic - use default if empty, no extra parameters\n  const paymentLink = data.payment.paymentLink || 'www.asklussen.nl';\n\n  // Generate QR code URL from payment link\n  const generateQRCode = () => {\n    return `https://api.qrserver.com/v1/create-qr-code/?size=50x50&data=${encodeURIComponent(paymentLink)}`;\n  };\n\n  // Replace dynamic values in text\n  const replaceDynamicValues = (text: string) => {\n    let result = text;\n\n    // OPMERKINGEN: Replace cash discount calculations\n    if (text.includes('contante betaling') || text.includes('cash payment')) {\n      // Replace specific patterns step by step\n\n      // 1. Replace total amount after \"totaalbedrag van\"\n      result = result.replace(/(totaalbedrag van\\s+)€\\s*[\\d.,]+/gi, `$1€ ${total.toFixed(2)}`);\n      result = result.replace(/(total amount of\\s+)€\\s*[\\d.,]+/gi, `$1€ ${total.toFixed(2)}`);\n\n      // 2. Replace savings amount after \"besparing\"\n      result = result.replace(/(besparing\\s+)€\\s*[\\d.,]+/gi, `$1€ ${cashDiscountAmount.toFixed(2)}`);\n      result = result.replace(/(saving\\s+)€\\s*[\\d.,]+/gi, `$1€ ${cashDiscountAmount.toFixed(2)}`);\n\n      // 3. Replace placeholder text\n      result = result.replace(/hier moet %?5 van bedraag van offerte incl btw hier plaatsen €/gi, `€ ${cashDiscountAmount.toFixed(2)}`);\n      result = result.replace(/here must %?5 of quote amount incl vat here place €/gi, `€ ${cashDiscountAmount.toFixed(2)}`);\n    }\n\n    // VOORWAARDEN: Replace deposit and hourly rate\n    if (text.includes('aanbetaling van') || text.includes('deposit of')) {\n      // Replace deposit amount after \"aanbetaling van\"\n      result = result.replace(/(aanbetaling van\\s+\\d+%\\s+\\()€\\s*[\\d.,]+/gi, `$1€ ${depositAmount.toFixed(2)}`);\n      result = result.replace(/(deposit of\\s+\\d+%\\s+\\()€\\s*[\\d.,]+/gi, `$1€ ${depositAmount.toFixed(2)}`);\n\n      // Replace hourly rate after \"tegen\" or \"at\"\n      result = result.replace(/(tegen\\s+)€\\s*[\\d.,]+/gi, `$1€ ${data.payment.hourlyRate.toFixed(2)}`);\n      result = result.replace(/(at\\s+)€\\s*[\\d.,]+/gi, `$1€ ${data.payment.hourlyRate.toFixed(2)}`);\n\n      // Add \"excl. BTW\" after hourly rate if not present\n      if (!result.includes('excl. BTW') && !result.includes('excl. VAT')) {\n        result = result.replace(/(€\\s*[\\d.,]+\\/uur)/, '$1 excl. BTW');\n      }\n    }\n\n    // VERVOLGSTAPPEN: Replace deposit and remaining amounts\n    if (text.includes('Betaal') || text.includes('Pay') || text.includes('Restbetaling')) {\n      result = result.replace(/€\\s*[\\d.,]+/g, (match, offset) => {\n        const beforeMatch = text.substring(0, offset);\n\n        // Deposit payment (30% of total incl. BTW)\n        if (beforeMatch.includes('Betaal') || beforeMatch.includes('Pay')) {\n          return `€ ${depositAmount.toFixed(2)}`;\n        }\n        // Remaining payment (70% of total incl. BTW)\n        if (beforeMatch.includes('Restbetaling') || beforeMatch.includes('Final Payment')) {\n          return `€ ${remainingAmount.toFixed(2)}`;\n        }\n        return match;\n      });\n    }\n\n    // Replace percentage values dynamically\n    result = result.replace(/(\\d+)%/g, (match, percentage) => {\n      if (text.includes('aanbetaling') || text.includes('deposit')) {\n        return `${data.payment.depositPercentage}%`;\n      }\n      if (text.includes('korting') || text.includes('discount')) {\n        return `${data.payment.cashDiscountPercentage}%`;\n      }\n      return match;\n    });\n\n    // Replace quote numbers everywhere\n    const oldQuotePattern = /OFF-2025-\\d+|QUO-2025-\\d+/g;\n    result = result.replace(oldQuotePattern, data.quote.number);\n\n    return result;\n  };\n\n  return (\n    <div className=\"max-w-2xl mx-auto bg-white shadow-lg rounded-lg overflow-hidden\">\n      {/* Email-style container */}\n      <div style={{ backgroundColor: '#f3f4f6', padding: '25px 10px' }}>\n        <div style={{ \n          maxWidth: '580px', \n          margin: '0 auto',\n          backgroundColor: '#ffffff', \n          borderRadius: '10px', \n          overflow: 'hidden', \n          boxShadow: '0 4px 15px rgba(0,0,0,0.08)' \n        }}>\n          \n          {/* Header */}\n          <div style={{ backgroundColor: '#111827', color: '#ffffff', padding: '30px 25px' }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n              <div>\n                <h1 style={{ margin: 0, fontSize: '32px', fontWeight: 800, color: '#ffffff', letterSpacing: '1px' }}>\n                  {data.title}\n                </h1>\n                <p style={{ margin: '8px 0 0 0', fontSize: '15px', color: '#d1d5db', fontWeight: 500 }}>\n                  {data.companyName}\n                </p>\n              </div>\n              <div style={{ textAlign: 'right' }}>\n                <div style={{ \n                  background: 'linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%)', \n                  padding: '3px', \n                  borderRadius: '8px', \n                  display: 'inline-block',\n                  boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)' \n                }}>\n                  <div style={{ \n                    backgroundColor: '#1e40af', \n                    color: '#ffffff', \n                    padding: '10px 18px', \n                    borderRadius: '6px', \n                    fontSize: '15px', \n                    fontWeight: 700, \n                    letterSpacing: '0.5px' \n                  }}>\n                    {data.quote.number}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n          \n          {/* Content */}\n          <div style={{ padding: '25px' }}>\n            \n            {/* Client & Quote Info */}\n            <div style={{ display: 'flex', gap: '4%', marginBottom: '25px' }}>\n              <div style={{ \n                width: '48%', \n                backgroundColor: '#f9fafb', \n                padding: '15px', \n                borderRadius: '8px', \n                borderLeft: '3px solid #3b82f6' \n              }}>\n                <h3 style={{ margin: '0 0 10px 0', color: '#111827', fontSize: '14px', fontWeight: 600 }}>\n                  {language === 'nl' ? 'OPDRACHTGEVER' : 'CLIENT'}\n                </h3>\n                <div style={{ fontSize: '13px', color: '#4b5563', lineHeight: 1.5 }}>\n                  <strong style={{ color: '#111827' }}>{data.client.name}</strong><br />\n                  {data.client.address}<br />\n                  {data.client.city}\n                </div>\n              </div>\n              \n              <div style={{ \n                width: '48%', \n                backgroundColor: '#f9fafb', \n                padding: '15px', \n                borderRadius: '8px', \n                borderLeft: '3px solid #f59e0b' \n              }}>\n                <h3 style={{ margin: '0 0 10px 0', color: '#111827', fontSize: '14px', fontWeight: 600 }}>\n                  {language === 'nl' ? 'OFFERTE DETAILS' : 'QUOTE DETAILS'}\n                </h3>\n                <div style={{ fontSize: '13px', color: '#4b5563', lineHeight: 1.5 }}>\n                  <strong style={{ color: '#111827' }}>\n                    {language === 'nl' ? 'Nummer:' : 'Number:'}\n                  </strong> {data.quote.number}<br />\n                  <strong style={{ color: '#111827' }}>\n                    {language === 'nl' ? 'Datum:' : 'Date:'}\n                  </strong> {data.quote.date}<br />\n                  <strong style={{ color: '#111827' }}>\n                    {language === 'nl' ? 'Geldig tot:' : 'Valid until:'}\n                  </strong> {data.quote.validUntil}\n                </div>\n              </div>\n            </div>\n\n            {/* Introduction */}\n            <div style={{ \n              backgroundColor: '#eff6ff', \n              borderRadius: '8px', \n              margin: '25px 0', \n              borderLeft: '3px solid #3b82f6',\n              padding: '15px'\n            }}>\n              <p style={{ margin: '0 0 8px 0', fontSize: '14px', color: '#1e40af', fontWeight: 600 }}>\n                {data.greeting}\n              </p>\n              <p style={{ margin: 0, fontSize: '13px', color: '#1e40af', lineHeight: 1.6 }}>\n                {data.introduction}\n              </p>\n            </div>\n\n            {/* Cost Breakdown */}\n            <div style={{ margin: '30px 0' }}>\n              <h2 style={{ \n                color: '#0c4a6e', \n                fontSize: '16px', \n                fontWeight: 700, \n                margin: '0 0 12px 0', \n                textAlign: 'center', \n                textTransform: 'uppercase', \n                letterSpacing: '0.5px', \n                borderBottom: '2px solid #0ea5e9', \n                paddingBottom: '8px' \n              }}>\n                {language === 'nl' ? 'Kostenoverzicht' : 'Cost Overview'}\n              </h2>\n              \n              <table style={{ \n                width: '100%', \n                borderCollapse: 'collapse', \n                borderRadius: '8px', \n                overflow: 'hidden', \n                boxShadow: '0 2px 8px rgba(0,0,0,0.08)' \n              }}>\n                {/* Header */}\n                <thead>\n                  <tr>\n                    <th style={{ backgroundColor: '#0c4a6e', color: '#ffffff', padding: '10px 6px', textAlign: 'center', fontSize: '11px', fontWeight: 600, letterSpacing: '0.5px', textTransform: 'uppercase' }}>#</th>\n                    <th style={{ backgroundColor: '#0c4a6e', color: '#ffffff', padding: '10px 12px', textAlign: 'left', fontSize: '11px', fontWeight: 600, letterSpacing: '0.5px', textTransform: 'uppercase' }}>\n                      {language === 'nl' ? 'Omschrijving' : 'Description'}\n                    </th>\n                    <th style={{ backgroundColor: '#0c4a6e', color: '#ffffff', padding: '10px 6px', textAlign: 'center', fontSize: '11px', fontWeight: 600, letterSpacing: '0.5px', textTransform: 'uppercase' }}>\n                      {language === 'nl' ? 'Aantal' : 'Qty'}\n                    </th>\n                    <th style={{ backgroundColor: '#0c4a6e', color: '#ffffff', padding: '10px 6px', textAlign: 'center', fontSize: '11px', fontWeight: 600, letterSpacing: '0.5px', textTransform: 'uppercase' }}>\n                      {language === 'nl' ? 'Eenh.' : 'Unit'}\n                    </th>\n                    <th style={{ backgroundColor: '#0c4a6e', color: '#ffffff', padding: '10px 10px', textAlign: 'right', fontSize: '11px', fontWeight: 600, letterSpacing: '0.5px', textTransform: 'uppercase' }}>\n                      {language === 'nl' ? 'Prijs' : 'Price'}\n                    </th>\n                    <th style={{ backgroundColor: '#0c4a6e', color: '#ffffff', padding: '10px 6px', textAlign: 'center', fontSize: '11px', fontWeight: 600, letterSpacing: '0.5px', textTransform: 'uppercase' }}>\n                      {language === 'nl' ? 'BTW' : 'VAT'}\n                    </th>\n                    <th style={{ backgroundColor: '#0c4a6e', color: '#ffffff', padding: '10px 10px', textAlign: 'right', fontSize: '11px', fontWeight: 600, letterSpacing: '0.5px', textTransform: 'uppercase' }}>\n                      {language === 'nl' ? 'Totaal' : 'Total'}\n                    </th>\n                  </tr>\n                </thead>\n                \n                {/* Items */}\n                <tbody>\n                  {data.items.map((item, index) => (\n                    <tr key={item.id} style={{ backgroundColor: index % 2 === 0 ? '#ffffff' : '#f0f9ff' }}>\n                      <td style={{ padding: '10px 6px', borderBottom: '1px solid #e5e7eb', textAlign: 'center', fontSize: '12px', color: '#6b7280' }}>\n                        {item.itemNumber}\n                      </td>\n                      <td style={{ padding: '10px 12px', borderBottom: '1px solid #e5e7eb' }}>\n                        <div style={{ fontWeight: 600, color: '#0c4a6e', fontSize: '12px' }}>\n                          {item.description}\n                        </div>\n                        <div style={{ color: '#6b7280', fontSize: '10px' }}>\n                          {item.subDescription}\n                        </div>\n                      </td>\n                      <td style={{ padding: '10px 6px', borderBottom: '1px solid #e5e7eb', textAlign: 'center', fontSize: '12px', color: '#4b5563' }}>\n                        {item.quantity}\n                      </td>\n                      <td style={{ padding: '10px 6px', borderBottom: '1px solid #e5e7eb', textAlign: 'center', fontSize: '12px', color: '#4b5563' }}>\n                        {item.unit}\n                      </td>\n                      <td style={{ padding: '10px 10px', borderBottom: '1px solid #e5e7eb', textAlign: 'right', fontSize: '12px', color: '#4b5563' }}>\n                        <span style={{ fontFamily: 'monospace', whiteSpace: 'nowrap' }}>€&nbsp;{item.price.toFixed(2)}</span>\n                      </td>\n                      <td style={{ padding: '10px 6px', borderBottom: '1px solid #e5e7eb', textAlign: 'center', fontSize: '12px', color: '#4b5563' }}>\n                        {item.vatRate}%\n                      </td>\n                      <td style={{ padding: '10px 10px', borderBottom: '1px solid #e5e7eb', textAlign: 'right', fontWeight: 600, color: '#0c4a6e', fontSize: '12px' }}>\n                        <span style={{ fontFamily: 'monospace', whiteSpace: 'nowrap' }}>€&nbsp;{item.total.toFixed(2)}</span>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n                \n                {/* Totals */}\n                <tfoot>\n                  <tr style={{ backgroundColor: '#f0f9ff' }}>\n                    <td colSpan={6} style={{ padding: '10px 12px', textAlign: 'right', fontWeight: 600, fontSize: '12px', color: '#0c4a6e', letterSpacing: '0.3px' }}>\n                      {language === 'nl' ? 'Subtotaal (excl. BTW)' : 'Subtotal (excl. VAT)'}\n                    </td>\n                    <td style={{ padding: '10px 10px', textAlign: 'right', fontWeight: 600, color: '#0c4a6e', fontSize: '12px' }}>\n                      <span style={{ fontFamily: 'monospace', whiteSpace: 'nowrap' }}>€&nbsp;{subtotal.toFixed(2)}</span>\n                    </td>\n                  </tr>\n                  {Object.entries(vatByRate).map(([rate, amount]) => (\n                    <tr key={rate} style={{ backgroundColor: '#f0f9ff' }}>\n                      <td colSpan={6} style={{ padding: '6px 12px', textAlign: 'right', fontSize: '12px', color: '#4b5563' }}>\n                        {language === 'nl' ? `BTW (${rate}%)` : `VAT (${rate}%)`}\n                      </td>\n                      <td style={{ padding: '6px 10px', textAlign: 'right', fontWeight: 600, color: '#4b5563', fontSize: '12px' }}>\n                        <span style={{ fontFamily: 'monospace', whiteSpace: 'nowrap' }}>€&nbsp;{amount.toFixed(2)}</span>\n                      </td>\n                    </tr>\n                  ))}\n                  <tr style={{ background: 'linear-gradient(90deg, #0ea5e9 0%, #0c4a6e 100%)' }}>\n                    <td colSpan={6} style={{ padding: '12px', textAlign: 'right', color: '#ffffff', fontSize: '13px', fontWeight: 700, letterSpacing: '0.5px' }}>\n                      {language === 'nl' ? 'TOTAAL INCL. BTW' : 'TOTAL INCL. VAT'}\n                    </td>\n                    <td style={{ padding: '12px 10px', textAlign: 'right', color: '#ffffff', fontSize: '15px', fontWeight: 700 }}>\n                      <span style={{ fontFamily: 'monospace', whiteSpace: 'nowrap' }}>€&nbsp;{total.toFixed(2)}</span>\n                    </td>\n                  </tr>\n                </tfoot>\n              </table>\n            </div>\n\n            {/* Notes */}\n            {data.notes && (\n              <div style={{ \n                backgroundColor: '#fffbeb', \n                borderRadius: '8px', \n                marginBottom: '15px', \n                borderLeft: '3px solid #f59e0b',\n                padding: '15px'\n              }}>\n                <h3 style={{ margin: '0 0 8px 0', color: '#92400e', fontSize: '14px', fontWeight: 600 }}>\n                  {language === 'nl' ? 'OPMERKINGEN' : 'NOTES'}\n                </h3>\n                <p style={{ fontSize: '12px', color: '#92400e', lineHeight: 1.5, margin: 0 }}>\n                  {replaceDynamicValues(data.notes)}\n                </p>\n              </div>\n            )}\n\n            {/* Conditions */}\n            {data.conditions && (\n              <div style={{\n                backgroundColor: '#fef2f2',\n                borderRadius: '8px',\n                marginBottom: '15px',\n                borderLeft: '3px solid #ef4444',\n                padding: '15px'\n              }}>\n                <h3 style={{ margin: '0 0 8px 0', color: '#991b1b', fontSize: '14px', fontWeight: 600 }}>\n                  {language === 'nl' ? 'VOORWAARDEN' : 'CONDITIONS'}\n                </h3>\n                <p style={{ fontSize: '12px', color: '#991b1b', lineHeight: 1.5, margin: 0 }}>\n                  {replaceDynamicValues(data.conditions)}\n                </p>\n              </div>\n            )}\n\n            {/* Next Steps */}\n            <div style={{\n              backgroundColor: '#f0fff4',\n              borderRadius: '8px',\n              margin: '25px 0',\n              borderLeft: '3px solid #10b981',\n              padding: '15px'\n            }}>\n              <h3 style={{ margin: '0 0 10px 0', color: '#065f46', fontSize: '14px', fontWeight: 600 }}>\n                {language === 'nl' ? 'VERVOLGSTAPPEN' : 'NEXT STEPS'}\n              </h3>\n              <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>\n                <div style={{ display: 'flex', alignItems: 'flex-start' }}>\n                  <div style={{\n                    backgroundColor: '#10b981',\n                    color: '#ffffff',\n                    width: '28px',\n                    height: '28px',\n                    borderRadius: '50%',\n                    textAlign: 'center',\n                    lineHeight: '28px',\n                    fontSize: '12px',\n                    fontWeight: 700,\n                    marginRight: '15px',\n                    flexShrink: 0\n                  }}>1</div>\n                  <div style={{ fontSize: '12px', color: '#065f46', lineHeight: 1.5 }}>\n                    {replaceDynamicValues(data.nextSteps.step1)}\n                  </div>\n                </div>\n                <div style={{ display: 'flex', alignItems: 'flex-start' }}>\n                  <div style={{\n                    backgroundColor: '#10b981',\n                    color: '#ffffff',\n                    width: '28px',\n                    height: '28px',\n                    borderRadius: '50%',\n                    textAlign: 'center',\n                    lineHeight: '28px',\n                    fontSize: '12px',\n                    fontWeight: 700,\n                    marginRight: '15px',\n                    flexShrink: 0\n                  }}>2</div>\n                  <div style={{ fontSize: '12px', color: '#065f46', lineHeight: 1.5 }}>\n                    {replaceDynamicValues(data.nextSteps.step2)}\n                  </div>\n                </div>\n                <div style={{ display: 'flex', alignItems: 'flex-start' }}>\n                  <div style={{\n                    backgroundColor: '#10b981',\n                    color: '#ffffff',\n                    width: '28px',\n                    height: '28px',\n                    borderRadius: '50%',\n                    textAlign: 'center',\n                    lineHeight: '28px',\n                    fontSize: '12px',\n                    fontWeight: 700,\n                    marginRight: '15px',\n                    flexShrink: 0\n                  }}>3</div>\n                  <div style={{ fontSize: '12px', color: '#065f46', lineHeight: 1.5 }}>\n                    {replaceDynamicValues(data.nextSteps.step3)}\n                  </div>\n                </div>\n                <div style={{ display: 'flex', alignItems: 'flex-start' }}>\n                  <div style={{\n                    backgroundColor: '#10b981',\n                    color: '#ffffff',\n                    width: '28px',\n                    height: '28px',\n                    borderRadius: '50%',\n                    textAlign: 'center',\n                    lineHeight: '28px',\n                    fontSize: '12px',\n                    fontWeight: 700,\n                    marginRight: '15px',\n                    flexShrink: 0\n                  }}>4</div>\n                  <div style={{ fontSize: '12px', color: '#065f46', lineHeight: 1.5 }}>\n                    {replaceDynamicValues(data.nextSteps.step4)}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Payment Options */}\n            <div style={{\n              backgroundColor: '#f0f9ff',\n              borderRadius: '8px',\n              margin: '25px 0',\n              borderLeft: '3px solid #0ea5e9',\n              padding: '15px'\n            }}>\n              <h3 style={{ margin: '0 0 10px 0', color: '#0c4a6e', fontSize: '14px', fontWeight: 600, textAlign: 'center' }}>\n                {language === 'nl' ? 'BETAALOPTIES AANBETALING' : 'DEPOSIT PAYMENT OPTIONS'} (€ {depositAmount.toFixed(2)})\n              </h3>\n\n              <div style={{\n                backgroundColor: '#ffffff',\n                borderRadius: '6px',\n                boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n                display: 'flex'\n              }}>\n                <div style={{\n                  width: '30%',\n                  padding: '12px',\n                  textAlign: 'center',\n                  borderRight: '1px solid #f3f4f6'\n                }}>\n                  <h4 style={{ margin: '0 0 5px 0', color: '#111827', fontSize: '12px', fontWeight: 600 }}>\n                    {language === 'nl' ? 'iDEAL Betaling' : 'iDEAL Payment'}\n                  </h4>\n                  <a\n                    href={paymentLink}\n                    style={{\n                      backgroundColor: '#10b981',\n                      color: '#ffffff',\n                      padding: '8px 15px',\n                      fontSize: '12px',\n                      fontWeight: 600,\n                      display: 'inline-block',\n                      borderRadius: '4px',\n                      margin: '5px 0',\n                      textDecoration: 'none'\n                    }}\n                  >\n                    {language === 'nl' ? 'NU BETALEN' : 'PAY NOW'}\n                  </a>\n                  <p style={{ margin: '5px 0 0 0', fontSize: '10px', color: '#6b7280' }}>\n                    {language === 'nl' ? 'Snel en veilig online betalen' : 'Fast and secure online payment'}\n                  </p>\n                </div>\n                <div style={{\n                  width: '30%',\n                  padding: '12px',\n                  textAlign: 'center',\n                  borderRight: '1px solid #f3f4f6'\n                }}>\n                  <h4 style={{ margin: '0 0 5px 0', color: '#111827', fontSize: '12px', fontWeight: 600 }}>\n                    {language === 'nl' ? 'QR Code Betaling' : 'QR Code Payment'}\n                  </h4>\n                  <div style={{\n                    border: '2px solid #10b981',\n                    borderRadius: '8px',\n                    padding: '5px',\n                    backgroundColor: '#ffffff',\n                    boxShadow: '0 2px 6px rgba(16, 185, 129, 0.2)',\n                    width: '60px',\n                    height: '60px',\n                    margin: '0 auto',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}>\n                    {data.payment.paymentLink ? (\n                      <img\n                        src={generateQRCode()}\n                        width=\"50\"\n                        height=\"50\"\n                        alt=\"QR Code\"\n                        style={{ display: 'block' }}\n                      />\n                    ) : (\n                      <div style={{\n                        width: '50px',\n                        height: '50px',\n                        backgroundColor: '#f3f4f6',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        fontSize: '8px',\n                        color: '#6b7280'\n                      }}>QR</div>\n                    )}\n                  </div>\n                  <p style={{ margin: '5px 0 0 0', fontSize: '10px', color: '#6b7280' }}>\n                    {language === 'nl' ? 'Scan met uw bank app' : 'Scan with your bank app'}\n                  </p>\n                </div>\n                <div style={{ width: '40%', padding: '12px', textAlign: 'center' }}>\n                  <h4 style={{ margin: '0 0 5px 0', color: '#111827', fontSize: '12px', fontWeight: 600 }}>\n                    {language === 'nl' ? 'Handmatig Overmaken' : 'Manual Transfer'}\n                  </h4>\n                  <div style={{ fontSize: '11px' }}>\n                    <div style={{ display: 'flex', justifyContent: 'space-between', padding: '2px 0' }}>\n                      <span style={{ fontWeight: 600, color: '#111827' }}>IBAN:</span>\n                      <span style={{ color: '#4b5563' }}>{data.payment.iban}</span>\n                    </div>\n                    <div style={{ display: 'flex', justifyContent: 'space-between', padding: '2px 0' }}>\n                      <span style={{ fontWeight: 600, color: '#111827' }}>\n                        {language === 'nl' ? 'T.n.v.:' : 'To:'}\n                      </span>\n                      <span style={{ color: '#4b5563' }}>{data.payment.accountName}</span>\n                    </div>\n                    <div style={{ display: 'flex', justifyContent: 'space-between', padding: '2px 0' }}>\n                      <span style={{ fontWeight: 600, color: '#111827' }}>\n                        {language === 'nl' ? 'O.v.v.:' : 'Ref:'}\n                      </span>\n                      <span style={{ color: '#3b82f6', fontWeight: 600 }}>{data.quote.number}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Contact */}\n            <div style={{ \n              backgroundColor: '#ecfdf5', \n              borderRadius: '8px', \n              margin: '25px 0', \n              borderLeft: '3px solid #10b981',\n              padding: '15px'\n            }}>\n              <h3 style={{ margin: '0 0 10px 0', color: '#065f46', fontSize: '14px', fontWeight: 600, textAlign: 'center' }}>\n                {data.contactTitle}\n              </h3>\n              <p style={{ fontSize: '12px', color: '#065f46', lineHeight: 1.5, margin: '0 0 12px 0', textAlign: 'center' }}>\n                {data.contactDescription}\n              </p>\n              <div style={{ display: 'flex', justifyContent: 'space-around', textAlign: 'center' }}>\n                <div>\n                  <div style={{ fontWeight: 600, marginBottom: '3px', fontSize: '12px', color: '#065f46' }}>\n                    {language === 'nl' ? 'Telefoon' : 'Phone'}\n                  </div>\n                  <div style={{ color: '#065f46', fontWeight: 600, fontSize: '12px' }}>\n                    {data.company.phone}\n                  </div>\n                </div>\n                <div>\n                  <div style={{ fontWeight: 600, marginBottom: '3px', fontSize: '12px', color: '#065f46' }}>\n                    Email\n                  </div>\n                  <div style={{ color: '#065f46', fontWeight: 600, fontSize: '12px' }}>\n                    {data.company.email}\n                  </div>\n                </div>\n                <div>\n                  <div style={{ fontWeight: 600, marginBottom: '3px', fontSize: '12px', color: '#065f46' }}>\n                    WhatsApp\n                  </div>\n                  <div style={{ color: '#065f46', fontWeight: 600, fontSize: '12px' }}>\n                    {data.company.phone}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Footer */}\n          <div style={{ backgroundColor: '#111827', color: '#9ca3af', padding: '20px', textAlign: 'center', fontSize: '12px', lineHeight: 1.5 }}>\n            <div style={{ color: '#ffffff', fontWeight: 600, marginBottom: '5px', fontSize: '14px' }}>\n              {data.company.name}\n            </div>\n            <div>\n              KvK: {data.company.kvk} | BTW: {data.company.btw}<br />\n              {data.company.address}, {data.company.city}<br />\n              <span style={{ color: '#60a5fa', fontWeight: 600 }}>{data.company.website}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AASO,SAAS,gBAAgB,EAAE,IAAI,EAAE,QAAQ,EAAwB;IACtE,yDAAyD;IACzD,MAAM,WAAW,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;IAEpE,oBAAoB;IACpB,MAAM,YAAY,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK;QACxC,MAAM,YAAY,KAAK,KAAK,GAAG,KAAK,OAAO,GAAG;QAC9C,IAAI,CAAC,GAAG,CAAC,KAAK,OAAO,CAAC,EAAE;YACtB,GAAG,CAAC,KAAK,OAAO,CAAC,GAAG;QACtB;QACA,GAAG,CAAC,KAAK,OAAO,CAAC,IAAI;QACrB,OAAO;IACT,GAAG,CAAC;IAEJ,MAAM,iBAAiB,OAAO,MAAM,CAAC,WAAW,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK;IAChF,MAAM,QAAQ,WAAW;IACzB,MAAM,gBAAgB,QAAQ,CAAC,KAAK,OAAO,CAAC,iBAAiB,GAAG,GAAG;IACnE,MAAM,kBAAkB,QAAQ;IAChC,MAAM,qBAAqB,QAAQ,CAAC,KAAK,OAAO,CAAC,sBAAsB,GAAG,GAAG;IAC7E,MAAM,wBAAwB,QAAQ;IAEtC,iEAAiE;IACjE,MAAM,cAAc,KAAK,OAAO,CAAC,WAAW,IAAI;IAEhD,yCAAyC;IACzC,MAAM,iBAAiB;QACrB,OAAO,CAAC,4DAA4D,EAAE,mBAAmB,cAAc;IACzG;IAEA,iCAAiC;IACjC,MAAM,uBAAuB,CAAC;QAC5B,IAAI,SAAS;QAEb,kDAAkD;QAClD,IAAI,KAAK,QAAQ,CAAC,wBAAwB,KAAK,QAAQ,CAAC,iBAAiB;YACvE,yCAAyC;YAEzC,mDAAmD;YACnD,SAAS,OAAO,OAAO,CAAC,sCAAsC,CAAC,IAAI,EAAE,MAAM,OAAO,CAAC,IAAI;YACvF,SAAS,OAAO,OAAO,CAAC,qCAAqC,CAAC,IAAI,EAAE,MAAM,OAAO,CAAC,IAAI;YAEtF,8CAA8C;YAC9C,SAAS,OAAO,OAAO,CAAC,+BAA+B,CAAC,IAAI,EAAE,mBAAmB,OAAO,CAAC,IAAI;YAC7F,SAAS,OAAO,OAAO,CAAC,4BAA4B,CAAC,IAAI,EAAE,mBAAmB,OAAO,CAAC,IAAI;YAE1F,8BAA8B;YAC9B,SAAS,OAAO,OAAO,CAAC,oEAAoE,CAAC,EAAE,EAAE,mBAAmB,OAAO,CAAC,IAAI;YAChI,SAAS,OAAO,OAAO,CAAC,yDAAyD,CAAC,EAAE,EAAE,mBAAmB,OAAO,CAAC,IAAI;QACvH;QAEA,+CAA+C;QAC/C,IAAI,KAAK,QAAQ,CAAC,sBAAsB,KAAK,QAAQ,CAAC,eAAe;YACnE,iDAAiD;YACjD,SAAS,OAAO,OAAO,CAAC,8CAA8C,CAAC,IAAI,EAAE,cAAc,OAAO,CAAC,IAAI;YACvG,SAAS,OAAO,OAAO,CAAC,yCAAyC,CAAC,IAAI,EAAE,cAAc,OAAO,CAAC,IAAI;YAElG,4CAA4C;YAC5C,SAAS,OAAO,OAAO,CAAC,2BAA2B,CAAC,IAAI,EAAE,KAAK,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI;YAC9F,SAAS,OAAO,OAAO,CAAC,wBAAwB,CAAC,IAAI,EAAE,KAAK,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI;YAE3F,mDAAmD;YACnD,IAAI,CAAC,OAAO,QAAQ,CAAC,gBAAgB,CAAC,OAAO,QAAQ,CAAC,cAAc;gBAClE,SAAS,OAAO,OAAO,CAAC,sBAAsB;YAChD;QACF;QAEA,wDAAwD;QACxD,IAAI,KAAK,QAAQ,CAAC,aAAa,KAAK,QAAQ,CAAC,UAAU,KAAK,QAAQ,CAAC,iBAAiB;YACpF,SAAS,OAAO,OAAO,CAAC,gBAAgB,CAAC,OAAO;gBAC9C,MAAM,cAAc,KAAK,SAAS,CAAC,GAAG;gBAEtC,2CAA2C;gBAC3C,IAAI,YAAY,QAAQ,CAAC,aAAa,YAAY,QAAQ,CAAC,QAAQ;oBACjE,OAAO,CAAC,EAAE,EAAE,cAAc,OAAO,CAAC,IAAI;gBACxC;gBACA,6CAA6C;gBAC7C,IAAI,YAAY,QAAQ,CAAC,mBAAmB,YAAY,QAAQ,CAAC,kBAAkB;oBACjF,OAAO,CAAC,EAAE,EAAE,gBAAgB,OAAO,CAAC,IAAI;gBAC1C;gBACA,OAAO;YACT;QACF;QAEA,wCAAwC;QACxC,SAAS,OAAO,OAAO,CAAC,WAAW,CAAC,OAAO;YACzC,IAAI,KAAK,QAAQ,CAAC,kBAAkB,KAAK,QAAQ,CAAC,YAAY;gBAC5D,OAAO,GAAG,KAAK,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC;YAC7C;YACA,IAAI,KAAK,QAAQ,CAAC,cAAc,KAAK,QAAQ,CAAC,aAAa;gBACzD,OAAO,GAAG,KAAK,OAAO,CAAC,sBAAsB,CAAC,CAAC,CAAC;YAClD;YACA,OAAO;QACT;QAEA,mCAAmC;QACnC,MAAM,kBAAkB;QACxB,SAAS,OAAO,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,MAAM;QAE1D,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,OAAO;gBAAE,iBAAiB;gBAAW,SAAS;YAAY;sBAC7D,cAAA,8OAAC;gBAAI,OAAO;oBACV,UAAU;oBACV,QAAQ;oBACR,iBAAiB;oBACjB,cAAc;oBACd,UAAU;oBACV,WAAW;gBACb;;kCAGE,8OAAC;wBAAI,OAAO;4BAAE,iBAAiB;4BAAW,OAAO;4BAAW,SAAS;wBAAY;kCAC/E,cAAA,8OAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,gBAAgB;gCAAiB,YAAY;4BAAS;;8CACnF,8OAAC;;sDACC,8OAAC;4CAAG,OAAO;gDAAE,QAAQ;gDAAG,UAAU;gDAAQ,YAAY;gDAAK,OAAO;gDAAW,eAAe;4CAAM;sDAC/F,KAAK,KAAK;;;;;;sDAEb,8OAAC;4CAAE,OAAO;gDAAE,QAAQ;gDAAa,UAAU;gDAAQ,OAAO;gDAAW,YAAY;4CAAI;sDAClF,KAAK,WAAW;;;;;;;;;;;;8CAGrB,8OAAC;oCAAI,OAAO;wCAAE,WAAW;oCAAQ;8CAC/B,cAAA,8OAAC;wCAAI,OAAO;4CACV,YAAY;4CACZ,SAAS;4CACT,cAAc;4CACd,SAAS;4CACT,WAAW;wCACb;kDACE,cAAA,8OAAC;4CAAI,OAAO;gDACV,iBAAiB;gDACjB,OAAO;gDACP,SAAS;gDACT,cAAc;gDACd,UAAU;gDACV,YAAY;gDACZ,eAAe;4CACjB;sDACG,KAAK,KAAK,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ5B,8OAAC;wBAAI,OAAO;4BAAE,SAAS;wBAAO;;0CAG5B,8OAAC;gCAAI,OAAO;oCAAE,SAAS;oCAAQ,KAAK;oCAAM,cAAc;gCAAO;;kDAC7D,8OAAC;wCAAI,OAAO;4CACV,OAAO;4CACP,iBAAiB;4CACjB,SAAS;4CACT,cAAc;4CACd,YAAY;wCACd;;0DACE,8OAAC;gDAAG,OAAO;oDAAE,QAAQ;oDAAc,OAAO;oDAAW,UAAU;oDAAQ,YAAY;gDAAI;0DACpF,aAAa,OAAO,kBAAkB;;;;;;0DAEzC,8OAAC;gDAAI,OAAO;oDAAE,UAAU;oDAAQ,OAAO;oDAAW,YAAY;gDAAI;;kEAChE,8OAAC;wDAAO,OAAO;4DAAE,OAAO;wDAAU;kEAAI,KAAK,MAAM,CAAC,IAAI;;;;;;kEAAU,8OAAC;;;;;oDAChE,KAAK,MAAM,CAAC,OAAO;kEAAC,8OAAC;;;;;oDACrB,KAAK,MAAM,CAAC,IAAI;;;;;;;;;;;;;kDAIrB,8OAAC;wCAAI,OAAO;4CACV,OAAO;4CACP,iBAAiB;4CACjB,SAAS;4CACT,cAAc;4CACd,YAAY;wCACd;;0DACE,8OAAC;gDAAG,OAAO;oDAAE,QAAQ;oDAAc,OAAO;oDAAW,UAAU;oDAAQ,YAAY;gDAAI;0DACpF,aAAa,OAAO,oBAAoB;;;;;;0DAE3C,8OAAC;gDAAI,OAAO;oDAAE,UAAU;oDAAQ,OAAO;oDAAW,YAAY;gDAAI;;kEAChE,8OAAC;wDAAO,OAAO;4DAAE,OAAO;wDAAU;kEAC/B,aAAa,OAAO,YAAY;;;;;;oDAC1B;oDAAE,KAAK,KAAK,CAAC,MAAM;kEAAC,8OAAC;;;;;kEAC9B,8OAAC;wDAAO,OAAO;4DAAE,OAAO;wDAAU;kEAC/B,aAAa,OAAO,WAAW;;;;;;oDACzB;oDAAE,KAAK,KAAK,CAAC,IAAI;kEAAC,8OAAC;;;;;kEAC5B,8OAAC;wDAAO,OAAO;4DAAE,OAAO;wDAAU;kEAC/B,aAAa,OAAO,gBAAgB;;;;;;oDAC9B;oDAAE,KAAK,KAAK,CAAC,UAAU;;;;;;;;;;;;;;;;;;;0CAMtC,8OAAC;gCAAI,OAAO;oCACV,iBAAiB;oCACjB,cAAc;oCACd,QAAQ;oCACR,YAAY;oCACZ,SAAS;gCACX;;kDACE,8OAAC;wCAAE,OAAO;4CAAE,QAAQ;4CAAa,UAAU;4CAAQ,OAAO;4CAAW,YAAY;wCAAI;kDAClF,KAAK,QAAQ;;;;;;kDAEhB,8OAAC;wCAAE,OAAO;4CAAE,QAAQ;4CAAG,UAAU;4CAAQ,OAAO;4CAAW,YAAY;wCAAI;kDACxE,KAAK,YAAY;;;;;;;;;;;;0CAKtB,8OAAC;gCAAI,OAAO;oCAAE,QAAQ;gCAAS;;kDAC7B,8OAAC;wCAAG,OAAO;4CACT,OAAO;4CACP,UAAU;4CACV,YAAY;4CACZ,QAAQ;4CACR,WAAW;4CACX,eAAe;4CACf,eAAe;4CACf,cAAc;4CACd,eAAe;wCACjB;kDACG,aAAa,OAAO,oBAAoB;;;;;;kDAG3C,8OAAC;wCAAM,OAAO;4CACZ,OAAO;4CACP,gBAAgB;4CAChB,cAAc;4CACd,UAAU;4CACV,WAAW;wCACb;;0DAEE,8OAAC;0DACC,cAAA,8OAAC;;sEACC,8OAAC;4DAAG,OAAO;gEAAE,iBAAiB;gEAAW,OAAO;gEAAW,SAAS;gEAAY,WAAW;gEAAU,UAAU;gEAAQ,YAAY;gEAAK,eAAe;gEAAS,eAAe;4DAAY;sEAAG;;;;;;sEAC9L,8OAAC;4DAAG,OAAO;gEAAE,iBAAiB;gEAAW,OAAO;gEAAW,SAAS;gEAAa,WAAW;gEAAQ,UAAU;gEAAQ,YAAY;gEAAK,eAAe;gEAAS,eAAe;4DAAY;sEACvL,aAAa,OAAO,iBAAiB;;;;;;sEAExC,8OAAC;4DAAG,OAAO;gEAAE,iBAAiB;gEAAW,OAAO;gEAAW,SAAS;gEAAY,WAAW;gEAAU,UAAU;gEAAQ,YAAY;gEAAK,eAAe;gEAAS,eAAe;4DAAY;sEACxL,aAAa,OAAO,WAAW;;;;;;sEAElC,8OAAC;4DAAG,OAAO;gEAAE,iBAAiB;gEAAW,OAAO;gEAAW,SAAS;gEAAY,WAAW;gEAAU,UAAU;gEAAQ,YAAY;gEAAK,eAAe;gEAAS,eAAe;4DAAY;sEACxL,aAAa,OAAO,UAAU;;;;;;sEAEjC,8OAAC;4DAAG,OAAO;gEAAE,iBAAiB;gEAAW,OAAO;gEAAW,SAAS;gEAAa,WAAW;gEAAS,UAAU;gEAAQ,YAAY;gEAAK,eAAe;gEAAS,eAAe;4DAAY;sEACxL,aAAa,OAAO,UAAU;;;;;;sEAEjC,8OAAC;4DAAG,OAAO;gEAAE,iBAAiB;gEAAW,OAAO;gEAAW,SAAS;gEAAY,WAAW;gEAAU,UAAU;gEAAQ,YAAY;gEAAK,eAAe;gEAAS,eAAe;4DAAY;sEACxL,aAAa,OAAO,QAAQ;;;;;;sEAE/B,8OAAC;4DAAG,OAAO;gEAAE,iBAAiB;gEAAW,OAAO;gEAAW,SAAS;gEAAa,WAAW;gEAAS,UAAU;gEAAQ,YAAY;gEAAK,eAAe;gEAAS,eAAe;4DAAY;sEACxL,aAAa,OAAO,WAAW;;;;;;;;;;;;;;;;;0DAMtC,8OAAC;0DACE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC;wDAAiB,OAAO;4DAAE,iBAAiB,QAAQ,MAAM,IAAI,YAAY;wDAAU;;0EAClF,8OAAC;gEAAG,OAAO;oEAAE,SAAS;oEAAY,cAAc;oEAAqB,WAAW;oEAAU,UAAU;oEAAQ,OAAO;gEAAU;0EAC1H,KAAK,UAAU;;;;;;0EAElB,8OAAC;gEAAG,OAAO;oEAAE,SAAS;oEAAa,cAAc;gEAAoB;;kFACnE,8OAAC;wEAAI,OAAO;4EAAE,YAAY;4EAAK,OAAO;4EAAW,UAAU;wEAAO;kFAC/D,KAAK,WAAW;;;;;;kFAEnB,8OAAC;wEAAI,OAAO;4EAAE,OAAO;4EAAW,UAAU;wEAAO;kFAC9C,KAAK,cAAc;;;;;;;;;;;;0EAGxB,8OAAC;gEAAG,OAAO;oEAAE,SAAS;oEAAY,cAAc;oEAAqB,WAAW;oEAAU,UAAU;oEAAQ,OAAO;gEAAU;0EAC1H,KAAK,QAAQ;;;;;;0EAEhB,8OAAC;gEAAG,OAAO;oEAAE,SAAS;oEAAY,cAAc;oEAAqB,WAAW;oEAAU,UAAU;oEAAQ,OAAO;gEAAU;0EAC1H,KAAK,IAAI;;;;;;0EAEZ,8OAAC;gEAAG,OAAO;oEAAE,SAAS;oEAAa,cAAc;oEAAqB,WAAW;oEAAS,UAAU;oEAAQ,OAAO;gEAAU;0EAC3H,cAAA,8OAAC;oEAAK,OAAO;wEAAE,YAAY;wEAAa,YAAY;oEAAS;;wEAAG;wEAAQ,KAAK,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;0EAE7F,8OAAC;gEAAG,OAAO;oEAAE,SAAS;oEAAY,cAAc;oEAAqB,WAAW;oEAAU,UAAU;oEAAQ,OAAO;gEAAU;;oEAC1H,KAAK,OAAO;oEAAC;;;;;;;0EAEhB,8OAAC;gEAAG,OAAO;oEAAE,SAAS;oEAAa,cAAc;oEAAqB,WAAW;oEAAS,YAAY;oEAAK,OAAO;oEAAW,UAAU;gEAAO;0EAC5I,cAAA,8OAAC;oEAAK,OAAO;wEAAE,YAAY;wEAAa,YAAY;oEAAS;;wEAAG;wEAAQ,KAAK,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;;uDAzBtF,KAAK,EAAE;;;;;;;;;;0DAgCpB,8OAAC;;kEACC,8OAAC;wDAAG,OAAO;4DAAE,iBAAiB;wDAAU;;0EACtC,8OAAC;gEAAG,SAAS;gEAAG,OAAO;oEAAE,SAAS;oEAAa,WAAW;oEAAS,YAAY;oEAAK,UAAU;oEAAQ,OAAO;oEAAW,eAAe;gEAAQ;0EAC5I,aAAa,OAAO,0BAA0B;;;;;;0EAEjD,8OAAC;gEAAG,OAAO;oEAAE,SAAS;oEAAa,WAAW;oEAAS,YAAY;oEAAK,OAAO;oEAAW,UAAU;gEAAO;0EACzG,cAAA,8OAAC;oEAAK,OAAO;wEAAE,YAAY;wEAAa,YAAY;oEAAS;;wEAAG;wEAAQ,SAAS,OAAO,CAAC;;;;;;;;;;;;;;;;;;oDAG5F,OAAO,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,MAAM,OAAO,iBAC5C,8OAAC;4DAAc,OAAO;gEAAE,iBAAiB;4DAAU;;8EACjD,8OAAC;oEAAG,SAAS;oEAAG,OAAO;wEAAE,SAAS;wEAAY,WAAW;wEAAS,UAAU;wEAAQ,OAAO;oEAAU;8EAClG,aAAa,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC;;;;;;8EAE1D,8OAAC;oEAAG,OAAO;wEAAE,SAAS;wEAAY,WAAW;wEAAS,YAAY;wEAAK,OAAO;wEAAW,UAAU;oEAAO;8EACxG,cAAA,8OAAC;wEAAK,OAAO;4EAAE,YAAY;4EAAa,YAAY;wEAAS;;4EAAG;4EAAQ,OAAO,OAAO,CAAC;;;;;;;;;;;;;2DALlF;;;;;kEASX,8OAAC;wDAAG,OAAO;4DAAE,YAAY;wDAAmD;;0EAC1E,8OAAC;gEAAG,SAAS;gEAAG,OAAO;oEAAE,SAAS;oEAAQ,WAAW;oEAAS,OAAO;oEAAW,UAAU;oEAAQ,YAAY;oEAAK,eAAe;gEAAQ;0EACvI,aAAa,OAAO,qBAAqB;;;;;;0EAE5C,8OAAC;gEAAG,OAAO;oEAAE,SAAS;oEAAa,WAAW;oEAAS,OAAO;oEAAW,UAAU;oEAAQ,YAAY;gEAAI;0EACzG,cAAA,8OAAC;oEAAK,OAAO;wEAAE,YAAY;wEAAa,YAAY;oEAAS;;wEAAG;wEAAQ,MAAM,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQ/F,KAAK,KAAK,kBACT,8OAAC;gCAAI,OAAO;oCACV,iBAAiB;oCACjB,cAAc;oCACd,cAAc;oCACd,YAAY;oCACZ,SAAS;gCACX;;kDACE,8OAAC;wCAAG,OAAO;4CAAE,QAAQ;4CAAa,OAAO;4CAAW,UAAU;4CAAQ,YAAY;wCAAI;kDACnF,aAAa,OAAO,gBAAgB;;;;;;kDAEvC,8OAAC;wCAAE,OAAO;4CAAE,UAAU;4CAAQ,OAAO;4CAAW,YAAY;4CAAK,QAAQ;wCAAE;kDACxE,qBAAqB,KAAK,KAAK;;;;;;;;;;;;4BAMrC,KAAK,UAAU,kBACd,8OAAC;gCAAI,OAAO;oCACV,iBAAiB;oCACjB,cAAc;oCACd,cAAc;oCACd,YAAY;oCACZ,SAAS;gCACX;;kDACE,8OAAC;wCAAG,OAAO;4CAAE,QAAQ;4CAAa,OAAO;4CAAW,UAAU;4CAAQ,YAAY;wCAAI;kDACnF,aAAa,OAAO,gBAAgB;;;;;;kDAEvC,8OAAC;wCAAE,OAAO;4CAAE,UAAU;4CAAQ,OAAO;4CAAW,YAAY;4CAAK,QAAQ;wCAAE;kDACxE,qBAAqB,KAAK,UAAU;;;;;;;;;;;;0CAM3C,8OAAC;gCAAI,OAAO;oCACV,iBAAiB;oCACjB,cAAc;oCACd,QAAQ;oCACR,YAAY;oCACZ,SAAS;gCACX;;kDACE,8OAAC;wCAAG,OAAO;4CAAE,QAAQ;4CAAc,OAAO;4CAAW,UAAU;4CAAQ,YAAY;wCAAI;kDACpF,aAAa,OAAO,mBAAmB;;;;;;kDAE1C,8OAAC;wCAAI,OAAO;4CAAE,SAAS;4CAAQ,eAAe;4CAAU,KAAK;wCAAO;;0DAClE,8OAAC;gDAAI,OAAO;oDAAE,SAAS;oDAAQ,YAAY;gDAAa;;kEACtD,8OAAC;wDAAI,OAAO;4DACV,iBAAiB;4DACjB,OAAO;4DACP,OAAO;4DACP,QAAQ;4DACR,cAAc;4DACd,WAAW;4DACX,YAAY;4DACZ,UAAU;4DACV,YAAY;4DACZ,aAAa;4DACb,YAAY;wDACd;kEAAG;;;;;;kEACH,8OAAC;wDAAI,OAAO;4DAAE,UAAU;4DAAQ,OAAO;4DAAW,YAAY;wDAAI;kEAC/D,qBAAqB,KAAK,SAAS,CAAC,KAAK;;;;;;;;;;;;0DAG9C,8OAAC;gDAAI,OAAO;oDAAE,SAAS;oDAAQ,YAAY;gDAAa;;kEACtD,8OAAC;wDAAI,OAAO;4DACV,iBAAiB;4DACjB,OAAO;4DACP,OAAO;4DACP,QAAQ;4DACR,cAAc;4DACd,WAAW;4DACX,YAAY;4DACZ,UAAU;4DACV,YAAY;4DACZ,aAAa;4DACb,YAAY;wDACd;kEAAG;;;;;;kEACH,8OAAC;wDAAI,OAAO;4DAAE,UAAU;4DAAQ,OAAO;4DAAW,YAAY;wDAAI;kEAC/D,qBAAqB,KAAK,SAAS,CAAC,KAAK;;;;;;;;;;;;0DAG9C,8OAAC;gDAAI,OAAO;oDAAE,SAAS;oDAAQ,YAAY;gDAAa;;kEACtD,8OAAC;wDAAI,OAAO;4DACV,iBAAiB;4DACjB,OAAO;4DACP,OAAO;4DACP,QAAQ;4DACR,cAAc;4DACd,WAAW;4DACX,YAAY;4DACZ,UAAU;4DACV,YAAY;4DACZ,aAAa;4DACb,YAAY;wDACd;kEAAG;;;;;;kEACH,8OAAC;wDAAI,OAAO;4DAAE,UAAU;4DAAQ,OAAO;4DAAW,YAAY;wDAAI;kEAC/D,qBAAqB,KAAK,SAAS,CAAC,KAAK;;;;;;;;;;;;0DAG9C,8OAAC;gDAAI,OAAO;oDAAE,SAAS;oDAAQ,YAAY;gDAAa;;kEACtD,8OAAC;wDAAI,OAAO;4DACV,iBAAiB;4DACjB,OAAO;4DACP,OAAO;4DACP,QAAQ;4DACR,cAAc;4DACd,WAAW;4DACX,YAAY;4DACZ,UAAU;4DACV,YAAY;4DACZ,aAAa;4DACb,YAAY;wDACd;kEAAG;;;;;;kEACH,8OAAC;wDAAI,OAAO;4DAAE,UAAU;4DAAQ,OAAO;4DAAW,YAAY;wDAAI;kEAC/D,qBAAqB,KAAK,SAAS,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;0CAOlD,8OAAC;gCAAI,OAAO;oCACV,iBAAiB;oCACjB,cAAc;oCACd,QAAQ;oCACR,YAAY;oCACZ,SAAS;gCACX;;kDACE,8OAAC;wCAAG,OAAO;4CAAE,QAAQ;4CAAc,OAAO;4CAAW,UAAU;4CAAQ,YAAY;4CAAK,WAAW;wCAAS;;4CACzG,aAAa,OAAO,6BAA6B;4CAA0B;4CAAK,cAAc,OAAO,CAAC;4CAAG;;;;;;;kDAG5G,8OAAC;wCAAI,OAAO;4CACV,iBAAiB;4CACjB,cAAc;4CACd,WAAW;4CACX,SAAS;wCACX;;0DACE,8OAAC;gDAAI,OAAO;oDACV,OAAO;oDACP,SAAS;oDACT,WAAW;oDACX,aAAa;gDACf;;kEACE,8OAAC;wDAAG,OAAO;4DAAE,QAAQ;4DAAa,OAAO;4DAAW,UAAU;4DAAQ,YAAY;wDAAI;kEACnF,aAAa,OAAO,mBAAmB;;;;;;kEAE1C,8OAAC;wDACC,MAAM;wDACN,OAAO;4DACL,iBAAiB;4DACjB,OAAO;4DACP,SAAS;4DACT,UAAU;4DACV,YAAY;4DACZ,SAAS;4DACT,cAAc;4DACd,QAAQ;4DACR,gBAAgB;wDAClB;kEAEC,aAAa,OAAO,eAAe;;;;;;kEAEtC,8OAAC;wDAAE,OAAO;4DAAE,QAAQ;4DAAa,UAAU;4DAAQ,OAAO;wDAAU;kEACjE,aAAa,OAAO,kCAAkC;;;;;;;;;;;;0DAG3D,8OAAC;gDAAI,OAAO;oDACV,OAAO;oDACP,SAAS;oDACT,WAAW;oDACX,aAAa;gDACf;;kEACE,8OAAC;wDAAG,OAAO;4DAAE,QAAQ;4DAAa,OAAO;4DAAW,UAAU;4DAAQ,YAAY;wDAAI;kEACnF,aAAa,OAAO,qBAAqB;;;;;;kEAE5C,8OAAC;wDAAI,OAAO;4DACV,QAAQ;4DACR,cAAc;4DACd,SAAS;4DACT,iBAAiB;4DACjB,WAAW;4DACX,OAAO;4DACP,QAAQ;4DACR,QAAQ;4DACR,SAAS;4DACT,YAAY;4DACZ,gBAAgB;wDAClB;kEACG,KAAK,OAAO,CAAC,WAAW,iBACvB,8OAAC;4DACC,KAAK;4DACL,OAAM;4DACN,QAAO;4DACP,KAAI;4DACJ,OAAO;gEAAE,SAAS;4DAAQ;;;;;iFAG5B,8OAAC;4DAAI,OAAO;gEACV,OAAO;gEACP,QAAQ;gEACR,iBAAiB;gEACjB,SAAS;gEACT,YAAY;gEACZ,gBAAgB;gEAChB,UAAU;gEACV,OAAO;4DACT;sEAAG;;;;;;;;;;;kEAGP,8OAAC;wDAAE,OAAO;4DAAE,QAAQ;4DAAa,UAAU;4DAAQ,OAAO;wDAAU;kEACjE,aAAa,OAAO,yBAAyB;;;;;;;;;;;;0DAGlD,8OAAC;gDAAI,OAAO;oDAAE,OAAO;oDAAO,SAAS;oDAAQ,WAAW;gDAAS;;kEAC/D,8OAAC;wDAAG,OAAO;4DAAE,QAAQ;4DAAa,OAAO;4DAAW,UAAU;4DAAQ,YAAY;wDAAI;kEACnF,aAAa,OAAO,wBAAwB;;;;;;kEAE/C,8OAAC;wDAAI,OAAO;4DAAE,UAAU;wDAAO;;0EAC7B,8OAAC;gEAAI,OAAO;oEAAE,SAAS;oEAAQ,gBAAgB;oEAAiB,SAAS;gEAAQ;;kFAC/E,8OAAC;wEAAK,OAAO;4EAAE,YAAY;4EAAK,OAAO;wEAAU;kFAAG;;;;;;kFACpD,8OAAC;wEAAK,OAAO;4EAAE,OAAO;wEAAU;kFAAI,KAAK,OAAO,CAAC,IAAI;;;;;;;;;;;;0EAEvD,8OAAC;gEAAI,OAAO;oEAAE,SAAS;oEAAQ,gBAAgB;oEAAiB,SAAS;gEAAQ;;kFAC/E,8OAAC;wEAAK,OAAO;4EAAE,YAAY;4EAAK,OAAO;wEAAU;kFAC9C,aAAa,OAAO,YAAY;;;;;;kFAEnC,8OAAC;wEAAK,OAAO;4EAAE,OAAO;wEAAU;kFAAI,KAAK,OAAO,CAAC,WAAW;;;;;;;;;;;;0EAE9D,8OAAC;gEAAI,OAAO;oEAAE,SAAS;oEAAQ,gBAAgB;oEAAiB,SAAS;gEAAQ;;kFAC/E,8OAAC;wEAAK,OAAO;4EAAE,YAAY;4EAAK,OAAO;wEAAU;kFAC9C,aAAa,OAAO,YAAY;;;;;;kFAEnC,8OAAC;wEAAK,OAAO;4EAAE,OAAO;4EAAW,YAAY;wEAAI;kFAAI,KAAK,KAAK,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQhF,8OAAC;gCAAI,OAAO;oCACV,iBAAiB;oCACjB,cAAc;oCACd,QAAQ;oCACR,YAAY;oCACZ,SAAS;gCACX;;kDACE,8OAAC;wCAAG,OAAO;4CAAE,QAAQ;4CAAc,OAAO;4CAAW,UAAU;4CAAQ,YAAY;4CAAK,WAAW;wCAAS;kDACzG,KAAK,YAAY;;;;;;kDAEpB,8OAAC;wCAAE,OAAO;4CAAE,UAAU;4CAAQ,OAAO;4CAAW,YAAY;4CAAK,QAAQ;4CAAc,WAAW;wCAAS;kDACxG,KAAK,kBAAkB;;;;;;kDAE1B,8OAAC;wCAAI,OAAO;4CAAE,SAAS;4CAAQ,gBAAgB;4CAAgB,WAAW;wCAAS;;0DACjF,8OAAC;;kEACC,8OAAC;wDAAI,OAAO;4DAAE,YAAY;4DAAK,cAAc;4DAAO,UAAU;4DAAQ,OAAO;wDAAU;kEACpF,aAAa,OAAO,aAAa;;;;;;kEAEpC,8OAAC;wDAAI,OAAO;4DAAE,OAAO;4DAAW,YAAY;4DAAK,UAAU;wDAAO;kEAC/D,KAAK,OAAO,CAAC,KAAK;;;;;;;;;;;;0DAGvB,8OAAC;;kEACC,8OAAC;wDAAI,OAAO;4DAAE,YAAY;4DAAK,cAAc;4DAAO,UAAU;4DAAQ,OAAO;wDAAU;kEAAG;;;;;;kEAG1F,8OAAC;wDAAI,OAAO;4DAAE,OAAO;4DAAW,YAAY;4DAAK,UAAU;wDAAO;kEAC/D,KAAK,OAAO,CAAC,KAAK;;;;;;;;;;;;0DAGvB,8OAAC;;kEACC,8OAAC;wDAAI,OAAO;4DAAE,YAAY;4DAAK,cAAc;4DAAO,UAAU;4DAAQ,OAAO;wDAAU;kEAAG;;;;;;kEAG1F,8OAAC;wDAAI,OAAO;4DAAE,OAAO;4DAAW,YAAY;4DAAK,UAAU;wDAAO;kEAC/D,KAAK,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ7B,8OAAC;wBAAI,OAAO;4BAAE,iBAAiB;4BAAW,OAAO;4BAAW,SAAS;4BAAQ,WAAW;4BAAU,UAAU;4BAAQ,YAAY;wBAAI;;0CAClI,8OAAC;gCAAI,OAAO;oCAAE,OAAO;oCAAW,YAAY;oCAAK,cAAc;oCAAO,UAAU;gCAAO;0CACpF,KAAK,OAAO,CAAC,IAAI;;;;;;0CAEpB,8OAAC;;oCAAI;oCACG,KAAK,OAAO,CAAC,GAAG;oCAAC;oCAAS,KAAK,OAAO,CAAC,GAAG;kDAAC,8OAAC;;;;;oCACjD,KAAK,OAAO,CAAC,OAAO;oCAAC;oCAAG,KAAK,OAAO,CAAC,IAAI;kDAAC,8OAAC;;;;;kDAC5C,8OAAC;wCAAK,OAAO;4CAAE,OAAO;4CAAW,YAAY;wCAAI;kDAAI,KAAK,OAAO,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvF", "debugId": null}}, {"offset": {"line": 3124, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/offerte%20email%20template%20editor%20/offerte-template-editor/src/components/CodeGenerator.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Copy, Download } from 'lucide-react';\nimport { TemplateData, Language } from '@/types/template';\n\ninterface CodeGeneratorProps {\n  data: TemplateData;\n  language: Language;\n}\n\nexport function CodeGenerator({ data, language }: CodeGeneratorProps) {\n  const [copied, setCopied] = useState(false);\n\n  // Calculate totals - items.total should be excluding VAT\n  const subtotal = data.items.reduce((sum, item) => sum + item.total, 0);\n\n  // Group VAT by rate\n  const vatByRate = data.items.reduce((acc, item) => {\n    const vatAmount = item.total * item.vatRate / 100;\n    if (!acc[item.vatRate]) {\n      acc[item.vatRate] = 0;\n    }\n    acc[item.vatRate] += vatAmount;\n    return acc;\n  }, {} as Record<number, number>);\n\n  const totalVatAmount = Object.values(vatByRate).reduce((sum, vat) => sum + vat, 0);\n  const total = subtotal + totalVatAmount;\n  const depositAmount = total * (data.payment.depositPercentage / 100);\n  const remainingAmount = total - depositAmount;\n  const cashDiscountAmount = total * (data.payment.cashDiscountPercentage / 100);\n  const totalWithCashDiscount = total - cashDiscountAmount;\n\n  // Payment link logic - use default if empty, no extra parameters\n  const paymentLink = data.payment.paymentLink || 'www.asklussen.nl';\n\n  // Replace dynamic values in text\n  const replaceDynamicValues = (text: string) => {\n    let result = text;\n\n    // OPMERKINGEN: Replace cash discount calculations\n    if (text.includes('contante betaling') || text.includes('cash payment')) {\n      // Replace specific patterns step by step\n\n      // 1. Replace total amount after \"totaalbedrag van\"\n      result = result.replace(/(totaalbedrag van\\s+)€\\s*[\\d.,]+/gi, `$1€ ${total.toFixed(2)}`);\n      result = result.replace(/(total amount of\\s+)€\\s*[\\d.,]+/gi, `$1€ ${total.toFixed(2)}`);\n\n      // 2. Replace savings amount after \"besparing\"\n      result = result.replace(/(besparing\\s+)€\\s*[\\d.,]+/gi, `$1€ ${cashDiscountAmount.toFixed(2)}`);\n      result = result.replace(/(saving\\s+)€\\s*[\\d.,]+/gi, `$1€ ${cashDiscountAmount.toFixed(2)}`);\n\n      // 3. Replace placeholder text\n      result = result.replace(/hier moet %?5 van bedraag van offerte incl btw hier plaatsen €/gi, `€ ${cashDiscountAmount.toFixed(2)}`);\n      result = result.replace(/here must %?5 of quote amount incl vat here place €/gi, `€ ${cashDiscountAmount.toFixed(2)}`);\n    }\n\n    // VOORWAARDEN: Replace deposit and hourly rate\n    if (text.includes('aanbetaling van') || text.includes('deposit of')) {\n      // Replace deposit amount after \"aanbetaling van\"\n      result = result.replace(/(aanbetaling van\\s+\\d+%\\s+\\()€\\s*[\\d.,]+/gi, `$1€ ${depositAmount.toFixed(2)}`);\n      result = result.replace(/(deposit of\\s+\\d+%\\s+\\()€\\s*[\\d.,]+/gi, `$1€ ${depositAmount.toFixed(2)}`);\n\n      // Replace hourly rate after \"tegen\" or \"at\"\n      result = result.replace(/(tegen\\s+)€\\s*[\\d.,]+/gi, `$1€ ${data.payment.hourlyRate.toFixed(2)}`);\n      result = result.replace(/(at\\s+)€\\s*[\\d.,]+/gi, `$1€ ${data.payment.hourlyRate.toFixed(2)}`);\n\n      // Add \"excl. BTW\" after hourly rate if not present\n      if (!result.includes('excl. BTW') && !result.includes('excl. VAT')) {\n        result = result.replace(/(€\\s*[\\d.,]+\\/uur)/, '$1 excl. BTW');\n      }\n    }\n\n    // VERVOLGSTAPPEN: Replace deposit and remaining amounts\n    if (text.includes('Betaal') || text.includes('Pay') || text.includes('Restbetaling')) {\n      result = result.replace(/€\\s*[\\d.,]+/g, (match, offset) => {\n        const beforeMatch = text.substring(0, offset);\n\n        // Deposit payment (30% of total incl. BTW)\n        if (beforeMatch.includes('Betaal') || beforeMatch.includes('Pay')) {\n          return `€ ${depositAmount.toFixed(2)}`;\n        }\n        // Remaining payment (70% of total incl. BTW)\n        if (beforeMatch.includes('Restbetaling') || beforeMatch.includes('Final Payment')) {\n          return `€ ${remainingAmount.toFixed(2)}`;\n        }\n        return match;\n      });\n    }\n\n    // Replace percentage values dynamically\n    result = result.replace(/(\\d+)%/g, (match, percentage) => {\n      if (text.includes('aanbetaling') || text.includes('deposit')) {\n        return `${data.payment.depositPercentage}%`;\n      }\n      if (text.includes('korting') || text.includes('discount')) {\n        return `${data.payment.cashDiscountPercentage}%`;\n      }\n      return match;\n    });\n\n    // Replace quote numbers everywhere\n    const oldQuotePattern = /OFF-2025-\\d+|QUO-2025-\\d+/g;\n    result = result.replace(oldQuotePattern, data.quote.number);\n\n    return result;\n  };\n\n  const generateHtmlCode = () => {\n    return `<!DOCTYPE html>\n<html lang=\"${language}\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <meta name=\"color-scheme\" content=\"light\">\n    <meta name=\"supported-color-schemes\" content=\"light\">\n    <meta name=\"format-detection\" content=\"telephone=yes\">\n    <meta name=\"x-apple-disable-message-reformatting\">\n    <meta name=\"apple-mobile-web-app-capable\" content=\"yes\">\n    <meta name=\"apple-mobile-web-app-status-bar-style\" content=\"black\">\n    <meta name=\"msapplication-TileColor\" content=\"#0ea5e9\">\n    <meta name=\"theme-color\" content=\"#0ea5e9\">\n    <title>${data.title} ${data.quote.number} | ${data.companyName}</title>\n    <!--[if mso]>\n    <noscript>\n        <xml>\n            <o:OfficeDocumentSettings>\n                <o:PixelsPerInch>96</o:PixelsPerInch>\n            </o:OfficeDocumentSettings>\n        </xml>\n    </noscript>\n    <![endif]-->\n    <!--[if !mso]><!------>\n    <link href=\"https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap\" rel=\"stylesheet\" type=\"text/css\">\n    <!--<![endif]-->\n</head>\n<body style=\"margin: 0; padding: 0; font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; background-color: #f3f4f6; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;\">\n    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"background-color: #f3f4f6;\">\n        <tr>\n            <td align=\"center\" style=\"padding: 25px 10px;\">\n                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"580\" style=\"max-width: 580px; background-color: #ffffff; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 15px rgba(0,0,0,0.08);\">\n                    \n                    <!-- Modern Header -->\n                    <tr>\n                        <td style=\"background-color: #111827; color: #ffffff; padding: 30px 25px;\">\n                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\">\n                                <tr>\n                                    <td style=\"vertical-align: middle;\">\n                                        <h1 style=\"margin: 0; font-size: 32px; font-weight: 800; color: #ffffff; letter-spacing: 1px;\">${data.title}</h1>\n                                        <p style=\"margin: 8px 0 0 0; font-size: 15px; color: #d1d5db; font-weight: 500;\">${data.companyName}</p>\n                                    </td>\n                                    <td style=\"text-align: right; vertical-align: middle;\">\n                                        <div style=\"background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); padding: 3px; border-radius: 8px; display: inline-block; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\">\n                                            <div style=\"background-color: #1e40af; color: #ffffff; padding: 10px 18px; border-radius: 6px; font-size: 15px; font-weight: 700; letter-spacing: 0.5px; text-align: center;\">\n                                                ${data.quote.number}\n                                            </div>\n                                        </div>\n                                    </td>\n                                </tr>\n                            </table>\n                        </td>\n                    </tr>\n                    \n                    <!-- Content -->\n                    <tr>\n                        <td style=\"padding: 25px;\">\n                            \n                            <!-- Client & Quote Info -->\n                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"margin-bottom: 25px;\">\n                                <tr>\n                                    <td width=\"48%\" style=\"background-color: #f9fafb; padding: 15px; border-radius: 8px; vertical-align: top; border-left: 3px solid #3b82f6;\">\n                                        <h3 style=\"margin: 0 0 10px 0; color: #111827; font-size: 14px; font-weight: 600;\">${language === 'nl' ? 'OPDRACHTGEVER' : 'CLIENT'}</h3>\n                                        <p style=\"margin: 0; font-size: 13px; color: #4b5563; line-height: 1.5;\">\n                                            <strong style=\"color: #111827;\">${data.client.name}</strong><br>\n                                            ${data.client.address}<br>\n                                            ${data.client.city}\n                                        </p>\n                                    </td>\n                                    <td width=\"4%\"></td>\n                                    <td width=\"48%\" style=\"background-color: #f9fafb; padding: 15px; border-radius: 8px; vertical-align: top; border-left: 3px solid #f59e0b;\">\n                                        <h3 style=\"margin: 0 0 10px 0; color: #111827; font-size: 14px; font-weight: 600;\">${language === 'nl' ? 'OFFERTE DETAILS' : 'QUOTE DETAILS'}</h3>\n                                        <p style=\"margin: 0; font-size: 13px; color: #4b5563; line-height: 1.5;\">\n                                            <strong style=\"color: #111827;\">${language === 'nl' ? 'Nummer:' : 'Number:'}</strong> ${data.quote.number}<br>\n                                            <strong style=\"color: #111827;\">${language === 'nl' ? 'Datum:' : 'Date:'}</strong> ${data.quote.date}<br>\n                                            <strong style=\"color: #111827;\">${language === 'nl' ? 'Geldig tot:' : 'Valid until:'}</strong> ${data.quote.validUntil}\n                                        </p>\n                                    </td>\n                                </tr>\n                            </table>\n\n                            <!-- Introduction -->\n                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"background-color: #eff6ff; border-radius: 8px; margin: 25px 0; border-left: 3px solid #3b82f6;\">\n                                <tr>\n                                    <td style=\"padding: 15px;\">\n                                        <p style=\"margin: 0 0 8px 0; font-size: 14px; color: #1e40af; font-weight: 600;\">${data.greeting}</p>\n                                        <p style=\"margin: 0; font-size: 13px; color: #1e40af; line-height: 1.6;\">\n                                            ${data.introduction}\n                                        </p>\n                                    </td>\n                                </tr>\n                            </table>\n\n                            <!-- Cost Breakdown -->\n                            <div style=\"margin: 30px 0;\">\n                                <h2 style=\"color: #0c4a6e; font-size: 16px; font-weight: 700; margin: 0 0 12px 0; text-align: center; text-transform: uppercase; letter-spacing: 0.5px; border-bottom: 2px solid #0ea5e9; padding-bottom: 8px;\">\n                                    ${language === 'nl' ? 'Kostenoverzicht' : 'Cost Overview'}\n                                </h2>\n                                \n                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"border-collapse: collapse; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.08);\">\n                                    <!-- Header -->\n                                    <thead>\n                                        <tr>\n                                            <th style=\"background-color: #0c4a6e; color: #ffffff; padding: 10px 6px; text-align: center; font-size: 11px; font-weight: 600; letter-spacing: 0.5px; text-transform: uppercase;\">#</th>\n                                            <th style=\"background-color: #0c4a6e; color: #ffffff; padding: 10px 12px; text-align: left; font-size: 11px; font-weight: 600; letter-spacing: 0.5px; text-transform: uppercase;\">${language === 'nl' ? 'Omschrijving' : 'Description'}</th>\n                                            <th style=\"background-color: #0c4a6e; color: #ffffff; padding: 10px 6px; text-align: center; font-size: 11px; font-weight: 600; letter-spacing: 0.5px; text-transform: uppercase;\">${language === 'nl' ? 'Aantal' : 'Qty'}</th>\n                                            <th style=\"background-color: #0c4a6e; color: #ffffff; padding: 10px 6px; text-align: center; font-size: 11px; font-weight: 600; letter-spacing: 0.5px; text-transform: uppercase;\">${language === 'nl' ? 'Eenh.' : 'Unit'}</th>\n                                            <th style=\"background-color: #0c4a6e; color: #ffffff; padding: 10px 10px; text-align: right; font-size: 11px; font-weight: 600; letter-spacing: 0.5px; text-transform: uppercase;\">${language === 'nl' ? 'Prijs' : 'Price'}</th>\n                                            <th style=\"background-color: #0c4a6e; color: #ffffff; padding: 10px 6px; text-align: center; font-size: 11px; font-weight: 600; letter-spacing: 0.5px; text-transform: uppercase;\">${language === 'nl' ? 'BTW' : 'VAT'}</th>\n                                            <th style=\"background-color: #0c4a6e; color: #ffffff; padding: 10px 10px; text-align: right; font-size: 11px; font-weight: 600; letter-spacing: 0.5px; text-transform: uppercase;\">${language === 'nl' ? 'Totaal' : 'Total'}</th>\n                                        </tr>\n                                    </thead>\n                                    <!-- Items -->\n                                    <tbody>\n${data.items.map((item, index) => `                                        <tr style=\"background-color: ${index % 2 === 0 ? '#ffffff' : '#f0f9ff'};\">\n                                            <td style=\"padding: 10px 6px; border-bottom: 1px solid #e5e7eb; text-align: center; font-size: 12px; color: #6b7280; vertical-align: middle;\">${item.itemNumber}</td>\n                                            <td style=\"padding: 10px 12px; border-bottom: 1px solid #e5e7eb; vertical-align: middle;\">\n                                                <div style=\"font-weight: 600; color: #0c4a6e; font-size: 12px;\">${item.description}</div>\n                                                <div style=\"color: #6b7280; font-size: 10px;\">${item.subDescription}</div>\n                                            </td>\n                                            <td style=\"padding: 10px 6px; border-bottom: 1px solid #e5e7eb; text-align: center; font-size: 12px; color: #4b5563; vertical-align: middle;\">${item.quantity}</td>\n                                            <td style=\"padding: 10px 6px; border-bottom: 1px solid #e5e7eb; text-align: center; font-size: 12px; color: #4b5563; vertical-align: middle;\">${item.unit}</td>\n                                            <td style=\"padding: 10px 10px; border-bottom: 1px solid #e5e7eb; text-align: right; font-size: 12px; color: #4b5563; vertical-align: middle;\"><span style=\"font-family: monospace; white-space: nowrap;\">€&nbsp;${item.price.toFixed(2)}</span></td>\n                                            <td style=\"padding: 10px 6px; border-bottom: 1px solid #e5e7eb; text-align: center; font-size: 12px; color: #4b5563; vertical-align: middle;\">${item.vatRate}%</td>\n                                            <td style=\"padding: 10px 10px; border-bottom: 1px solid #e5e7eb; text-align: right; font-weight: 600; color: #0c4a6e; font-size: 12px; vertical-align: middle;\"><span style=\"font-family: monospace; white-space: nowrap;\">€&nbsp;${item.total.toFixed(2)}</span></td>\n                                        </tr>`).join('\\n')}\n                                    </tbody>\n                                    <!-- Totals -->\n                                    <tfoot>\n                                        <tr style=\"background-color: #f0f9ff;\">\n                                            <td colspan=\"6\" style=\"padding: 10px 12px; text-align: right; font-weight: 600; font-size: 12px; color: #0c4a6e; letter-spacing: 0.3px;\">${language === 'nl' ? 'Subtotaal (excl. BTW)' : 'Subtotal (excl. VAT)'}</td>\n                                            <td style=\"padding: 10px 10px; text-align: right; font-weight: 600; color: #0c4a6e; font-size: 12px;\"><span style=\"font-family: monospace; white-space: nowrap;\">€&nbsp;${subtotal.toFixed(2)}</span></td>\n                                        </tr>\n${Object.entries(vatByRate).map(([rate, amount]) => `                                        <tr style=\"background-color: #f0f9ff;\">\n                                            <td colspan=\"6\" style=\"padding: 6px 12px; text-align: right; font-size: 12px; color: #4b5563;\">${language === 'nl' ? `BTW (${rate}%)` : `VAT (${rate}%)`}</td>\n                                            <td style=\"padding: 6px 10px; text-align: right; font-weight: 600; color: #4b5563; font-size: 12px;\"><span style=\"font-family: monospace; white-space: nowrap;\">€&nbsp;${amount.toFixed(2)}</span></td>\n                                        </tr>`).join('\\n')}\n                                        <tr style=\"background: linear-gradient(90deg, #0ea5e9 0%, #0c4a6e 100%);\">\n                                            <td colspan=\"6\" style=\"padding: 12px; text-align: right; color: #ffffff; font-size: 13px; font-weight: 700; letter-spacing: 0.5px;\">${language === 'nl' ? 'TOTAAL INCL. BTW' : 'TOTAL INCL. VAT'}</td>\n                                            <td style=\"padding: 12px 10px; text-align: right; color: #ffffff; font-size: 15px; font-weight: 700;\"><span style=\"font-family: monospace; white-space: nowrap;\">€&nbsp;${total.toFixed(2)}</span></td>\n                                        </tr>\n                                    </tfoot>\n                                </table>\n                            </div>\n\n                            <!-- Compact Info Sections -->\n                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"margin: 25px 0;\">\n                                <tr>\n                                    <td width=\"100%\">\n${data.notes ? `                                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"background-color: #fffbeb; border-radius: 8px; margin-bottom: 15px; border-left: 3px solid #f59e0b;\">\n                                            <tr>\n                                                <td style=\"padding: 15px;\">\n                                                    <h3 style=\"margin: 0 0 8px 0; color: #92400e; font-size: 14px; font-weight: 600;\">${language === 'nl' ? 'OPMERKINGEN' : 'NOTES'}</h3>\n                                                    <p style=\"font-size: 12px; color: #92400e; line-height: 1.5; margin: 0;\">\n                                                        ${replaceDynamicValues(data.notes)}\n                                                    </p>\n                                                </td>\n                                            </tr>\n                                        </table>` : ''}\n                                        \n${data.conditions ? `                                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"background-color: #fef2f2; border-radius: 8px; margin-bottom: 15px; border-left: 3px solid #ef4444;\">\n                                            <tr>\n                                                <td style=\"padding: 15px;\">\n                                                    <h3 style=\"margin: 0 0 8px 0; color: #991b1b; font-size: 14px; font-weight: 600;\">${language === 'nl' ? 'VOORWAARDEN' : 'CONDITIONS'}</h3>\n                                                    <p style=\"font-size: 12px; color: #991b1b; line-height: 1.5; margin: 0;\">\n                                                        ${replaceDynamicValues(data.conditions)}\n                                                    </p>\n                                                </td>\n                                            </tr>\n                                        </table>` : ''}\n                                    </td>\n                                </tr>\n                            </table>\n\n                            <!-- Steps -->\n                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"background-color: #f0fff4; border-radius: 8px; margin: 25px 0; border-left: 3px solid #10b981;\">\n                                <tr>\n                                    <td style=\"padding: 15px;\">\n                                        <h3 style=\"margin: 0 0 10px 0; color: #065f46; font-size: 14px; font-weight: 600;\">${language === 'nl' ? 'VERVOLGSTAPPEN' : 'NEXT STEPS'}</h3>\n                                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\">\n                                            <tr>\n                                                <td width=\"40\" style=\"vertical-align: top; padding-right: 15px;\">\n                                                    <div style=\"background-color: #10b981; color: #ffffff; width: 28px; height: 28px; border-radius: 50%; text-align: center; line-height: 28px; font-size: 12px; font-weight: 700;\">1</div>\n                                                </td>\n                                                <td style=\"vertical-align: top; padding-bottom: 12px;\">\n                                                    <div style=\"font-size: 12px; color: #065f46; line-height: 1.5;\">\n                                                        ${replaceDynamicValues(data.nextSteps.step1)}\n                                                    </div>\n                                                </td>\n                                            </tr>\n                                            <tr>\n                                                <td width=\"40\" style=\"vertical-align: top; padding-right: 15px;\">\n                                                    <div style=\"background-color: #10b981; color: #ffffff; width: 28px; height: 28px; border-radius: 50%; text-align: center; line-height: 28px; font-size: 12px; font-weight: 700;\">2</div>\n                                                </td>\n                                                <td style=\"vertical-align: top; padding-bottom: 12px;\">\n                                                    <div style=\"font-size: 12px; color: #065f46; line-height: 1.5;\">\n                                                        ${replaceDynamicValues(data.nextSteps.step2)}\n                                                    </div>\n                                                </td>\n                                            </tr>\n                                            <tr>\n                                                <td width=\"40\" style=\"vertical-align: top; padding-right: 15px;\">\n                                                    <div style=\"background-color: #10b981; color: #ffffff; width: 28px; height: 28px; border-radius: 50%; text-align: center; line-height: 28px; font-size: 12px; font-weight: 700;\">3</div>\n                                                </td>\n                                                <td style=\"vertical-align: top; padding-bottom: 12px;\">\n                                                    <div style=\"font-size: 12px; color: #065f46; line-height: 1.5;\">\n                                                        ${replaceDynamicValues(data.nextSteps.step3)}\n                                                    </div>\n                                                </td>\n                                            </tr>\n                                            <tr>\n                                                <td width=\"40\" style=\"vertical-align: top; padding-right: 15px;\">\n                                                    <div style=\"background-color: #10b981; color: #ffffff; width: 28px; height: 28px; border-radius: 50%; text-align: center; line-height: 28px; font-size: 12px; font-weight: 700;\">4</div>\n                                                </td>\n                                                <td style=\"vertical-align: top;\">\n                                                    <div style=\"font-size: 12px; color: #065f46; line-height: 1.5;\">\n                                                        ${replaceDynamicValues(data.nextSteps.step4)}\n                                                    </div>\n                                                </td>\n                                            </tr>\n                                        </table>\n                                    </td>\n                                </tr>\n                            </table>\n\n                            <!-- Payment -->\n                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"background-color: #f0f9ff; border-radius: 8px; margin: 25px 0; border-left: 3px solid #0ea5e9;\">\n                                <tr>\n                                    <td style=\"padding: 15px;\">\n                                        <h3 style=\"margin: 0 0 10px 0; color: #0c4a6e; font-size: 14px; font-weight: 600; text-align: center;\">${language === 'nl' ? 'BETAALOPTIES AANBETALING' : 'DEPOSIT PAYMENT OPTIONS'} (€ ${depositAmount.toFixed(2)})</h3>\n\n                                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"background-color: #ffffff; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);\">\n                                            <tr>\n                                                <td width=\"30%\" style=\"padding: 12px; text-align: center; vertical-align: top; border-right: 1px solid #f3f4f6;\">\n                                                    <h4 style=\"margin: 0 0 5px 0; color: #111827; font-size: 12px; font-weight: 600;\">${language === 'nl' ? 'iDEAL Betaling' : 'iDEAL Payment'}</h4>\n                                                    <a href=\"${paymentLink}\" style=\"background-color: #10b981; color: #ffffff; padding: 8px 15px; text-decoration: none; font-size: 12px; font-weight: 600; display: inline-block; border-radius: 4px; margin: 5px 0;\">${language === 'nl' ? 'NU BETALEN' : 'PAY NOW'}</a>\n                                                    <p style=\"margin: 5px 0 0 0; font-size: 10px; color: #6b7280;\">${language === 'nl' ? 'Snel en veilig online betalen' : 'Fast and secure online payment'}</p>\n                                                </td>\n                                                <td width=\"30%\" style=\"padding: 12px; text-align: center; vertical-align: top; border-right: 1px solid #f3f4f6;\">\n                                                    <h4 style=\"margin: 0 0 5px 0; color: #111827; font-size: 12px; font-weight: 600;\">${language === 'nl' ? 'QR Code Betaling' : 'QR Code Payment'}</h4>\n                                                    <div style=\"border: 2px solid #10b981; border-radius: 8px; padding: 5px; display: table; background: #ffffff; box-shadow: 0 2px 6px rgba(16, 185, 129, 0.2); width: 60px; height: 60px; margin: 0 auto;\">\n                                                        <div style=\"display: table-cell; vertical-align: middle; text-align: center;\">\n                                                            <img src=\"https://api.qrserver.com/v1/create-qr-code/?size=50x50&data=${encodeURIComponent(paymentLink)}\" width=\"50\" height=\"50\" alt=\"QR Code\" style=\"display: inline-block;\">\n                                                        </div>\n                                                    </div>\n                                                    <p style=\"margin: 5px 0 0 0; font-size: 10px; color: #6b7280;\">${language === 'nl' ? 'Scan met uw bank app' : 'Scan with your bank app'}</p>\n                                                </td>\n                                                <td width=\"40%\" style=\"padding: 12px; text-align: center; vertical-align: top;\">\n                                                    <h4 style=\"margin: 0 0 5px 0; color: #111827; font-size: 12px; font-weight: 600;\">${language === 'nl' ? 'Handmatig Overmaken' : 'Manual Transfer'}</h4>\n                                                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"font-size: 11px;\">\n                                                        <tr>\n                                                            <td width=\"30%\" style=\"padding: 2px 0; text-align: right; font-weight: 600; color: #111827; padding-right: 5px;\">IBAN:</td>\n                                                            <td width=\"70%\" style=\"padding: 2px 0; text-align: left; color: #4b5563;\">${data.payment.iban}</td>\n                                                        </tr>\n                                                        <tr>\n                                                            <td width=\"30%\" style=\"padding: 2px 0; text-align: right; font-weight: 600; color: #111827; padding-right: 5px;\">${language === 'nl' ? 'T.n.v.:' : 'To:'}</td>\n                                                            <td width=\"70%\" style=\"padding: 2px 0; text-align: left; color: #4b5563;\">${data.payment.accountName}</td>\n                                                        </tr>\n                                                        <tr>\n                                                            <td width=\"30%\" style=\"padding: 2px 0; text-align: right; font-weight: 600; color: #111827; padding-right: 5px;\">${language === 'nl' ? 'O.v.v.:' : 'Ref:'}</td>\n                                                            <td width=\"70%\" style=\"padding: 2px 0; text-align: left; color: #3b82f6; font-weight: 600;\">${data.quote.number}</td>\n                                                        </tr>\n                                                    </table>\n                                                </td>\n                                            </tr>\n                                        </table>\n                                    </td>\n                                </tr>\n                            </table>\n\n                            <!-- Contact -->\n                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"background-color: #ecfdf5; border-radius: 8px; margin: 25px 0; border-left: 3px solid #10b981;\">\n                                <tr>\n                                    <td style=\"padding: 15px;\">\n                                        <h3 style=\"margin: 0 0 10px 0; color: #065f46; font-size: 14px; font-weight: 600; text-align: center;\">${data.contactTitle}</h3>\n                                        <p style=\"font-size: 12px; color: #065f46; line-height: 1.5; margin: 0 0 12px 0; text-align: center;\">\n                                            ${data.contactDescription}\n                                        </p>\n                                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\">\n                                            <tr>\n                                                <td width=\"33%\" style=\"text-align: center; vertical-align: top;\">\n                                                    <div style=\"font-weight: 600; margin-bottom: 3px; font-size: 12px; color: #065f46;\">${language === 'nl' ? 'Telefoon' : 'Phone'}</div>\n                                                    <div><a href=\"tel:${data.company.phone.replace(/[^0-9+]/g, '')}\" style=\"color: #065f46; text-decoration: none; font-weight: 600; font-size: 12px;\">${data.company.phone}</a></div>\n                                                </td>\n                                                <td width=\"33%\" style=\"text-align: center; vertical-align: top;\">\n                                                    <div style=\"font-weight: 600; margin-bottom: 3px; font-size: 12px; color: #065f46;\">Email</div>\n                                                    <div><a href=\"mailto:${data.company.email}?subject=${language === 'nl' ? 'Vraag over offerte' : 'Question about quote'} ${data.quote.number}\" style=\"color: #065f46; text-decoration: none; font-weight: 600; font-size: 12px;\">${data.company.email}</a></div>\n                                                </td>\n                                                <td width=\"34%\" style=\"text-align: center; vertical-align: top;\">\n                                                    <div style=\"font-weight: 600; margin-bottom: 3px; font-size: 12px; color: #065f46;\">WhatsApp</div>\n                                                    <div><a href=\"https://wa.me/${data.company.phone.replace(/[^0-9]/g, '')}?text=${language === 'nl' ? 'Hallo, ik heb een vraag over offerte' : 'Hello, I have a question about quote'} ${data.quote.number}\" style=\"color: #065f46; text-decoration: none; font-weight: 600; font-size: 12px;\">${data.company.phone}</a></div>\n                                                </td>\n                                            </tr>\n                                        </table>\n                                    </td>\n                                </tr>\n                            </table>\n                        </td>\n                    </tr>\n\n                    <!-- Footer -->\n                    <tr>\n                        <td style=\"background-color: #111827; color: #9ca3af; padding: 20px; text-align: center; font-size: 12px; line-height: 1.5;\">\n                            <div style=\"color: #ffffff; font-weight: 600; margin-bottom: 5px; font-size: 14px;\">${data.company.name}</div>\n                            <div>\n                                KvK: ${data.company.kvk} | BTW: ${data.company.btw}<br>\n                                ${data.company.address}, ${data.company.city}<br>\n                                <a href=\"http://${data.company.website}\" style=\"color: #60a5fa; text-decoration: none; font-weight: 600;\">${data.company.website}</a>\n                            </div>\n                        </td>\n                    </tr>\n                </table>\n            </td>\n        </tr>\n    </table>\n</body>\n</html>`;\n  };\n\n  const copyToClipboard = async () => {\n    const htmlCode = generateHtmlCode();\n    try {\n      await navigator.clipboard.writeText(htmlCode);\n      setCopied(true);\n      setTimeout(() => setCopied(false), 2000);\n    } catch (err) {\n      console.error('Failed to copy: ', err);\n    }\n  };\n\n  const downloadHtml = () => {\n    const htmlCode = generateHtmlCode();\n    const blob = new Blob([htmlCode], { type: 'text/html' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `${data.quote.number}-template.html`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"flex justify-between items-center\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">\n          {language === 'nl' ? 'Gegenereerde HTML Code' : 'Generated HTML Code'}\n        </h3>\n        <div className=\"flex space-x-2\">\n          <button\n            onClick={copyToClipboard}\n            className={`inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${\n              copied ? 'bg-green-50 border-green-300 text-green-700' : ''\n            }`}\n          >\n            <Copy className=\"h-4 w-4 mr-2\" />\n            {copied \n              ? (language === 'nl' ? 'Gekopieerd!' : 'Copied!') \n              : (language === 'nl' ? 'Kopiëren' : 'Copy')\n            }\n          </button>\n          \n          <button\n            onClick={downloadHtml}\n            className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n          >\n            <Download className=\"h-4 w-4 mr-2\" />\n            {language === 'nl' ? 'Download' : 'Download'}\n          </button>\n        </div>\n      </div>\n      \n      <div className=\"bg-gray-900 rounded-lg p-4 max-h-96 overflow-auto\">\n        <pre className=\"text-green-400 text-xs font-mono whitespace-pre-wrap\">\n          {generateHtmlCode()}\n        </pre>\n      </div>\n      \n      <div className=\"text-sm text-gray-600\">\n        <p>\n          {language === 'nl' \n            ? 'Deze HTML code is geoptimaliseerd voor email clients en kan direct gebruikt worden in uw email templates.'\n            : 'This HTML code is optimized for email clients and can be used directly in your email templates.'\n          }\n        </p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAWO,SAAS,cAAc,EAAE,IAAI,EAAE,QAAQ,EAAsB;IAClE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,yDAAyD;IACzD,MAAM,WAAW,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;IAEpE,oBAAoB;IACpB,MAAM,YAAY,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK;QACxC,MAAM,YAAY,KAAK,KAAK,GAAG,KAAK,OAAO,GAAG;QAC9C,IAAI,CAAC,GAAG,CAAC,KAAK,OAAO,CAAC,EAAE;YACtB,GAAG,CAAC,KAAK,OAAO,CAAC,GAAG;QACtB;QACA,GAAG,CAAC,KAAK,OAAO,CAAC,IAAI;QACrB,OAAO;IACT,GAAG,CAAC;IAEJ,MAAM,iBAAiB,OAAO,MAAM,CAAC,WAAW,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK;IAChF,MAAM,QAAQ,WAAW;IACzB,MAAM,gBAAgB,QAAQ,CAAC,KAAK,OAAO,CAAC,iBAAiB,GAAG,GAAG;IACnE,MAAM,kBAAkB,QAAQ;IAChC,MAAM,qBAAqB,QAAQ,CAAC,KAAK,OAAO,CAAC,sBAAsB,GAAG,GAAG;IAC7E,MAAM,wBAAwB,QAAQ;IAEtC,iEAAiE;IACjE,MAAM,cAAc,KAAK,OAAO,CAAC,WAAW,IAAI;IAEhD,iCAAiC;IACjC,MAAM,uBAAuB,CAAC;QAC5B,IAAI,SAAS;QAEb,kDAAkD;QAClD,IAAI,KAAK,QAAQ,CAAC,wBAAwB,KAAK,QAAQ,CAAC,iBAAiB;YACvE,yCAAyC;YAEzC,mDAAmD;YACnD,SAAS,OAAO,OAAO,CAAC,sCAAsC,CAAC,IAAI,EAAE,MAAM,OAAO,CAAC,IAAI;YACvF,SAAS,OAAO,OAAO,CAAC,qCAAqC,CAAC,IAAI,EAAE,MAAM,OAAO,CAAC,IAAI;YAEtF,8CAA8C;YAC9C,SAAS,OAAO,OAAO,CAAC,+BAA+B,CAAC,IAAI,EAAE,mBAAmB,OAAO,CAAC,IAAI;YAC7F,SAAS,OAAO,OAAO,CAAC,4BAA4B,CAAC,IAAI,EAAE,mBAAmB,OAAO,CAAC,IAAI;YAE1F,8BAA8B;YAC9B,SAAS,OAAO,OAAO,CAAC,oEAAoE,CAAC,EAAE,EAAE,mBAAmB,OAAO,CAAC,IAAI;YAChI,SAAS,OAAO,OAAO,CAAC,yDAAyD,CAAC,EAAE,EAAE,mBAAmB,OAAO,CAAC,IAAI;QACvH;QAEA,+CAA+C;QAC/C,IAAI,KAAK,QAAQ,CAAC,sBAAsB,KAAK,QAAQ,CAAC,eAAe;YACnE,iDAAiD;YACjD,SAAS,OAAO,OAAO,CAAC,8CAA8C,CAAC,IAAI,EAAE,cAAc,OAAO,CAAC,IAAI;YACvG,SAAS,OAAO,OAAO,CAAC,yCAAyC,CAAC,IAAI,EAAE,cAAc,OAAO,CAAC,IAAI;YAElG,4CAA4C;YAC5C,SAAS,OAAO,OAAO,CAAC,2BAA2B,CAAC,IAAI,EAAE,KAAK,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI;YAC9F,SAAS,OAAO,OAAO,CAAC,wBAAwB,CAAC,IAAI,EAAE,KAAK,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI;YAE3F,mDAAmD;YACnD,IAAI,CAAC,OAAO,QAAQ,CAAC,gBAAgB,CAAC,OAAO,QAAQ,CAAC,cAAc;gBAClE,SAAS,OAAO,OAAO,CAAC,sBAAsB;YAChD;QACF;QAEA,wDAAwD;QACxD,IAAI,KAAK,QAAQ,CAAC,aAAa,KAAK,QAAQ,CAAC,UAAU,KAAK,QAAQ,CAAC,iBAAiB;YACpF,SAAS,OAAO,OAAO,CAAC,gBAAgB,CAAC,OAAO;gBAC9C,MAAM,cAAc,KAAK,SAAS,CAAC,GAAG;gBAEtC,2CAA2C;gBAC3C,IAAI,YAAY,QAAQ,CAAC,aAAa,YAAY,QAAQ,CAAC,QAAQ;oBACjE,OAAO,CAAC,EAAE,EAAE,cAAc,OAAO,CAAC,IAAI;gBACxC;gBACA,6CAA6C;gBAC7C,IAAI,YAAY,QAAQ,CAAC,mBAAmB,YAAY,QAAQ,CAAC,kBAAkB;oBACjF,OAAO,CAAC,EAAE,EAAE,gBAAgB,OAAO,CAAC,IAAI;gBAC1C;gBACA,OAAO;YACT;QACF;QAEA,wCAAwC;QACxC,SAAS,OAAO,OAAO,CAAC,WAAW,CAAC,OAAO;YACzC,IAAI,KAAK,QAAQ,CAAC,kBAAkB,KAAK,QAAQ,CAAC,YAAY;gBAC5D,OAAO,GAAG,KAAK,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC;YAC7C;YACA,IAAI,KAAK,QAAQ,CAAC,cAAc,KAAK,QAAQ,CAAC,aAAa;gBACzD,OAAO,GAAG,KAAK,OAAO,CAAC,sBAAsB,CAAC,CAAC,CAAC;YAClD;YACA,OAAO;QACT;QAEA,mCAAmC;QACnC,MAAM,kBAAkB;QACxB,SAAS,OAAO,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,MAAM;QAE1D,OAAO;IACT;IAEA,MAAM,mBAAmB;QACvB,OAAO,CAAC;YACA,EAAE,SAAS;;;;;;;;;;;;WAYZ,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,WAAW,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;uIA0BoE,EAAE,KAAK,KAAK,CAAC;yHAC3B,EAAE,KAAK,WAAW,CAAC;;;;;gDAK5F,EAAE,KAAK,KAAK,CAAC,MAAM,CAAC;;;;;;;;;;;;;;;;;2HAiBuD,EAAE,aAAa,OAAO,kBAAkB,SAAS;;4EAEhG,EAAE,KAAK,MAAM,CAAC,IAAI,CAAC;4CACnD,EAAE,KAAK,MAAM,CAAC,OAAO,CAAC;4CACtB,EAAE,KAAK,MAAM,CAAC,IAAI,CAAC;;;;;2HAK4D,EAAE,aAAa,OAAO,oBAAoB,gBAAgB;;4EAEzG,EAAE,aAAa,OAAO,YAAY,UAAU,UAAU,EAAE,KAAK,KAAK,CAAC,MAAM,CAAC;4EAC1E,EAAE,aAAa,OAAO,WAAW,QAAQ,UAAU,EAAE,KAAK,KAAK,CAAC,IAAI,CAAC;4EACrE,EAAE,aAAa,OAAO,gBAAgB,eAAe,UAAU,EAAE,KAAK,KAAK,CAAC,UAAU,CAAC;;;;;;;;;;yHAU1C,EAAE,KAAK,QAAQ,CAAC;;4CAE7F,EAAE,KAAK,YAAY,CAAC;;;;;;;;;oCAS5B,EAAE,aAAa,OAAO,oBAAoB,gBAAgB;;;;;;;;8NAQgI,EAAE,aAAa,OAAO,iBAAiB,cAAc;+NACpD,EAAE,aAAa,OAAO,WAAW,MAAM;+NACvC,EAAE,aAAa,OAAO,UAAU,OAAO;+NACvC,EAAE,aAAa,OAAO,UAAU,QAAQ;+NACxC,EAAE,aAAa,OAAO,QAAQ,MAAM;+NACpC,EAAE,aAAa,OAAO,WAAW,QAAQ;;;;;AAKxQ,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC,qEAAqE,EAAE,QAAQ,MAAM,IAAI,YAAY,UAAU;0LACwC,EAAE,KAAK,UAAU,CAAC;;gHAE5F,EAAE,KAAK,WAAW,CAAC;8FACrC,EAAE,KAAK,cAAc,CAAC;;0LAEsE,EAAE,KAAK,QAAQ,CAAC;0LAChB,EAAE,KAAK,IAAI,CAAC;4PACsD,EAAE,KAAK,KAAK,CAAC,OAAO,CAAC,GAAG;0LAC1F,EAAE,KAAK,OAAO,CAAC;8QACqE,EAAE,KAAK,KAAK,CAAC,OAAO,CAAC,GAAG;6CACzP,CAAC,EAAE,IAAI,CAAC,MAAM;;;;;qLAK0H,EAAE,aAAa,OAAO,0BAA0B,uBAAuB;oNACxC,EAAE,SAAS,OAAO,CAAC,GAAG;;AAE1O,EAAE,OAAO,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,MAAM,OAAO,GAAK,CAAC;2IACsF,EAAE,aAAa,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;mNACc,EAAE,OAAO,OAAO,CAAC,GAAG;6CAC1L,CAAC,EAAE,IAAI,CAAC,MAAM;;gLAEqH,EAAE,aAAa,OAAO,qBAAqB,kBAAkB;oNACzB,EAAE,MAAM,OAAO,CAAC,GAAG;;;;;;;;;;AAUvO,EAAE,KAAK,KAAK,GAAG,CAAC;;;sIAGsH,EAAE,aAAa,OAAO,gBAAgB,QAAQ;;wDAE5H,EAAE,qBAAqB,KAAK,KAAK,EAAE;;;;gDAI3C,CAAC,GAAG,GAAG;;AAEvD,EAAE,KAAK,UAAU,GAAG,CAAC;;;sIAGiH,EAAE,aAAa,OAAO,gBAAgB,aAAa;;wDAEjI,EAAE,qBAAqB,KAAK,UAAU,EAAE;;;;gDAIhD,CAAC,GAAG,GAAG;;;;;;;;;2HASoE,EAAE,aAAa,OAAO,mBAAmB,aAAa;;;;;;;;wDAQzH,EAAE,qBAAqB,KAAK,SAAS,CAAC,KAAK,EAAE;;;;;;;;;;wDAU7C,EAAE,qBAAqB,KAAK,SAAS,CAAC,KAAK,EAAE;;;;;;;;;;wDAU7C,EAAE,qBAAqB,KAAK,SAAS,CAAC,KAAK,EAAE;;;;;;;;;;wDAU7C,EAAE,qBAAqB,KAAK,SAAS,CAAC,KAAK,EAAE;;;;;;;;;;;;;+IAa0C,EAAE,aAAa,OAAO,6BAA6B,0BAA0B,IAAI,EAAE,cAAc,OAAO,CAAC,GAAG;;;;;sIAKrH,EAAE,aAAa,OAAO,mBAAmB,gBAAgB;6DAClI,EAAE,YAAY,4LAA4L,EAAE,aAAa,OAAO,eAAe,UAAU;mHACnM,EAAE,aAAa,OAAO,kCAAkC,iCAAiC;;;sIAGtE,EAAE,aAAa,OAAO,qBAAqB,kBAAkB;;;kIAGjE,EAAE,mBAAmB,aAAa;;;mHAGjD,EAAE,aAAa,OAAO,yBAAyB,0BAA0B;;;sIAGtD,EAAE,aAAa,OAAO,wBAAwB,kBAAkB;;;;sIAIhE,EAAE,KAAK,OAAO,CAAC,IAAI,CAAC;;;6KAGmB,EAAE,aAAa,OAAO,YAAY,MAAM;sIAC/E,EAAE,KAAK,OAAO,CAAC,WAAW,CAAC;;;6KAGY,EAAE,aAAa,OAAO,YAAY,OAAO;wJAC9D,EAAE,KAAK,KAAK,CAAC,MAAM,CAAC;;;;;;;;;;;;;;+IAc7B,EAAE,KAAK,YAAY,CAAC;;4CAEvH,EAAE,KAAK,kBAAkB,CAAC;;;;;wIAKkE,EAAE,aAAa,OAAO,aAAa,QAAQ;sEAC7G,EAAE,KAAK,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,IAAI,oFAAoF,EAAE,KAAK,OAAO,CAAC,KAAK,CAAC;;;;yEAInJ,EAAE,KAAK,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,aAAa,OAAO,uBAAuB,uBAAuB,CAAC,EAAE,KAAK,KAAK,CAAC,MAAM,CAAC,oFAAoF,EAAE,KAAK,OAAO,CAAC,KAAK,CAAC;;;;gFAIzN,EAAE,KAAK,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,IAAI,MAAM,EAAE,aAAa,OAAO,yCAAyC,uCAAuC,CAAC,EAAE,KAAK,KAAK,CAAC,MAAM,CAAC,oFAAoF,EAAE,KAAK,OAAO,CAAC,KAAK,CAAC;;;;;;;;;;;;;gHAatP,EAAE,KAAK,OAAO,CAAC,IAAI,CAAC;;qCAE/F,EAAE,KAAK,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC,GAAG,CAAC;gCACnD,EAAE,KAAK,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,KAAK,OAAO,CAAC,IAAI,CAAC;gDAC7B,EAAE,KAAK,OAAO,CAAC,OAAO,CAAC,mEAAmE,EAAE,KAAK,OAAO,CAAC,OAAO,CAAC;;;;;;;;;OAS1J,CAAC;IACN;IAEA,MAAM,kBAAkB;QACtB,MAAM,WAAW;QACjB,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,UAAU;YACV,WAAW,IAAM,UAAU,QAAQ;QACrC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oBAAoB;QACpC;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,WAAW;QACjB,MAAM,OAAO,IAAI,KAAK;YAAC;SAAS,EAAE;YAAE,MAAM;QAAY;QACtD,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,GAAG,KAAK,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC;QACjD,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACX,aAAa,OAAO,2BAA2B;;;;;;kCAElD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,WAAW,CAAC,6NAA6N,EACvO,SAAS,gDAAgD,IACzD;;kDAEF,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCACf,SACI,aAAa,OAAO,gBAAgB,YACpC,aAAa,OAAO,aAAa;;;;;;;0CAIxC,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCACnB,aAAa,OAAO,aAAa;;;;;;;;;;;;;;;;;;;0BAKxC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;0BAIL,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;8BACE,aAAa,OACV,8GACA;;;;;;;;;;;;;;;;;AAMd", "debugId": null}}, {"offset": {"line": 3665, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/offerte%20email%20template%20editor%20/offerte-template-editor/src/components/TemplateEditor.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Languages, Copy, Download, Eye } from 'lucide-react';\nimport { Language, TemplateData } from '@/types/template';\nimport { defaultTemplateData } from '@/data/defaultTemplate';\nimport { FormEditor } from './FormEditor';\nimport { TemplatePreview } from './TemplatePreview';\nimport { CodeGenerator } from './CodeGenerator';\n\nexport function TemplateEditor() {\n  const [language, setLanguage] = useState<Language>('nl');\n  const [templateData, setTemplateData] = useState<TemplateData>(() => {\n    // Ensure all fields have values\n    return { ...defaultTemplateData[language] };\n  });\n  const [activeTab, setActiveTab] = useState<'preview' | 'code'>('preview');\n\n  const handleLanguageChange = (newLanguage: Language) => {\n    setLanguage(newLanguage);\n    // Update template data to the new language\n    setTemplateData({ ...defaultTemplateData[newLanguage] });\n  };\n\n  const handleDataChange = (newData: TemplateData) => {\n    // Ensure all fields have proper values and force re-render\n    const cleanedData = {\n      ...newData,\n      // Ensure strings are never null/undefined\n      title: newData.title || '',\n      companyName: newData.companyName || '',\n      greeting: newData.greeting || '',\n      introduction: newData.introduction || '',\n      notes: newData.notes || '',\n      conditions: newData.conditions || '',\n      contactTitle: newData.contactTitle || '',\n      contactDescription: newData.contactDescription || ''\n    };\n    setTemplateData(cleanedData);\n  };\n\n\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">\n                {language === 'nl' ? 'Offerte Template Editor' : 'Quote Template Editor'}\n              </h1>\n              <p className=\"text-sm text-gray-600\">\n                {language === 'nl' \n                  ? 'Bewerk uw template en genereer HTML code' \n                  : 'Edit your template and generate HTML code'\n                }\n              </p>\n            </div>\n            \n            <div className=\"flex items-center space-x-4\">\n              {/* Language Toggle */}\n              <div className=\"flex items-center space-x-2\">\n                <Languages className=\"h-5 w-5 text-blue-600\" />\n                <select\n                  value={language}\n                  onChange={(e) => handleLanguageChange(e.target.value as Language)}\n                  className=\"border-2 border-blue-500 rounded-lg px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-600 bg-white text-blue-700 font-medium shadow-sm hover:shadow-md transition-all duration-200\"\n                >\n                  <option value=\"nl\">🇳🇱 Nederlands</option>\n                  <option value=\"en\">🇬🇧 English</option>\n                </select>\n              </div>\n\n\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Left Column - Form Editor */}\n          <div className=\"space-y-6\">\n            <div className=\"bg-white rounded-lg shadow-sm border\">\n              <div className=\"px-6 py-4 border-b border-gray-200\">\n                <h2 className=\"text-lg font-semibold text-gray-900\">\n                  {language === 'nl' ? 'Template Bewerken' : 'Edit Template'}\n                </h2>\n              </div>\n              <div className=\"p-6\">\n                <FormEditor\n                  data={templateData}\n                  language={language}\n                  onChange={handleDataChange}\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Right Column - Preview & Code */}\n          <div className=\"space-y-6\">\n            {/* Tab Navigation */}\n            <div className=\"bg-white rounded-lg shadow-sm border\">\n              <div className=\"border-b border-gray-200\">\n                <nav className=\"-mb-px flex\">\n                  <button\n                    onClick={() => setActiveTab('preview')}\n                    className={`py-2 px-4 border-b-2 font-medium text-sm ${\n                      activeTab === 'preview'\n                        ? 'border-blue-500 text-blue-600'\n                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                    }`}\n                  >\n                    <Eye className=\"h-4 w-4 inline mr-2\" />\n                    {language === 'nl' ? 'Voorbeeld' : 'Preview'}\n                  </button>\n                  <button\n                    onClick={() => setActiveTab('code')}\n                    className={`py-2 px-4 border-b-2 font-medium text-sm ${\n                      activeTab === 'code'\n                        ? 'border-blue-500 text-blue-600'\n                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                    }`}\n                  >\n                    <Copy className=\"h-4 w-4 inline mr-2\" />\n                    {language === 'nl' ? 'HTML Code' : 'HTML Code'}\n                  </button>\n                </nav>\n              </div>\n              \n              <div className=\"p-6\">\n                {activeTab === 'preview' ? (\n                  <TemplatePreview data={templateData} language={language} />\n                ) : (\n                  <CodeGenerator data={templateData} language={language} />\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AARA;;;;;;;;AAUO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QAC7D,gCAAgC;QAChC,OAAO;YAAE,GAAG,8HAAA,CAAA,sBAAmB,CAAC,SAAS;QAAC;IAC5C;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAE/D,MAAM,uBAAuB,CAAC;QAC5B,YAAY;QACZ,2CAA2C;QAC3C,gBAAgB;YAAE,GAAG,8HAAA,CAAA,sBAAmB,CAAC,YAAY;QAAC;IACxD;IAEA,MAAM,mBAAmB,CAAC;QACxB,2DAA2D;QAC3D,MAAM,cAAc;YAClB,GAAG,OAAO;YACV,0CAA0C;YAC1C,OAAO,QAAQ,KAAK,IAAI;YACxB,aAAa,QAAQ,WAAW,IAAI;YACpC,UAAU,QAAQ,QAAQ,IAAI;YAC9B,cAAc,QAAQ,YAAY,IAAI;YACtC,OAAO,QAAQ,KAAK,IAAI;YACxB,YAAY,QAAQ,UAAU,IAAI;YAClC,cAAc,QAAQ,YAAY,IAAI;YACtC,oBAAoB,QAAQ,kBAAkB,IAAI;QACpD;QACA,gBAAgB;IAClB;IAIA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDACX,aAAa,OAAO,4BAA4B;;;;;;kDAEnD,8OAAC;wCAAE,WAAU;kDACV,aAAa,OACV,6CACA;;;;;;;;;;;;0CAKR,8OAAC;gCAAI,WAAU;0CAEb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4MAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;4CACpD,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,8OAAC;oDAAO,OAAM;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW/B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDACX,aAAa,OAAO,sBAAsB;;;;;;;;;;;kDAG/C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gIAAA,CAAA,aAAU;4CACT,MAAM;4CACN,UAAU;4CACV,UAAU;;;;;;;;;;;;;;;;;;;;;;sCAOlB,8OAAC;4BAAI,WAAU;sCAEb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,aAAa;oDAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,YACV,kCACA,8EACJ;;sEAEF,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDACd,aAAa,OAAO,cAAc;;;;;;;8DAErC,8OAAC;oDACC,SAAS,IAAM,aAAa;oDAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,SACV,kCACA,8EACJ;;sEAEF,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDACf,aAAa,OAAO,cAAc;;;;;;;;;;;;;;;;;;kDAKzC,8OAAC;wCAAI,WAAU;kDACZ,cAAc,0BACb,8OAAC,qIAAA,CAAA,kBAAe;4CAAC,MAAM;4CAAc,UAAU;;;;;iEAE/C,8OAAC,mIAAA,CAAA,gBAAa;4CAAC,MAAM;4CAAc,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/D", "debugId": null}}, {"offset": {"line": 3981, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/offerte%20email%20template%20editor%20/offerte-template-editor/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { TemplateEditor } from '@/components/TemplateEditor';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <TemplateEditor />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,oIAAA,CAAA,iBAAc;;;;;;;;;;AAGrB", "debugId": null}}]}