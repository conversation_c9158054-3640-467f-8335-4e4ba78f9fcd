import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "Offerte Template Editor",
  description: "Professional quote template editor with multi-language support",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="font-sans antialiased">
        {children}
      </body>
    </html>
  );
}
