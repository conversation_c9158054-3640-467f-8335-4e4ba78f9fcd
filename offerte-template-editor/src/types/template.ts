export interface QuoteItem {
  id: string;
  description: string;
  subDescription: string;
  quantity: number;
  unit: string;
  price: number;
  vatRate: number;
  total: number;
}

export interface ClientInfo {
  name: string;
  address: string;
  city: string;
}

export interface QuoteDetails {
  number: string;
  date: string;
  validUntil: string;
}

export interface CompanyInfo {
  name: string;
  kvk: string;
  btw: string;
  address: string;
  city: string;
  website: string;
  email: string;
  phone: string;
}

export interface PaymentInfo {
  iban: string;
  accountName: string;
  depositPercentage: number;
  hourlyRate: number;
  cashDiscountPercentage: number;
}

export interface TemplateData {
  // Header
  title: string;
  companyName: string;
  
  // Client & Quote Info
  client: ClientInfo;
  quote: QuoteDetails;
  
  // Introduction
  greeting: string;
  introduction: string;
  
  // Items
  items: QuoteItem[];
  
  // Additional Info
  notes: string;
  conditions: string;
  nextSteps: {
    step1: string;
    step2: string;
    step3: string;
    step4: string;
  };
  
  // Company & Payment
  company: CompanyInfo;
  payment: PaymentInfo;
  
  // Contact
  contactTitle: string;
  contactDescription: string;
}

export interface Translations {
  nl: TemplateData;
  en: TemplateData;
}

export type Language = 'nl' | 'en';
