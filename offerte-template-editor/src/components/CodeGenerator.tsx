'use client';

import { useState } from 'react';
import { Copy, Download } from 'lucide-react';
import { TemplateData, Language } from '@/types/template';

interface CodeGeneratorProps {
  data: TemplateData;
  language: Language;
}

export function CodeGenerator({ data, language }: CodeGeneratorProps) {
  const [copied, setCopied] = useState(false);

  // Calculate totals - items.total should be excluding VAT
  const subtotal = data.items.reduce((sum, item) => sum + item.total, 0);

  // Group VAT by rate
  const vatByRate = data.items.reduce((acc, item) => {
    const vatAmount = item.total * item.vatRate / 100;
    if (!acc[item.vatRate]) {
      acc[item.vatRate] = 0;
    }
    acc[item.vatRate] += vatAmount;
    return acc;
  }, {} as Record<number, number>);

  const totalVatAmount = Object.values(vatByRate).reduce((sum, vat) => sum + vat, 0);
  const total = subtotal + totalVatAmount;
  const depositAmount = total * (data.payment.depositPercentage / 100);
  const remainingAmount = total - depositAmount;
  const cashDiscountAmount = total * (data.payment.cashDiscountPercentage / 100);
  const totalWithCashDiscount = total - cashDiscountAmount;

  // Replace dynamic values in text
  const replaceDynamicValues = (text: string) => {
    let result = text;

    // OPMERKINGEN: Replace cash discount calculations
    if (text.includes('contante betaling') || text.includes('cash payment')) {
      // Replace the placeholder text for savings amount first
      result = result.replace(/hier moet %?5 van bedraag van offerte incl btw hier plaatsen €/gi, `€ ${cashDiscountAmount.toFixed(2)}`);
      result = result.replace(/here must %?5 of quote amount incl vat here place €/gi, `€ ${cashDiscountAmount.toFixed(2)}`);

      // Replace all euro amounts in the text
      result = result.replace(/€\s*[\d.,]+/g, (match, offset) => {
        const beforeMatch = result.substring(0, offset);
        const afterMatch = result.substring(offset + match.length);

        // First occurrence: total amount
        if (beforeMatch.includes('totaalbedrag van') || beforeMatch.includes('total amount of')) {
          return `€ ${total.toFixed(2)}`;
        }
        // Second occurrence: savings amount (5% of total incl. BTW)
        if (beforeMatch.includes('besparing') || beforeMatch.includes('saving')) {
          return `€ ${cashDiscountAmount.toFixed(2)}`;
        }
        return match;
      });
    }

    // VOORWAARDEN: Replace deposit and hourly rate
    if (text.includes('aanbetaling van') || text.includes('deposit of')) {
      result = result.replace(/€\s*[\d.,]+/g, (match, offset) => {
        const beforeMatch = text.substring(0, offset);

        // Deposit amount (30% of total incl. BTW)
        if (beforeMatch.includes('aanbetaling van') || beforeMatch.includes('deposit of')) {
          return `€ ${depositAmount.toFixed(2)}`;
        }
        // Hourly rate (excl. BTW)
        if (beforeMatch.includes('tegen') || beforeMatch.includes('at')) {
          return `€ ${data.payment.hourlyRate.toFixed(2)}`;
        }
        return match;
      });

      // Add "excl. BTW" after hourly rate if not present
      if (!result.includes('excl. BTW') && !result.includes('excl. VAT')) {
        result = result.replace(/(€\s*[\d.,]+\/uur)/, '$1 excl. BTW');
      }
    }

    // VERVOLGSTAPPEN: Replace deposit and remaining amounts
    if (text.includes('Betaal') || text.includes('Pay') || text.includes('Restbetaling')) {
      result = result.replace(/€\s*[\d.,]+/g, (match, offset) => {
        const beforeMatch = text.substring(0, offset);

        // Deposit payment (30% of total incl. BTW)
        if (beforeMatch.includes('Betaal') || beforeMatch.includes('Pay')) {
          return `€ ${depositAmount.toFixed(2)}`;
        }
        // Remaining payment (70% of total incl. BTW)
        if (beforeMatch.includes('Restbetaling') || beforeMatch.includes('Final payment')) {
          return `€ ${remainingAmount.toFixed(2)}`;
        }
        return match;
      });
    }

    // Replace percentage values dynamically
    result = result.replace(/(\d+)%/g, (match, percentage) => {
      if (text.includes('aanbetaling') || text.includes('deposit')) {
        return `${data.payment.depositPercentage}%`;
      }
      if (text.includes('korting') || text.includes('discount')) {
        return `${data.payment.cashDiscountPercentage}%`;
      }
      return match;
    });

    // Replace quote numbers everywhere
    const oldQuotePattern = /OFF-2025-\d+|QUO-2025-\d+/g;
    result = result.replace(oldQuotePattern, data.quote.number);

    return result;
  };

  const generateHtmlCode = () => {
    return `<!DOCTYPE html>
<html lang="${language}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="color-scheme" content="light">
    <meta name="supported-color-schemes" content="light">
    <meta name="format-detection" content="telephone=yes">
    <meta name="x-apple-disable-message-reformatting">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="msapplication-TileColor" content="#0ea5e9">
    <meta name="theme-color" content="#0ea5e9">
    <title>${data.title} ${data.quote.number} | ${data.companyName}</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <!--[if !mso]><!------>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" type="text/css">
    <!--<![endif]-->
</head>
<body style="margin: 0; padding: 0; font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; background-color: #f3f4f6; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;">
    <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #f3f4f6;">
        <tr>
            <td align="center" style="padding: 25px 10px;">
                <table border="0" cellpadding="0" cellspacing="0" width="580" style="max-width: 580px; background-color: #ffffff; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 15px rgba(0,0,0,0.08);">
                    
                    <!-- Modern Header -->
                    <tr>
                        <td style="background-color: #111827; color: #ffffff; padding: 30px 25px;">
                            <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <td style="vertical-align: middle;">
                                        <h1 style="margin: 0; font-size: 32px; font-weight: 800; color: #ffffff; letter-spacing: 1px;">${data.title}</h1>
                                        <p style="margin: 8px 0 0 0; font-size: 15px; color: #d1d5db; font-weight: 500;">${data.companyName}</p>
                                    </td>
                                    <td style="text-align: right; vertical-align: middle;">
                                        <div style="background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); padding: 3px; border-radius: 8px; display: inline-block; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                                            <div style="background-color: #1e40af; color: #ffffff; padding: 10px 18px; border-radius: 6px; font-size: 15px; font-weight: 700; letter-spacing: 0.5px; text-align: center;">
                                                ${data.quote.number}
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    
                    <!-- Content -->
                    <tr>
                        <td style="padding: 25px;">
                            
                            <!-- Client & Quote Info -->
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" style="margin-bottom: 25px;">
                                <tr>
                                    <td width="48%" style="background-color: #f9fafb; padding: 15px; border-radius: 8px; vertical-align: top; border-left: 3px solid #3b82f6;">
                                        <h3 style="margin: 0 0 10px 0; color: #111827; font-size: 14px; font-weight: 600;">${language === 'nl' ? 'OPDRACHTGEVER' : 'CLIENT'}</h3>
                                        <p style="margin: 0; font-size: 13px; color: #4b5563; line-height: 1.5;">
                                            <strong style="color: #111827;">${data.client.name}</strong><br>
                                            ${data.client.address}<br>
                                            ${data.client.city}
                                        </p>
                                    </td>
                                    <td width="4%"></td>
                                    <td width="48%" style="background-color: #f9fafb; padding: 15px; border-radius: 8px; vertical-align: top; border-left: 3px solid #f59e0b;">
                                        <h3 style="margin: 0 0 10px 0; color: #111827; font-size: 14px; font-weight: 600;">${language === 'nl' ? 'OFFERTE DETAILS' : 'QUOTE DETAILS'}</h3>
                                        <p style="margin: 0; font-size: 13px; color: #4b5563; line-height: 1.5;">
                                            <strong style="color: #111827;">${language === 'nl' ? 'Nummer:' : 'Number:'}</strong> ${data.quote.number}<br>
                                            <strong style="color: #111827;">${language === 'nl' ? 'Datum:' : 'Date:'}</strong> ${data.quote.date}<br>
                                            <strong style="color: #111827;">${language === 'nl' ? 'Geldig tot:' : 'Valid until:'}</strong> ${data.quote.validUntil}
                                        </p>
                                    </td>
                                </tr>
                            </table>

                            <!-- Introduction -->
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #eff6ff; border-radius: 8px; margin: 25px 0; border-left: 3px solid #3b82f6;">
                                <tr>
                                    <td style="padding: 15px;">
                                        <p style="margin: 0 0 8px 0; font-size: 14px; color: #1e40af; font-weight: 600;">${data.greeting}</p>
                                        <p style="margin: 0; font-size: 13px; color: #1e40af; line-height: 1.6;">
                                            ${data.introduction}
                                        </p>
                                    </td>
                                </tr>
                            </table>

                            <!-- Cost Breakdown -->
                            <div style="margin: 30px 0;">
                                <h2 style="color: #0c4a6e; font-size: 16px; font-weight: 700; margin: 0 0 12px 0; text-align: center; text-transform: uppercase; letter-spacing: 0.5px; border-bottom: 2px solid #0ea5e9; padding-bottom: 8px;">
                                    ${language === 'nl' ? 'Kostenoverzicht' : 'Cost Overview'}
                                </h2>
                                
                                <table border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.08);">
                                    <!-- Header -->
                                    <thead>
                                        <tr>
                                            <th style="background-color: #0c4a6e; color: #ffffff; padding: 10px 6px; text-align: center; font-size: 11px; font-weight: 600; letter-spacing: 0.5px; text-transform: uppercase;">#</th>
                                            <th style="background-color: #0c4a6e; color: #ffffff; padding: 10px 12px; text-align: left; font-size: 11px; font-weight: 600; letter-spacing: 0.5px; text-transform: uppercase;">${language === 'nl' ? 'Omschrijving' : 'Description'}</th>
                                            <th style="background-color: #0c4a6e; color: #ffffff; padding: 10px 6px; text-align: center; font-size: 11px; font-weight: 600; letter-spacing: 0.5px; text-transform: uppercase;">${language === 'nl' ? 'Aantal' : 'Qty'}</th>
                                            <th style="background-color: #0c4a6e; color: #ffffff; padding: 10px 6px; text-align: center; font-size: 11px; font-weight: 600; letter-spacing: 0.5px; text-transform: uppercase;">${language === 'nl' ? 'Eenh.' : 'Unit'}</th>
                                            <th style="background-color: #0c4a6e; color: #ffffff; padding: 10px 10px; text-align: right; font-size: 11px; font-weight: 600; letter-spacing: 0.5px; text-transform: uppercase;">${language === 'nl' ? 'Prijs' : 'Price'}</th>
                                            <th style="background-color: #0c4a6e; color: #ffffff; padding: 10px 6px; text-align: center; font-size: 11px; font-weight: 600; letter-spacing: 0.5px; text-transform: uppercase;">${language === 'nl' ? 'BTW' : 'VAT'}</th>
                                            <th style="background-color: #0c4a6e; color: #ffffff; padding: 10px 10px; text-align: right; font-size: 11px; font-weight: 600; letter-spacing: 0.5px; text-transform: uppercase;">${language === 'nl' ? 'Totaal' : 'Total'}</th>
                                        </tr>
                                    </thead>
                                    <!-- Items -->
                                    <tbody>
${data.items.map((item, index) => `                                        <tr style="background-color: ${index % 2 === 0 ? '#ffffff' : '#f0f9ff'};">
                                            <td style="padding: 10px 6px; border-bottom: 1px solid #e5e7eb; text-align: center; font-size: 12px; color: #6b7280; vertical-align: middle;">${item.itemNumber}</td>
                                            <td style="padding: 10px 12px; border-bottom: 1px solid #e5e7eb; vertical-align: middle;">
                                                <div style="font-weight: 600; color: #0c4a6e; font-size: 12px;">${item.description}</div>
                                                <div style="color: #6b7280; font-size: 10px;">${item.subDescription}</div>
                                            </td>
                                            <td style="padding: 10px 6px; border-bottom: 1px solid #e5e7eb; text-align: center; font-size: 12px; color: #4b5563; vertical-align: middle;">${item.quantity}</td>
                                            <td style="padding: 10px 6px; border-bottom: 1px solid #e5e7eb; text-align: center; font-size: 12px; color: #4b5563; vertical-align: middle;">${item.unit}</td>
                                            <td style="padding: 10px 10px; border-bottom: 1px solid #e5e7eb; text-align: right; font-size: 12px; color: #4b5563; vertical-align: middle;"><span style="font-family: monospace; white-space: nowrap;">€&nbsp;${item.price.toFixed(2)}</span></td>
                                            <td style="padding: 10px 6px; border-bottom: 1px solid #e5e7eb; text-align: center; font-size: 12px; color: #4b5563; vertical-align: middle;">${item.vatRate}%</td>
                                            <td style="padding: 10px 10px; border-bottom: 1px solid #e5e7eb; text-align: right; font-weight: 600; color: #0c4a6e; font-size: 12px; vertical-align: middle;"><span style="font-family: monospace; white-space: nowrap;">€&nbsp;${item.total.toFixed(2)}</span></td>
                                        </tr>`).join('\n')}
                                    </tbody>
                                    <!-- Totals -->
                                    <tfoot>
                                        <tr style="background-color: #f0f9ff;">
                                            <td colspan="6" style="padding: 10px 12px; text-align: right; font-weight: 600; font-size: 12px; color: #0c4a6e; letter-spacing: 0.3px;">${language === 'nl' ? 'Subtotaal (excl. BTW)' : 'Subtotal (excl. VAT)'}</td>
                                            <td style="padding: 10px 10px; text-align: right; font-weight: 600; color: #0c4a6e; font-size: 12px;"><span style="font-family: monospace; white-space: nowrap;">€&nbsp;${subtotal.toFixed(2)}</span></td>
                                        </tr>
${Object.entries(vatByRate).map(([rate, amount]) => `                                        <tr style="background-color: #f0f9ff;">
                                            <td colspan="6" style="padding: 6px 12px; text-align: right; font-size: 12px; color: #4b5563;">${language === 'nl' ? `BTW (${rate}%)` : `VAT (${rate}%)`}</td>
                                            <td style="padding: 6px 10px; text-align: right; font-weight: 600; color: #4b5563; font-size: 12px;"><span style="font-family: monospace; white-space: nowrap;">€&nbsp;${amount.toFixed(2)}</span></td>
                                        </tr>`).join('\n')}
                                        <tr style="background: linear-gradient(90deg, #0ea5e9 0%, #0c4a6e 100%);">
                                            <td colspan="6" style="padding: 12px; text-align: right; color: #ffffff; font-size: 13px; font-weight: 700; letter-spacing: 0.5px;">${language === 'nl' ? 'TOTAAL INCL. BTW' : 'TOTAL INCL. VAT'}</td>
                                            <td style="padding: 12px 10px; text-align: right; color: #ffffff; font-size: 15px; font-weight: 700;"><span style="font-family: monospace; white-space: nowrap;">€&nbsp;${total.toFixed(2)}</span></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>

                            <!-- Compact Info Sections -->
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" style="margin: 25px 0;">
                                <tr>
                                    <td width="100%">
${data.notes ? `                                        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #fffbeb; border-radius: 8px; margin-bottom: 15px; border-left: 3px solid #f59e0b;">
                                            <tr>
                                                <td style="padding: 15px;">
                                                    <h3 style="margin: 0 0 8px 0; color: #92400e; font-size: 14px; font-weight: 600;">${language === 'nl' ? 'OPMERKINGEN' : 'NOTES'}</h3>
                                                    <p style="font-size: 12px; color: #92400e; line-height: 1.5; margin: 0;">
                                                        ${replaceDynamicValues(data.notes)}
                                                    </p>
                                                </td>
                                            </tr>
                                        </table>` : ''}
                                        
${data.conditions ? `                                        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #fef2f2; border-radius: 8px; margin-bottom: 15px; border-left: 3px solid #ef4444;">
                                            <tr>
                                                <td style="padding: 15px;">
                                                    <h3 style="margin: 0 0 8px 0; color: #991b1b; font-size: 14px; font-weight: 600;">${language === 'nl' ? 'VOORWAARDEN' : 'CONDITIONS'}</h3>
                                                    <p style="font-size: 12px; color: #991b1b; line-height: 1.5; margin: 0;">
                                                        ${replaceDynamicValues(data.conditions)}
                                                    </p>
                                                </td>
                                            </tr>
                                        </table>` : ''}
                                    </td>
                                </tr>
                            </table>

                            <!-- Steps -->
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #f0fff4; border-radius: 8px; margin: 25px 0; border-left: 3px solid #10b981;">
                                <tr>
                                    <td style="padding: 15px;">
                                        <h3 style="margin: 0 0 10px 0; color: #065f46; font-size: 14px; font-weight: 600;">${language === 'nl' ? 'VERVOLGSTAPPEN' : 'NEXT STEPS'}</h3>
                                        <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                            <tr>
                                                <td width="40" style="vertical-align: top; padding-right: 15px;">
                                                    <div style="background-color: #10b981; color: #ffffff; width: 28px; height: 28px; border-radius: 50%; text-align: center; line-height: 28px; font-size: 12px; font-weight: 700;">1</div>
                                                </td>
                                                <td style="vertical-align: top; padding-bottom: 12px;">
                                                    <div style="font-size: 12px; color: #065f46; line-height: 1.5;">
                                                        ${replaceDynamicValues(data.nextSteps.step1)}
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td width="40" style="vertical-align: top; padding-right: 15px;">
                                                    <div style="background-color: #10b981; color: #ffffff; width: 28px; height: 28px; border-radius: 50%; text-align: center; line-height: 28px; font-size: 12px; font-weight: 700;">2</div>
                                                </td>
                                                <td style="vertical-align: top; padding-bottom: 12px;">
                                                    <div style="font-size: 12px; color: #065f46; line-height: 1.5;">
                                                        ${replaceDynamicValues(data.nextSteps.step2)}
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td width="40" style="vertical-align: top; padding-right: 15px;">
                                                    <div style="background-color: #10b981; color: #ffffff; width: 28px; height: 28px; border-radius: 50%; text-align: center; line-height: 28px; font-size: 12px; font-weight: 700;">3</div>
                                                </td>
                                                <td style="vertical-align: top; padding-bottom: 12px;">
                                                    <div style="font-size: 12px; color: #065f46; line-height: 1.5;">
                                                        ${replaceDynamicValues(data.nextSteps.step3)}
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td width="40" style="vertical-align: top; padding-right: 15px;">
                                                    <div style="background-color: #10b981; color: #ffffff; width: 28px; height: 28px; border-radius: 50%; text-align: center; line-height: 28px; font-size: 12px; font-weight: 700;">4</div>
                                                </td>
                                                <td style="vertical-align: top;">
                                                    <div style="font-size: 12px; color: #065f46; line-height: 1.5;">
                                                        ${replaceDynamicValues(data.nextSteps.step4)}
                                                    </div>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>

                            <!-- Payment -->
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #f0f9ff; border-radius: 8px; margin: 25px 0; border-left: 3px solid #0ea5e9;">
                                <tr>
                                    <td style="padding: 15px;">
                                        <h3 style="margin: 0 0 10px 0; color: #0c4a6e; font-size: 14px; font-weight: 600; text-align: center;">${language === 'nl' ? 'BETAALOPTIES AANBETALING' : 'DEPOSIT PAYMENT OPTIONS'} (€ ${depositAmount.toFixed(2)})</h3>

                                        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #ffffff; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                            <tr>
                                                <td width="30%" style="padding: 12px; text-align: center; vertical-align: top; border-right: 1px solid #f3f4f6;">
                                                    <h4 style="margin: 0 0 5px 0; color: #111827; font-size: 12px; font-weight: 600;">${language === 'nl' ? 'iDEAL Betaling' : 'iDEAL Payment'}</h4>
                                                    <a href="${data.payment.paymentLink ? `${data.payment.paymentLink}${data.quote.number}?amount=${depositAmount.toFixed(2)}` : '#'}" style="background-color: #10b981; color: #ffffff; padding: 8px 15px; text-decoration: none; font-size: 12px; font-weight: 600; display: inline-block; border-radius: 4px; margin: 5px 0;">${language === 'nl' ? 'NU BETALEN' : 'PAY NOW'}</a>
                                                    <p style="margin: 5px 0 0 0; font-size: 10px; color: #6b7280;">${language === 'nl' ? 'Snel en veilig online betalen' : 'Fast and secure online payment'}</p>
                                                </td>
                                                <td width="30%" style="padding: 12px; text-align: center; vertical-align: top; border-right: 1px solid #f3f4f6;">
                                                    <h4 style="margin: 0 0 5px 0; color: #111827; font-size: 12px; font-weight: 600;">${language === 'nl' ? 'QR Code Betaling' : 'QR Code Payment'}</h4>
                                                    <div style="border: 2px solid #10b981; border-radius: 8px; padding: 5px; display: table; background: #ffffff; box-shadow: 0 2px 6px rgba(16, 185, 129, 0.2); width: 60px; height: 60px; margin: 0 auto;">
                                                        <div style="display: table-cell; vertical-align: middle; text-align: center;">
                                                            ${data.payment.paymentLink ?
                                                                `<img src="https://api.qrserver.com/v1/create-qr-code/?size=50x50&data=${encodeURIComponent(`${data.payment.paymentLink}${data.quote.number}?amount=${depositAmount.toFixed(2)}`)}" width="50" height="50" alt="QR Code" style="display: inline-block;">` :
                                                                `<div style="width: 50px; height: 50px; background-color: #f3f4f6; display: flex; align-items: center; justify-content: center; font-size: 8px; color: #6b7280;">QR</div>`
                                                            }
                                                        </div>
                                                    </div>
                                                    <p style="margin: 5px 0 0 0; font-size: 10px; color: #6b7280;">${language === 'nl' ? 'Scan met uw bank app' : 'Scan with your bank app'}</p>
                                                </td>
                                                <td width="40%" style="padding: 12px; text-align: center; vertical-align: top;">
                                                    <h4 style="margin: 0 0 5px 0; color: #111827; font-size: 12px; font-weight: 600;">${language === 'nl' ? 'Handmatig Overmaken' : 'Manual Transfer'}</h4>
                                                    <table border="0" cellpadding="0" cellspacing="0" width="100%" style="font-size: 11px;">
                                                        <tr>
                                                            <td width="30%" style="padding: 2px 0; text-align: right; font-weight: 600; color: #111827; padding-right: 5px;">IBAN:</td>
                                                            <td width="70%" style="padding: 2px 0; text-align: left; color: #4b5563;">${data.payment.iban}</td>
                                                        </tr>
                                                        <tr>
                                                            <td width="30%" style="padding: 2px 0; text-align: right; font-weight: 600; color: #111827; padding-right: 5px;">${language === 'nl' ? 'T.n.v.:' : 'To:'}</td>
                                                            <td width="70%" style="padding: 2px 0; text-align: left; color: #4b5563;">${data.payment.accountName}</td>
                                                        </tr>
                                                        <tr>
                                                            <td width="30%" style="padding: 2px 0; text-align: right; font-weight: 600; color: #111827; padding-right: 5px;">${language === 'nl' ? 'O.v.v.:' : 'Ref:'}</td>
                                                            <td width="70%" style="padding: 2px 0; text-align: left; color: #3b82f6; font-weight: 600;">${data.quote.number}</td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>

                            <!-- Contact -->
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #ecfdf5; border-radius: 8px; margin: 25px 0; border-left: 3px solid #10b981;">
                                <tr>
                                    <td style="padding: 15px;">
                                        <h3 style="margin: 0 0 10px 0; color: #065f46; font-size: 14px; font-weight: 600; text-align: center;">${data.contactTitle}</h3>
                                        <p style="font-size: 12px; color: #065f46; line-height: 1.5; margin: 0 0 12px 0; text-align: center;">
                                            ${data.contactDescription}
                                        </p>
                                        <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                            <tr>
                                                <td width="33%" style="text-align: center; vertical-align: top;">
                                                    <div style="font-weight: 600; margin-bottom: 3px; font-size: 12px; color: #065f46;">${language === 'nl' ? 'Telefoon' : 'Phone'}</div>
                                                    <div><a href="tel:${data.company.phone.replace(/[^0-9+]/g, '')}" style="color: #065f46; text-decoration: none; font-weight: 600; font-size: 12px;">${data.company.phone}</a></div>
                                                </td>
                                                <td width="33%" style="text-align: center; vertical-align: top;">
                                                    <div style="font-weight: 600; margin-bottom: 3px; font-size: 12px; color: #065f46;">Email</div>
                                                    <div><a href="mailto:${data.company.email}?subject=${language === 'nl' ? 'Vraag over offerte' : 'Question about quote'} ${data.quote.number}" style="color: #065f46; text-decoration: none; font-weight: 600; font-size: 12px;">${data.company.email}</a></div>
                                                </td>
                                                <td width="34%" style="text-align: center; vertical-align: top;">
                                                    <div style="font-weight: 600; margin-bottom: 3px; font-size: 12px; color: #065f46;">WhatsApp</div>
                                                    <div><a href="https://wa.me/${data.company.phone.replace(/[^0-9]/g, '')}?text=${language === 'nl' ? 'Hallo, ik heb een vraag over offerte' : 'Hello, I have a question about quote'} ${data.quote.number}" style="color: #065f46; text-decoration: none; font-weight: 600; font-size: 12px;">${data.company.phone}</a></div>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>

                    <!-- Footer -->
                    <tr>
                        <td style="background-color: #111827; color: #9ca3af; padding: 20px; text-align: center; font-size: 12px; line-height: 1.5;">
                            <div style="color: #ffffff; font-weight: 600; margin-bottom: 5px; font-size: 14px;">${data.company.name}</div>
                            <div>
                                KvK: ${data.company.kvk} | BTW: ${data.company.btw}<br>
                                ${data.company.address}, ${data.company.city}<br>
                                <a href="http://${data.company.website}" style="color: #60a5fa; text-decoration: none; font-weight: 600;">${data.company.website}</a>
                            </div>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>`;
  };

  const copyToClipboard = async () => {
    const htmlCode = generateHtmlCode();
    try {
      await navigator.clipboard.writeText(htmlCode);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const downloadHtml = () => {
    const htmlCode = generateHtmlCode();
    const blob = new Blob([htmlCode], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${data.quote.number}-template.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">
          {language === 'nl' ? 'Gegenereerde HTML Code' : 'Generated HTML Code'}
        </h3>
        <div className="flex space-x-2">
          <button
            onClick={copyToClipboard}
            className={`inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
              copied ? 'bg-green-50 border-green-300 text-green-700' : ''
            }`}
          >
            <Copy className="h-4 w-4 mr-2" />
            {copied 
              ? (language === 'nl' ? 'Gekopieerd!' : 'Copied!') 
              : (language === 'nl' ? 'Kopiëren' : 'Copy')
            }
          </button>
          
          <button
            onClick={downloadHtml}
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <Download className="h-4 w-4 mr-2" />
            {language === 'nl' ? 'Download' : 'Download'}
          </button>
        </div>
      </div>
      
      <div className="bg-gray-900 rounded-lg p-4 max-h-96 overflow-auto">
        <pre className="text-green-400 text-xs font-mono whitespace-pre-wrap">
          {generateHtmlCode()}
        </pre>
      </div>
      
      <div className="text-sm text-gray-600">
        <p>
          {language === 'nl' 
            ? 'Deze HTML code is geoptimaliseerd voor email clients en kan direct gebruikt worden in uw email templates.'
            : 'This HTML code is optimized for email clients and can be used directly in your email templates.'
          }
        </p>
      </div>
    </div>
  );
}
