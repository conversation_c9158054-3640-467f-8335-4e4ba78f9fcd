'use client';

import { useState } from 'react';
import { Languages, Copy, Download, Eye } from 'lucide-react';
import { Language, TemplateData } from '@/types/template';
import { defaultTemplateData } from '@/data/defaultTemplate';
import { FormEditor } from './FormEditor';
import { TemplatePreview } from './TemplatePreview';
import { CodeGenerator } from './CodeGenerator';

export function TemplateEditor() {
  const [language, setLanguage] = useState<Language>('nl');
  const [templateData, setTemplateData] = useState<TemplateData>(() => {
    // Ensure all fields have values
    return { ...defaultTemplateData[language] };
  });
  const [activeTab, setActiveTab] = useState<'preview' | 'code'>('preview');

  const handleLanguageChange = (newLanguage: Language) => {
    setLanguage(newLanguage);
    // Update template data to the new language
    setTemplateData({ ...defaultTemplateData[newLanguage] });
  };

  const handleDataChange = (newData: TemplateData) => {
    // Ensure all fields have proper values and force re-render
    const cleanedData = {
      ...newData,
      // Ensure strings are never null/undefined
      title: newData.title || '',
      companyName: newData.companyName || '',
      greeting: newData.greeting || '',
      introduction: newData.introduction || '',
      notes: newData.notes || '',
      conditions: newData.conditions || '',
      contactTitle: newData.contactTitle || '',
      contactDescription: newData.contactDescription || ''
    };
    setTemplateData(cleanedData);
  };



  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {language === 'nl' ? 'Offerte Template Editor' : 'Quote Template Editor'}
              </h1>
              <p className="text-sm text-gray-600">
                {language === 'nl' 
                  ? 'Bewerk uw template en genereer HTML code' 
                  : 'Edit your template and generate HTML code'
                }
              </p>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Language Toggle */}
              <div className="flex items-center space-x-2">
                <Languages className="h-5 w-5 text-blue-600" />
                <select
                  value={language}
                  onChange={(e) => handleLanguageChange(e.target.value as Language)}
                  className="border-2 border-blue-500 rounded-lg px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-600 bg-white text-blue-700 font-medium shadow-sm hover:shadow-md transition-all duration-200"
                >
                  <option value="nl">🇳🇱 Nederlands</option>
                  <option value="en">🇬🇧 English</option>
                </select>
              </div>


            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Form Editor */}
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">
                  {language === 'nl' ? 'Template Bewerken' : 'Edit Template'}
                </h2>
              </div>
              <div className="p-6">
                <FormEditor
                  data={templateData}
                  language={language}
                  onChange={handleDataChange}
                />
              </div>
            </div>
          </div>

          {/* Right Column - Preview & Code */}
          <div className="space-y-6">
            {/* Tab Navigation */}
            <div className="bg-white rounded-lg shadow-sm border">
              <div className="border-b border-gray-200">
                <nav className="-mb-px flex">
                  <button
                    onClick={() => setActiveTab('preview')}
                    className={`py-2 px-4 border-b-2 font-medium text-sm ${
                      activeTab === 'preview'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Eye className="h-4 w-4 inline mr-2" />
                    {language === 'nl' ? 'Voorbeeld' : 'Preview'}
                  </button>
                  <button
                    onClick={() => setActiveTab('code')}
                    className={`py-2 px-4 border-b-2 font-medium text-sm ${
                      activeTab === 'code'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Copy className="h-4 w-4 inline mr-2" />
                    {language === 'nl' ? 'HTML Code' : 'HTML Code'}
                  </button>
                </nav>
              </div>
              
              <div className="p-6">
                {activeTab === 'preview' ? (
                  <TemplatePreview data={templateData} language={language} />
                ) : (
                  <CodeGenerator data={templateData} language={language} />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
