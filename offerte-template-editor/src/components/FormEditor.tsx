'use client';

import { useState } from 'react';
import { Plus, Trash2, ChevronRight, ChevronLeft } from 'lucide-react';
import { TemplateData, QuoteItem, Language } from '@/types/template';

interface FormEditorProps {
  data: TemplateData;
  language: Language;
  onChange: (data: TemplateData) => void;
}

const FORM_STEPS = [
  { id: 'header', titleNL: 'Header & Basis', titleEN: 'Header & Basic' },
  { id: 'client', titleNL: 'Klant & Offerte', titleEN: 'Client & Quote' },
  { id: 'items', titleNL: 'Offerte Items', titleEN: 'Quote Items' },
  { id: 'notes', titleNL: 'Opmerkingen', titleEN: 'Notes' },
  { id: 'steps', titleNL: 'Vervolgstappen', titleEN: 'Next Steps' },
  { id: 'company', titleNL: 'Bedrijfsgegevens', titleEN: 'Company Info' },
  { id: 'payment', titleNL: 'Betaalgegevens', titleEN: 'Payment Info' }
];

export function FormEditor({ data, language, onChange }: FormEditorProps) {
  const [currentStep, setCurrentStep] = useState(0);

  const nextStep = () => {
    if (currentStep < FORM_STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const updateData = (updates: Partial<TemplateData>) => {
    onChange({ ...data, ...updates });
  };

  const updateNestedData = <T extends keyof TemplateData>(
    key: T,
    updates: Partial<TemplateData[T]>
  ) => {
    onChange({
      ...data,
      [key]: { ...data[key], ...updates }
    });
  };

  const addItem = () => {
    const newItemNumber = Math.max(...data.items.map(item => item.itemNumber), 0) + 1;
    const newItem: QuoteItem = {
      id: Date.now().toString(),
      itemNumber: newItemNumber,
      description: language === 'nl' ? 'Nieuwe service' : 'New service',
      subDescription: language === 'nl' ? 'Beschrijving van de service' : 'Service description',
      quantity: 1,
      unit: language === 'nl' ? 'stuk' : 'piece',
      price: 0,
      vatRate: 21,
      total: 0
    };

    updateData({ items: [...data.items, newItem] });
  };

  const updateItem = (index: number, updates: Partial<QuoteItem>) => {
    const updatedItems = data.items.map((item, i) => {
      if (i === index) {
        const updatedItem = { ...item, ...updates };
        // Recalculate total when price, quantity, or VAT changes - total should be EXCLUDING VAT
        if ('price' in updates || 'quantity' in updates) {
          updatedItem.total = updatedItem.price * updatedItem.quantity;
        }
        return updatedItem;
      }
      return item;
    });

    updateData({ items: updatedItems });
  };

  const removeItem = (index: number) => {
    const updatedItems = data.items.filter((_, i) => i !== index);
    updateData({ items: updatedItems });
  };

  const currentStepData = FORM_STEPS[currentStep];
  const stepTitle = language === 'nl' ? currentStepData.titleNL : currentStepData.titleEN;

  // Simple input component that doesn't lose focus
  const SimpleInput = ({ label, defaultValue, onChange, type = 'text', placeholder = '' }: {
    label: string;
    defaultValue: string | number;
    onChange: (value: string | number) => void;
    type?: string;
    placeholder?: string;
  }) => (
    <div>
      <label className="block text-xs font-medium text-gray-700 mb-1">{label}</label>
      <input
        type={type}
        defaultValue={String(defaultValue || '')}
        onBlur={(e) => {
          const val = e.target.value;
          if (type === 'number') {
            onChange(val === '' ? 0 : parseFloat(val) || 0);
          } else {
            onChange(val);
          }
        }}
        placeholder={placeholder}
        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
      />
    </div>
  );

  const SimpleTextArea = ({ label, defaultValue, onChange, rows = 2 }: {
    label: string;
    defaultValue: string;
    onChange: (value: string) => void;
    rows?: number;
  }) => (
    <div>
      <label className="block text-xs font-medium text-gray-700 mb-1">{label}</label>
      <textarea
        defaultValue={String(defaultValue || '')}
        onBlur={(e) => onChange(e.target.value)}
        rows={rows}
        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
      />
    </div>
  );

  const SimpleSelect = ({ label, defaultValue, onChange, options }: {
    label: string;
    defaultValue: string | number;
    onChange: (value: string | number) => void;
    options: { value: string | number; label: string }[];
  }) => (
    <div>
      <label className="block text-xs font-medium text-gray-700 mb-1">{label}</label>
      <select
        defaultValue={String(defaultValue || '')}
        onChange={(e) => {
          const val = e.target.value;
          const num = parseFloat(val);
          onChange(isNaN(num) ? val : num);
        }}
        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
      >
        {options.map((option) => (
          <option key={option.value} defaultValue={option.value}>{option.label}</option>
        ))}
      </select>
    </div>
  );



  const renderStepContent = () => {
    switch (currentStepData.id) {
      case 'header':
        return (
          <div className="space-y-3">
            <SimpleInput
              label={language === 'nl' ? 'Titel' : 'Title'}
              defaultValue={data.title}
              onChange={(value) => updateData({ title: value as string })}
            />
            <SimpleInput
              label={language === 'nl' ? 'Bedrijfsnaam' : 'Company Name'}
              defaultValue={data.companyName}
              onChange={(value) => updateData({ companyName: value as string })}
            />
          </div>
        );

      case 'client':
        return (
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-3">
              <SimpleInput
                label={language === 'nl' ? 'Klant Naam' : 'Client Name'}
                defaultValue={data.client.name}
                onChange={(value) => updateNestedData('client', { name: value as string })}
              />
              <SimpleInput
                label={language === 'nl' ? 'Offerte Nummer' : 'Quote Number'}
                defaultValue={data.quote.number}
                onChange={(value) => updateNestedData('quote', { number: value as string })}
              />
            </div>
            <div className="grid grid-cols-2 gap-3">
              <SimpleInput
                label={language === 'nl' ? 'Adres' : 'Address'}
                defaultValue={data.client.address}
                onChange={(value) => updateNestedData('client', { address: value as string })}
              />
              <SimpleInput
                label={language === 'nl' ? 'Stad' : 'City'}
                defaultValue={data.client.city}
                onChange={(value) => updateNestedData('client', { city: value as string })}
              />
            </div>
            <div className="grid grid-cols-2 gap-3">
              <SimpleInput
                label={language === 'nl' ? 'Datum' : 'Date'}
                defaultValue={data.quote.date}
                onChange={(value) => updateNestedData('quote', { date: value as string })}
              />
              <SimpleInput
                label={language === 'nl' ? 'Geldig tot' : 'Valid Until'}
                defaultValue={data.quote.validUntil}
                onChange={(value) => updateNestedData('quote', { validUntil: value as string })}
              />
            </div>
            <SimpleTextArea
              label={language === 'nl' ? 'Begroeting' : 'Greeting'}
              defaultValue={data.greeting}
              onChange={(value) => updateData({ greeting: value })}
              rows={1}
            />
            <SimpleTextArea
              label={language === 'nl' ? 'Introductie' : 'Introduction'}
              defaultValue={data.introduction}
              onChange={(value) => updateData({ introduction: value })}
              rows={2}
            />
          </div>
        );

      case 'items':
        return (
          <div className="space-y-3">
            {data.items.map((item, index) => (
              <div key={item.id} className="border border-gray-200 rounded p-3 bg-gray-50">
                <div className="flex justify-between items-center mb-2">
                  <h4 className="text-sm font-medium text-gray-900">
                    {language === 'nl' ? 'Item' : 'Item'} {item.itemNumber}
                  </h4>
                  <button
                    onClick={() => removeItem(index)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="h-3 w-3" />
                  </button>
                </div>

                <div className="grid grid-cols-3 gap-2 mb-2">
                  <SimpleInput
                    label="#"
                    defaultValue={item.itemNumber}
                    onChange={(value) => updateItem(index, { itemNumber: value as number })}
                    type="number"
                  />
                  <SimpleInput
                    label={language === 'nl' ? 'Aantal' : 'Qty'}
                    defaultValue={item.quantity}
                    onChange={(value) => updateItem(index, { quantity: value as number })}
                    type="number"
                  />
                  <SimpleInput
                    label={language === 'nl' ? 'Eenheid' : 'Unit'}
                    defaultValue={item.unit}
                    onChange={(value) => updateItem(index, { unit: value as string })}
                  />
                </div>

                <div className="grid grid-cols-1 gap-2 mb-2">
                  <SimpleInput
                    label={language === 'nl' ? 'Beschrijving' : 'Description'}
                    defaultValue={item.description}
                    onChange={(value) => updateItem(index, { description: value as string })}
                  />
                  <SimpleInput
                    label={language === 'nl' ? 'Sub-beschrijving' : 'Sub-description'}
                    defaultValue={item.subDescription}
                    onChange={(value) => updateItem(index, { subDescription: value as string })}
                  />
                </div>

                <div className="grid grid-cols-3 gap-2">
                  <SimpleInput
                    label={language === 'nl' ? 'Prijs (€)' : 'Price (€)'}
                    defaultValue={item.price}
                    onChange={(value) => updateItem(index, { price: value as number })}
                    type="number"
                  />
                  <SimpleSelect
                    label={language === 'nl' ? 'BTW' : 'VAT'}
                    defaultValue={item.vatRate}
                    onChange={(value) => updateItem(index, { vatRate: value as number })}
                    options={[
                      { value: 21, label: '21%' },
                      { value: 9, label: '9%' }
                    ]}
                  />
                  <div className="flex items-end">
                    <div className="text-xs text-gray-600 pb-1">
                      <strong>{language === 'nl' ? 'Totaal excl.:' : 'Total excl.:'} €{item.total.toFixed(2)}</strong>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            <button
              onClick={addItem}
              className="w-full flex items-center justify-center px-3 py-2 border border-dashed border-gray-300 rounded text-sm text-gray-600 hover:text-gray-900 hover:border-gray-400 transition-colors"
            >
              <Plus className="h-4 w-4 mr-2" />
              {language === 'nl' ? 'Item Toevoegen' : 'Add Item'}
            </button>
          </div>
        );

      case 'notes':
        return (
          <div className="space-y-3">
            <SimpleTextArea
              label={language === 'nl' ? 'Opmerkingen' : 'Notes'}
              defaultValue={data.notes}
              onChange={(value) => updateData({ notes: value })}
              rows={3}
            />
            <SimpleTextArea
              label={language === 'nl' ? 'Voorwaarden' : 'Conditions'}
              defaultValue={data.conditions}
              onChange={(value) => updateData({ conditions: value })}
              rows={3}
            />
          </div>
        );

      case 'steps':
        return (
          <div className="space-y-3">
            <SimpleTextArea
              label={language === 'nl' ? 'Stap 1: Offerte Accorderen' : 'Step 1: Approve Quote'}
              defaultValue={data.nextSteps.step1}
              onChange={(value) => updateNestedData('nextSteps', { step1: value })}
              rows={2}
            />
            <SimpleTextArea
              label={language === 'nl' ? 'Stap 2: Aanbetaling' : 'Step 2: Deposit'}
              defaultValue={data.nextSteps.step2}
              onChange={(value) => updateNestedData('nextSteps', { step2: value })}
              rows={1}
            />
            <SimpleTextArea
              label={language === 'nl' ? 'Stap 3: Afspraak Inplannen' : 'Step 3: Schedule Appointment'}
              defaultValue={data.nextSteps.step3}
              onChange={(value) => updateNestedData('nextSteps', { step3: value })}
              rows={1}
            />
            <SimpleTextArea
              label={language === 'nl' ? 'Stap 4: Restbetaling' : 'Step 4: Final Payment'}
              defaultValue={data.nextSteps.step4}
              onChange={(value) => updateNestedData('nextSteps', { step4: value })}
              rows={1}
            />
          </div>
        );

      case 'company':
        return (
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-3">
              <SimpleInput
                label={language === 'nl' ? 'Bedrijfsnaam' : 'Company Name'}
                defaultValue={data.company.name}
                onChange={(value) => updateNestedData('company', { name: value as string })}
              />
              <SimpleInput
                label="Website"
                defaultValue={data.company.website}
                onChange={(value) => updateNestedData('company', { website: value as string })}
              />
            </div>
            <div className="grid grid-cols-2 gap-3">
              <SimpleInput
                label="KvK"
                defaultValue={data.company.kvk}
                onChange={(value) => updateNestedData('company', { kvk: value as string })}
              />
              <SimpleInput
                label="BTW"
                defaultValue={data.company.btw}
                onChange={(value) => updateNestedData('company', { btw: value as string })}
              />
            </div>
            <div className="grid grid-cols-2 gap-3">
              <SimpleInput
                label={language === 'nl' ? 'Adres' : 'Address'}
                defaultValue={data.company.address}
                onChange={(value) => updateNestedData('company', { address: value as string })}
              />
              <SimpleInput
                label={language === 'nl' ? 'Stad' : 'City'}
                defaultValue={data.company.city}
                onChange={(value) => updateNestedData('company', { city: value as string })}
              />
            </div>
            <div className="grid grid-cols-2 gap-3">
              <SimpleInput
                label="Email"
                defaultValue={data.company.email}
                onChange={(value) => updateNestedData('company', { email: value as string })}
              />
              <SimpleInput
                label={language === 'nl' ? 'Telefoon' : 'Phone'}
                defaultValue={data.company.phone}
                onChange={(value) => updateNestedData('company', { phone: value as string })}
              />
            </div>
          </div>
        );

      case 'payment':
        return (
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-3">
              <SimpleInput
                label="IBAN"
                defaultValue={data.payment.iban}
                onChange={(value) => updateNestedData('payment', { iban: value as string })}
              />
              <SimpleInput
                label={language === 'nl' ? 'Rekeninghouder' : 'Account Name'}
                defaultValue={data.payment.accountName}
                onChange={(value) => updateNestedData('payment', { accountName: value as string })}
              />
            </div>
            <SimpleInput
              label={language === 'nl' ? 'Betaallink' : 'Payment Link'}
              defaultValue={data.payment.paymentLink}
              onChange={(value) => updateNestedData('payment', { paymentLink: value as string })}
              placeholder="https://betaal.example.com/pay/"
            />
            <div className="grid grid-cols-2 gap-3">
              <SimpleInput
                label={language === 'nl' ? 'Aanbetaling (%)' : 'Deposit (%)'}
                defaultValue={data.payment.depositPercentage}
                onChange={(value) => updateNestedData('payment', { depositPercentage: value as number })}
                type="number"
                placeholder="30"
              />
              <SimpleInput
                label={language === 'nl' ? 'Contantkorting (%)' : 'Cash Discount (%)'}
                defaultValue={data.payment.cashDiscountPercentage}
                onChange={(value) => updateNestedData('payment', { cashDiscountPercentage: value as number })}
                type="number"
                placeholder="5"
              />
            </div>
            <div className="grid grid-cols-1 gap-3">
              <SimpleInput
                label={language === 'nl' ? 'Uurtarief (€)' : 'Hourly Rate (€)'}
                defaultValue={data.payment.hourlyRate}
                onChange={(value) => updateNestedData('payment', { hourlyRate: value as number })}
                type="number"
                placeholder="75.00"
              />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-4">
      {/* Step Navigation */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">
            {language === 'nl' ? 'Stap' : 'Step'} {currentStep + 1} {language === 'nl' ? 'van' : 'of'} {FORM_STEPS.length}
          </span>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={prevStep}
            disabled={currentStep === 0}
            className="flex items-center px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            {language === 'nl' ? 'Vorige' : 'Previous'}
          </button>
          {currentStep === FORM_STEPS.length - 1 ? (
            <button
              className="flex items-center px-4 py-2 text-sm bg-green-600 text-white rounded hover:bg-green-700 font-medium"
            >
              ✓ {language === 'nl' ? 'Voltooien' : 'Complete'}
            </button>
          ) : (
            <button
              onClick={nextStep}
              className="flex items-center px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              {language === 'nl' ? 'Volgende' : 'Next'}
              <ChevronRight className="h-4 w-4 ml-1" />
            </button>
          )}
        </div>
      </div>

      {/* Step Indicator */}
      <div className="flex items-center space-x-2 mb-4">
        {FORM_STEPS.map((step, index) => (
          <div key={step.id} className="flex items-center">
            <button
              onClick={() => setCurrentStep(index)}
              className={`w-8 h-8 rounded-full text-xs font-medium ${
                index === currentStep
                  ? 'bg-blue-600 text-white'
                  : index < currentStep
                  ? 'bg-green-600 text-white'
                  : 'bg-gray-200 text-gray-600'
              }`}
            >
              {index + 1}
            </button>
            {index < FORM_STEPS.length - 1 && (
              <div className={`w-8 h-0.5 ${index < currentStep ? 'bg-green-600' : 'bg-gray-200'}`} />
            )}
          </div>
        ))}
      </div>

      {/* Current Step Content */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{stepTitle}</h3>
        <div className="max-h-[calc(100vh-400px)] overflow-y-auto">
          {renderStepContent()}
        </div>
      </div>
    </div>
  );
}
