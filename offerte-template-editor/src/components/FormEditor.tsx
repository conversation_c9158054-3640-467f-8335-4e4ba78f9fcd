'use client';

import { useState } from 'react';
import { Plus, Trash2, ChevronDown, ChevronRight } from 'lucide-react';
import { TemplateData, QuoteItem, Language } from '@/types/template';

interface FormEditorProps {
  data: TemplateData;
  language: Language;
  onChange: (data: TemplateData) => void;
}

export function FormEditor({ data, language, onChange }: FormEditorProps) {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    header: true,
    client: true,
    items: true,
    notes: false,
    company: false,
    payment: false
  });

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const updateData = (updates: Partial<TemplateData>) => {
    onChange({ ...data, ...updates });
  };

  const updateNestedData = <T extends keyof TemplateData>(
    key: T,
    updates: Partial<TemplateData[T]>
  ) => {
    onChange({
      ...data,
      [key]: { ...data[key], ...updates }
    });
  };

  const addItem = () => {
    const newItem: QuoteItem = {
      id: Date.now().toString(),
      description: language === 'nl' ? 'Nieuwe service' : 'New service',
      subDescription: language === 'nl' ? 'Beschrijving van de service' : 'Service description',
      quantity: 1,
      unit: language === 'nl' ? 'stuk' : 'piece',
      price: 0,
      vatRate: 21,
      total: 0
    };
    
    updateData({ items: [...data.items, newItem] });
  };

  const updateItem = (index: number, updates: Partial<QuoteItem>) => {
    const updatedItems = data.items.map((item, i) => {
      if (i === index) {
        const updatedItem = { ...item, ...updates };
        // Recalculate total when price, quantity, or VAT changes
        if ('price' in updates || 'quantity' in updates || 'vatRate' in updates) {
          const subtotal = updatedItem.price * updatedItem.quantity;
          updatedItem.total = subtotal * (1 + updatedItem.vatRate / 100);
        }
        return updatedItem;
      }
      return item;
    });
    
    updateData({ items: updatedItems });
  };

  const removeItem = (index: number) => {
    const updatedItems = data.items.filter((_, i) => i !== index);
    updateData({ items: updatedItems });
  };

  const SectionHeader = ({ title, section }: { title: string; section: string }) => (
    <button
      onClick={() => toggleSection(section)}
      className="flex items-center justify-between w-full p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors"
    >
      <h3 className="text-sm font-semibold text-gray-900">{title}</h3>
      {expandedSections[section] ? (
        <ChevronDown className="h-4 w-4 text-gray-500" />
      ) : (
        <ChevronRight className="h-4 w-4 text-gray-500" />
      )}
    </button>
  );

  const InputField = ({ 
    label, 
    value, 
    onChange, 
    type = 'text',
    placeholder = '',
    className = ''
  }: {
    label: string;
    value: string | number;
    onChange: (value: string | number) => void;
    type?: string;
    placeholder?: string;
    className?: string;
  }) => (
    <div className={className}>
      <label className="block text-xs font-medium text-gray-700 mb-1">
        {label}
      </label>
      <input
        type={type}
        value={value}
        onChange={(e) => onChange(type === 'number' ? parseFloat(e.target.value) || 0 : e.target.value)}
        placeholder={placeholder}
        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      />
    </div>
  );

  const TextAreaField = ({ 
    label, 
    value, 
    onChange, 
    rows = 3,
    className = ''
  }: {
    label: string;
    value: string;
    onChange: (value: string) => void;
    rows?: number;
    className?: string;
  }) => (
    <div className={className}>
      <label className="block text-xs font-medium text-gray-700 mb-1">
        {label}
      </label>
      <textarea
        value={value}
        onChange={(e) => onChange(e.target.value)}
        rows={rows}
        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      />
    </div>
  );

  return (
    <div className="space-y-4 max-h-[calc(100vh-200px)] overflow-y-auto">
      {/* Header Section */}
      <div>
        <SectionHeader 
          title={language === 'nl' ? 'Header Informatie' : 'Header Information'} 
          section="header" 
        />
        {expandedSections.header && (
          <div className="mt-3 space-y-3 pl-4">
            <InputField
              label={language === 'nl' ? 'Titel' : 'Title'}
              value={data.title}
              onChange={(value) => updateData({ title: value as string })}
            />
            <InputField
              label={language === 'nl' ? 'Bedrijfsnaam' : 'Company Name'}
              value={data.companyName}
              onChange={(value) => updateData({ companyName: value as string })}
            />
          </div>
        )}
      </div>

      {/* Client & Quote Section */}
      <div>
        <SectionHeader 
          title={language === 'nl' ? 'Klant & Offerte Details' : 'Client & Quote Details'} 
          section="client" 
        />
        {expandedSections.client && (
          <div className="mt-3 space-y-3 pl-4">
            <div className="grid grid-cols-1 gap-3">
              <InputField
                label={language === 'nl' ? 'Klant Naam' : 'Client Name'}
                value={data.client.name}
                onChange={(value) => updateNestedData('client', { name: value as string })}
              />
              <InputField
                label={language === 'nl' ? 'Adres' : 'Address'}
                value={data.client.address}
                onChange={(value) => updateNestedData('client', { address: value as string })}
              />
              <InputField
                label={language === 'nl' ? 'Stad' : 'City'}
                value={data.client.city}
                onChange={(value) => updateNestedData('client', { city: value as string })}
              />
              <InputField
                label={language === 'nl' ? 'Offerte Nummer' : 'Quote Number'}
                value={data.quote.number}
                onChange={(value) => updateNestedData('quote', { number: value as string })}
              />
              <InputField
                label={language === 'nl' ? 'Datum' : 'Date'}
                value={data.quote.date}
                onChange={(value) => updateNestedData('quote', { date: value as string })}
              />
              <InputField
                label={language === 'nl' ? 'Geldig tot' : 'Valid Until'}
                value={data.quote.validUntil}
                onChange={(value) => updateNestedData('quote', { validUntil: value as string })}
              />
            </div>
            
            <TextAreaField
              label={language === 'nl' ? 'Begroeting' : 'Greeting'}
              value={data.greeting}
              onChange={(value) => updateData({ greeting: value })}
              rows={1}
            />
            
            <TextAreaField
              label={language === 'nl' ? 'Introductie' : 'Introduction'}
              value={data.introduction}
              onChange={(value) => updateData({ introduction: value })}
              rows={3}
            />
          </div>
        )}
      </div>

      {/* Items Section */}
      <div>
        <SectionHeader 
          title={language === 'nl' ? 'Offerte Items' : 'Quote Items'} 
          section="items" 
        />
        {expandedSections.items && (
          <div className="mt-3 space-y-4 pl-4">
            {data.items.map((item, index) => (
              <div key={item.id} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                <div className="flex justify-between items-start mb-3">
                  <h4 className="text-sm font-medium text-gray-900">
                    {language === 'nl' ? 'Item' : 'Item'} {index + 1}
                  </h4>
                  <button
                    onClick={() => removeItem(index)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
                
                <div className="grid grid-cols-1 gap-3">
                  <InputField
                    label={language === 'nl' ? 'Beschrijving' : 'Description'}
                    value={item.description}
                    onChange={(value) => updateItem(index, { description: value as string })}
                  />
                  <InputField
                    label={language === 'nl' ? 'Sub-beschrijving' : 'Sub-description'}
                    value={item.subDescription}
                    onChange={(value) => updateItem(index, { subDescription: value as string })}
                  />
                  <div className="grid grid-cols-2 gap-3">
                    <InputField
                      label={language === 'nl' ? 'Aantal' : 'Quantity'}
                      value={item.quantity}
                      onChange={(value) => updateItem(index, { quantity: value as number })}
                      type="number"
                    />
                    <InputField
                      label={language === 'nl' ? 'Eenheid' : 'Unit'}
                      value={item.unit}
                      onChange={(value) => updateItem(index, { unit: value as string })}
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-3">
                    <InputField
                      label={language === 'nl' ? 'Prijs (€)' : 'Price (€)'}
                      value={item.price}
                      onChange={(value) => updateItem(index, { price: value as number })}
                      type="number"
                    />
                    <InputField
                      label={language === 'nl' ? 'BTW (%)' : 'VAT (%)'}
                      value={item.vatRate}
                      onChange={(value) => updateItem(index, { vatRate: value as number })}
                      type="number"
                    />
                  </div>
                  <div className="text-sm text-gray-600">
                    <strong>{language === 'nl' ? 'Totaal:' : 'Total:'} €{item.total.toFixed(2)}</strong>
                  </div>
                </div>
              </div>
            ))}
            
            <button
              onClick={addItem}
              className="w-full flex items-center justify-center px-4 py-2 border border-dashed border-gray-300 rounded-lg text-sm text-gray-600 hover:text-gray-900 hover:border-gray-400 transition-colors"
            >
              <Plus className="h-4 w-4 mr-2" />
              {language === 'nl' ? 'Item Toevoegen' : 'Add Item'}
            </button>
          </div>
        )}
      </div>

      {/* Notes Section */}
      <div>
        <SectionHeader
          title={language === 'nl' ? 'Opmerkingen & Voorwaarden' : 'Notes & Conditions'}
          section="notes"
        />
        {expandedSections.notes && (
          <div className="mt-3 space-y-3 pl-4">
            <TextAreaField
              label={language === 'nl' ? 'Opmerkingen' : 'Notes'}
              value={data.notes}
              onChange={(value) => updateData({ notes: value })}
              rows={4}
            />
            <TextAreaField
              label={language === 'nl' ? 'Voorwaarden' : 'Conditions'}
              value={data.conditions}
              onChange={(value) => updateData({ conditions: value })}
              rows={4}
            />
          </div>
        )}
      </div>

      {/* Company Section */}
      <div>
        <SectionHeader
          title={language === 'nl' ? 'Bedrijfsgegevens' : 'Company Information'}
          section="company"
        />
        {expandedSections.company && (
          <div className="mt-3 space-y-3 pl-4">
            <div className="grid grid-cols-1 gap-3">
              <InputField
                label={language === 'nl' ? 'Bedrijfsnaam' : 'Company Name'}
                value={data.company.name}
                onChange={(value) => updateNestedData('company', { name: value as string })}
              />
              <InputField
                label="KvK"
                value={data.company.kvk}
                onChange={(value) => updateNestedData('company', { kvk: value as string })}
              />
              <InputField
                label="BTW"
                value={data.company.btw}
                onChange={(value) => updateNestedData('company', { btw: value as string })}
              />
              <InputField
                label={language === 'nl' ? 'Adres' : 'Address'}
                value={data.company.address}
                onChange={(value) => updateNestedData('company', { address: value as string })}
              />
              <InputField
                label={language === 'nl' ? 'Stad' : 'City'}
                value={data.company.city}
                onChange={(value) => updateNestedData('company', { city: value as string })}
              />
              <InputField
                label="Website"
                value={data.company.website}
                onChange={(value) => updateNestedData('company', { website: value as string })}
              />
              <InputField
                label="Email"
                value={data.company.email}
                onChange={(value) => updateNestedData('company', { email: value as string })}
              />
              <InputField
                label={language === 'nl' ? 'Telefoon' : 'Phone'}
                value={data.company.phone}
                onChange={(value) => updateNestedData('company', { phone: value as string })}
              />
            </div>
          </div>
        )}
      </div>

      {/* Payment Section */}
      <div>
        <SectionHeader
          title={language === 'nl' ? 'Betaalgegevens' : 'Payment Information'}
          section="payment"
        />
        {expandedSections.payment && (
          <div className="mt-3 space-y-3 pl-4">
            <div className="grid grid-cols-1 gap-3">
              <InputField
                label="IBAN"
                value={data.payment.iban}
                onChange={(value) => updateNestedData('payment', { iban: value as string })}
              />
              <InputField
                label={language === 'nl' ? 'Rekeninghouder' : 'Account Name'}
                value={data.payment.accountName}
                onChange={(value) => updateNestedData('payment', { accountName: value as string })}
              />
              <InputField
                label={language === 'nl' ? 'Aanbetaling (%)' : 'Deposit (%)'}
                value={data.payment.depositPercentage}
                onChange={(value) => updateNestedData('payment', { depositPercentage: value as number })}
                type="number"
              />
              <InputField
                label={language === 'nl' ? 'Uurtarief (€)' : 'Hourly Rate (€)'}
                value={data.payment.hourlyRate}
                onChange={(value) => updateNestedData('payment', { hourlyRate: value as number })}
                type="number"
              />
              <InputField
                label={language === 'nl' ? 'Contantkorting (%)' : 'Cash Discount (%)'}
                value={data.payment.cashDiscountPercentage}
                onChange={(value) => updateNestedData('payment', { cashDiscountPercentage: value as number })}
                type="number"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
