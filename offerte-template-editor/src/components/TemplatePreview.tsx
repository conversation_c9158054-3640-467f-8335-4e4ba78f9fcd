'use client';

import { TemplateData, Language } from '@/types/template';

interface TemplatePreviewProps {
  data: TemplateData;
  language: Language;
}

export function TemplatePreview({ data, language }: TemplatePreviewProps) {
  // Calculate totals - items.total should be excluding VAT
  const subtotal = data.items.reduce((sum, item) => sum + item.total, 0);

  // Group VAT by rate
  const vatByRate = data.items.reduce((acc, item) => {
    const vatAmount = item.total * item.vatRate / 100;
    if (!acc[item.vatRate]) {
      acc[item.vatRate] = 0;
    }
    acc[item.vatRate] += vatAmount;
    return acc;
  }, {} as Record<number, number>);

  const totalVatAmount = Object.values(vatByRate).reduce((sum, vat) => sum + vat, 0);
  const total = subtotal + totalVatAmount;
  const depositAmount = total * (data.payment.depositPercentage / 100);
  const remainingAmount = total - depositAmount;
  const cashDiscountAmount = total * (data.payment.cashDiscountPercentage / 100);
  const totalWithCashDiscount = total - cashDiscountAmount;

  // Payment link logic - use default if empty
  const basePaymentLink = data.payment.paymentLink || 'www.ASklussen.nl/';
  const fullPaymentLink = `${basePaymentLink}${data.quote.number}?amount=${depositAmount.toFixed(2)}`;

  // Generate QR code URL from payment link
  const generateQRCode = () => {
    return `https://api.qrserver.com/v1/create-qr-code/?size=50x50&data=${encodeURIComponent(fullPaymentLink)}`;
  };

  // Replace dynamic values in text
  const replaceDynamicValues = (text: string) => {
    let result = text;

    // OPMERKINGEN: Replace cash discount calculations
    if (text.includes('contante betaling') || text.includes('cash payment')) {
      // Replace specific patterns step by step

      // 1. Replace total amount after "totaalbedrag van"
      result = result.replace(/(totaalbedrag van\s+)€\s*[\d.,]+/gi, `$1€ ${total.toFixed(2)}`);
      result = result.replace(/(total amount of\s+)€\s*[\d.,]+/gi, `$1€ ${total.toFixed(2)}`);

      // 2. Replace savings amount after "besparing"
      result = result.replace(/(besparing\s+)€\s*[\d.,]+/gi, `$1€ ${cashDiscountAmount.toFixed(2)}`);
      result = result.replace(/(saving\s+)€\s*[\d.,]+/gi, `$1€ ${cashDiscountAmount.toFixed(2)}`);

      // 3. Replace placeholder text
      result = result.replace(/hier moet %?5 van bedraag van offerte incl btw hier plaatsen €/gi, `€ ${cashDiscountAmount.toFixed(2)}`);
      result = result.replace(/here must %?5 of quote amount incl vat here place €/gi, `€ ${cashDiscountAmount.toFixed(2)}`);
    }

    // VOORWAARDEN: Replace deposit and hourly rate
    if (text.includes('aanbetaling van') || text.includes('deposit of')) {
      // Replace deposit amount after "aanbetaling van"
      result = result.replace(/(aanbetaling van\s+\d+%\s+\()€\s*[\d.,]+/gi, `$1€ ${depositAmount.toFixed(2)}`);
      result = result.replace(/(deposit of\s+\d+%\s+\()€\s*[\d.,]+/gi, `$1€ ${depositAmount.toFixed(2)}`);

      // Replace hourly rate after "tegen" or "at"
      result = result.replace(/(tegen\s+)€\s*[\d.,]+/gi, `$1€ ${data.payment.hourlyRate.toFixed(2)}`);
      result = result.replace(/(at\s+)€\s*[\d.,]+/gi, `$1€ ${data.payment.hourlyRate.toFixed(2)}`);

      // Add "excl. BTW" after hourly rate if not present
      if (!result.includes('excl. BTW') && !result.includes('excl. VAT')) {
        result = result.replace(/(€\s*[\d.,]+\/uur)/, '$1 excl. BTW');
      }
    }

    // VERVOLGSTAPPEN: Replace deposit and remaining amounts
    if (text.includes('Betaal') || text.includes('Pay') || text.includes('Restbetaling')) {
      result = result.replace(/€\s*[\d.,]+/g, (match, offset) => {
        const beforeMatch = text.substring(0, offset);

        // Deposit payment (30% of total incl. BTW)
        if (beforeMatch.includes('Betaal') || beforeMatch.includes('Pay')) {
          return `€ ${depositAmount.toFixed(2)}`;
        }
        // Remaining payment (70% of total incl. BTW)
        if (beforeMatch.includes('Restbetaling') || beforeMatch.includes('Final payment')) {
          return `€ ${remainingAmount.toFixed(2)}`;
        }
        return match;
      });
    }

    // Replace percentage values dynamically
    result = result.replace(/(\d+)%/g, (match, percentage) => {
      if (text.includes('aanbetaling') || text.includes('deposit')) {
        return `${data.payment.depositPercentage}%`;
      }
      if (text.includes('korting') || text.includes('discount')) {
        return `${data.payment.cashDiscountPercentage}%`;
      }
      return match;
    });

    // Replace quote numbers everywhere
    const oldQuotePattern = /OFF-2025-\d+|QUO-2025-\d+/g;
    result = result.replace(oldQuotePattern, data.quote.number);

    return result;
  };

  return (
    <div className="max-w-2xl mx-auto bg-white shadow-lg rounded-lg overflow-hidden">
      {/* Email-style container */}
      <div style={{ backgroundColor: '#f3f4f6', padding: '25px 10px' }}>
        <div style={{ 
          maxWidth: '580px', 
          margin: '0 auto',
          backgroundColor: '#ffffff', 
          borderRadius: '10px', 
          overflow: 'hidden', 
          boxShadow: '0 4px 15px rgba(0,0,0,0.08)' 
        }}>
          
          {/* Header */}
          <div style={{ backgroundColor: '#111827', color: '#ffffff', padding: '30px 25px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <h1 style={{ margin: 0, fontSize: '32px', fontWeight: 800, color: '#ffffff', letterSpacing: '1px' }}>
                  {data.title}
                </h1>
                <p style={{ margin: '8px 0 0 0', fontSize: '15px', color: '#d1d5db', fontWeight: 500 }}>
                  {data.companyName}
                </p>
              </div>
              <div style={{ textAlign: 'right' }}>
                <div style={{ 
                  background: 'linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%)', 
                  padding: '3px', 
                  borderRadius: '8px', 
                  display: 'inline-block',
                  boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)' 
                }}>
                  <div style={{ 
                    backgroundColor: '#1e40af', 
                    color: '#ffffff', 
                    padding: '10px 18px', 
                    borderRadius: '6px', 
                    fontSize: '15px', 
                    fontWeight: 700, 
                    letterSpacing: '0.5px' 
                  }}>
                    {data.quote.number}
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Content */}
          <div style={{ padding: '25px' }}>
            
            {/* Client & Quote Info */}
            <div style={{ display: 'flex', gap: '4%', marginBottom: '25px' }}>
              <div style={{ 
                width: '48%', 
                backgroundColor: '#f9fafb', 
                padding: '15px', 
                borderRadius: '8px', 
                borderLeft: '3px solid #3b82f6' 
              }}>
                <h3 style={{ margin: '0 0 10px 0', color: '#111827', fontSize: '14px', fontWeight: 600 }}>
                  {language === 'nl' ? 'OPDRACHTGEVER' : 'CLIENT'}
                </h3>
                <div style={{ fontSize: '13px', color: '#4b5563', lineHeight: 1.5 }}>
                  <strong style={{ color: '#111827' }}>{data.client.name}</strong><br />
                  {data.client.address}<br />
                  {data.client.city}
                </div>
              </div>
              
              <div style={{ 
                width: '48%', 
                backgroundColor: '#f9fafb', 
                padding: '15px', 
                borderRadius: '8px', 
                borderLeft: '3px solid #f59e0b' 
              }}>
                <h3 style={{ margin: '0 0 10px 0', color: '#111827', fontSize: '14px', fontWeight: 600 }}>
                  {language === 'nl' ? 'OFFERTE DETAILS' : 'QUOTE DETAILS'}
                </h3>
                <div style={{ fontSize: '13px', color: '#4b5563', lineHeight: 1.5 }}>
                  <strong style={{ color: '#111827' }}>
                    {language === 'nl' ? 'Nummer:' : 'Number:'}
                  </strong> {data.quote.number}<br />
                  <strong style={{ color: '#111827' }}>
                    {language === 'nl' ? 'Datum:' : 'Date:'}
                  </strong> {data.quote.date}<br />
                  <strong style={{ color: '#111827' }}>
                    {language === 'nl' ? 'Geldig tot:' : 'Valid until:'}
                  </strong> {data.quote.validUntil}
                </div>
              </div>
            </div>

            {/* Introduction */}
            <div style={{ 
              backgroundColor: '#eff6ff', 
              borderRadius: '8px', 
              margin: '25px 0', 
              borderLeft: '3px solid #3b82f6',
              padding: '15px'
            }}>
              <p style={{ margin: '0 0 8px 0', fontSize: '14px', color: '#1e40af', fontWeight: 600 }}>
                {data.greeting}
              </p>
              <p style={{ margin: 0, fontSize: '13px', color: '#1e40af', lineHeight: 1.6 }}>
                {data.introduction}
              </p>
            </div>

            {/* Cost Breakdown */}
            <div style={{ margin: '30px 0' }}>
              <h2 style={{ 
                color: '#0c4a6e', 
                fontSize: '16px', 
                fontWeight: 700, 
                margin: '0 0 12px 0', 
                textAlign: 'center', 
                textTransform: 'uppercase', 
                letterSpacing: '0.5px', 
                borderBottom: '2px solid #0ea5e9', 
                paddingBottom: '8px' 
              }}>
                {language === 'nl' ? 'Kostenoverzicht' : 'Cost Overview'}
              </h2>
              
              <table style={{ 
                width: '100%', 
                borderCollapse: 'collapse', 
                borderRadius: '8px', 
                overflow: 'hidden', 
                boxShadow: '0 2px 8px rgba(0,0,0,0.08)' 
              }}>
                {/* Header */}
                <thead>
                  <tr>
                    <th style={{ backgroundColor: '#0c4a6e', color: '#ffffff', padding: '10px 6px', textAlign: 'center', fontSize: '11px', fontWeight: 600, letterSpacing: '0.5px', textTransform: 'uppercase' }}>#</th>
                    <th style={{ backgroundColor: '#0c4a6e', color: '#ffffff', padding: '10px 12px', textAlign: 'left', fontSize: '11px', fontWeight: 600, letterSpacing: '0.5px', textTransform: 'uppercase' }}>
                      {language === 'nl' ? 'Omschrijving' : 'Description'}
                    </th>
                    <th style={{ backgroundColor: '#0c4a6e', color: '#ffffff', padding: '10px 6px', textAlign: 'center', fontSize: '11px', fontWeight: 600, letterSpacing: '0.5px', textTransform: 'uppercase' }}>
                      {language === 'nl' ? 'Aantal' : 'Qty'}
                    </th>
                    <th style={{ backgroundColor: '#0c4a6e', color: '#ffffff', padding: '10px 6px', textAlign: 'center', fontSize: '11px', fontWeight: 600, letterSpacing: '0.5px', textTransform: 'uppercase' }}>
                      {language === 'nl' ? 'Eenh.' : 'Unit'}
                    </th>
                    <th style={{ backgroundColor: '#0c4a6e', color: '#ffffff', padding: '10px 10px', textAlign: 'right', fontSize: '11px', fontWeight: 600, letterSpacing: '0.5px', textTransform: 'uppercase' }}>
                      {language === 'nl' ? 'Prijs' : 'Price'}
                    </th>
                    <th style={{ backgroundColor: '#0c4a6e', color: '#ffffff', padding: '10px 6px', textAlign: 'center', fontSize: '11px', fontWeight: 600, letterSpacing: '0.5px', textTransform: 'uppercase' }}>
                      {language === 'nl' ? 'BTW' : 'VAT'}
                    </th>
                    <th style={{ backgroundColor: '#0c4a6e', color: '#ffffff', padding: '10px 10px', textAlign: 'right', fontSize: '11px', fontWeight: 600, letterSpacing: '0.5px', textTransform: 'uppercase' }}>
                      {language === 'nl' ? 'Totaal' : 'Total'}
                    </th>
                  </tr>
                </thead>
                
                {/* Items */}
                <tbody>
                  {data.items.map((item, index) => (
                    <tr key={item.id} style={{ backgroundColor: index % 2 === 0 ? '#ffffff' : '#f0f9ff' }}>
                      <td style={{ padding: '10px 6px', borderBottom: '1px solid #e5e7eb', textAlign: 'center', fontSize: '12px', color: '#6b7280' }}>
                        {item.itemNumber}
                      </td>
                      <td style={{ padding: '10px 12px', borderBottom: '1px solid #e5e7eb' }}>
                        <div style={{ fontWeight: 600, color: '#0c4a6e', fontSize: '12px' }}>
                          {item.description}
                        </div>
                        <div style={{ color: '#6b7280', fontSize: '10px' }}>
                          {item.subDescription}
                        </div>
                      </td>
                      <td style={{ padding: '10px 6px', borderBottom: '1px solid #e5e7eb', textAlign: 'center', fontSize: '12px', color: '#4b5563' }}>
                        {item.quantity}
                      </td>
                      <td style={{ padding: '10px 6px', borderBottom: '1px solid #e5e7eb', textAlign: 'center', fontSize: '12px', color: '#4b5563' }}>
                        {item.unit}
                      </td>
                      <td style={{ padding: '10px 10px', borderBottom: '1px solid #e5e7eb', textAlign: 'right', fontSize: '12px', color: '#4b5563' }}>
                        <span style={{ fontFamily: 'monospace', whiteSpace: 'nowrap' }}>€&nbsp;{item.price.toFixed(2)}</span>
                      </td>
                      <td style={{ padding: '10px 6px', borderBottom: '1px solid #e5e7eb', textAlign: 'center', fontSize: '12px', color: '#4b5563' }}>
                        {item.vatRate}%
                      </td>
                      <td style={{ padding: '10px 10px', borderBottom: '1px solid #e5e7eb', textAlign: 'right', fontWeight: 600, color: '#0c4a6e', fontSize: '12px' }}>
                        <span style={{ fontFamily: 'monospace', whiteSpace: 'nowrap' }}>€&nbsp;{item.total.toFixed(2)}</span>
                      </td>
                    </tr>
                  ))}
                </tbody>
                
                {/* Totals */}
                <tfoot>
                  <tr style={{ backgroundColor: '#f0f9ff' }}>
                    <td colSpan={6} style={{ padding: '10px 12px', textAlign: 'right', fontWeight: 600, fontSize: '12px', color: '#0c4a6e', letterSpacing: '0.3px' }}>
                      {language === 'nl' ? 'Subtotaal (excl. BTW)' : 'Subtotal (excl. VAT)'}
                    </td>
                    <td style={{ padding: '10px 10px', textAlign: 'right', fontWeight: 600, color: '#0c4a6e', fontSize: '12px' }}>
                      <span style={{ fontFamily: 'monospace', whiteSpace: 'nowrap' }}>€&nbsp;{subtotal.toFixed(2)}</span>
                    </td>
                  </tr>
                  {Object.entries(vatByRate).map(([rate, amount]) => (
                    <tr key={rate} style={{ backgroundColor: '#f0f9ff' }}>
                      <td colSpan={6} style={{ padding: '6px 12px', textAlign: 'right', fontSize: '12px', color: '#4b5563' }}>
                        {language === 'nl' ? `BTW (${rate}%)` : `VAT (${rate}%)`}
                      </td>
                      <td style={{ padding: '6px 10px', textAlign: 'right', fontWeight: 600, color: '#4b5563', fontSize: '12px' }}>
                        <span style={{ fontFamily: 'monospace', whiteSpace: 'nowrap' }}>€&nbsp;{amount.toFixed(2)}</span>
                      </td>
                    </tr>
                  ))}
                  <tr style={{ background: 'linear-gradient(90deg, #0ea5e9 0%, #0c4a6e 100%)' }}>
                    <td colSpan={6} style={{ padding: '12px', textAlign: 'right', color: '#ffffff', fontSize: '13px', fontWeight: 700, letterSpacing: '0.5px' }}>
                      {language === 'nl' ? 'TOTAAL INCL. BTW' : 'TOTAL INCL. VAT'}
                    </td>
                    <td style={{ padding: '12px 10px', textAlign: 'right', color: '#ffffff', fontSize: '15px', fontWeight: 700 }}>
                      <span style={{ fontFamily: 'monospace', whiteSpace: 'nowrap' }}>€&nbsp;{total.toFixed(2)}</span>
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>

            {/* Notes */}
            {data.notes && (
              <div style={{ 
                backgroundColor: '#fffbeb', 
                borderRadius: '8px', 
                marginBottom: '15px', 
                borderLeft: '3px solid #f59e0b',
                padding: '15px'
              }}>
                <h3 style={{ margin: '0 0 8px 0', color: '#92400e', fontSize: '14px', fontWeight: 600 }}>
                  {language === 'nl' ? 'OPMERKINGEN' : 'NOTES'}
                </h3>
                <p style={{ fontSize: '12px', color: '#92400e', lineHeight: 1.5, margin: 0 }}>
                  {replaceDynamicValues(data.notes)}
                </p>
              </div>
            )}

            {/* Conditions */}
            {data.conditions && (
              <div style={{
                backgroundColor: '#fef2f2',
                borderRadius: '8px',
                marginBottom: '15px',
                borderLeft: '3px solid #ef4444',
                padding: '15px'
              }}>
                <h3 style={{ margin: '0 0 8px 0', color: '#991b1b', fontSize: '14px', fontWeight: 600 }}>
                  {language === 'nl' ? 'VOORWAARDEN' : 'CONDITIONS'}
                </h3>
                <p style={{ fontSize: '12px', color: '#991b1b', lineHeight: 1.5, margin: 0 }}>
                  {replaceDynamicValues(data.conditions)}
                </p>
              </div>
            )}

            {/* Next Steps */}
            <div style={{
              backgroundColor: '#f0fff4',
              borderRadius: '8px',
              margin: '25px 0',
              borderLeft: '3px solid #10b981',
              padding: '15px'
            }}>
              <h3 style={{ margin: '0 0 10px 0', color: '#065f46', fontSize: '14px', fontWeight: 600 }}>
                {language === 'nl' ? 'VERVOLGSTAPPEN' : 'NEXT STEPS'}
              </h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                  <div style={{
                    backgroundColor: '#10b981',
                    color: '#ffffff',
                    width: '28px',
                    height: '28px',
                    borderRadius: '50%',
                    textAlign: 'center',
                    lineHeight: '28px',
                    fontSize: '12px',
                    fontWeight: 700,
                    marginRight: '15px',
                    flexShrink: 0
                  }}>1</div>
                  <div style={{ fontSize: '12px', color: '#065f46', lineHeight: 1.5 }}>
                    {replaceDynamicValues(data.nextSteps.step1)}
                  </div>
                </div>
                <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                  <div style={{
                    backgroundColor: '#10b981',
                    color: '#ffffff',
                    width: '28px',
                    height: '28px',
                    borderRadius: '50%',
                    textAlign: 'center',
                    lineHeight: '28px',
                    fontSize: '12px',
                    fontWeight: 700,
                    marginRight: '15px',
                    flexShrink: 0
                  }}>2</div>
                  <div style={{ fontSize: '12px', color: '#065f46', lineHeight: 1.5 }}>
                    {replaceDynamicValues(data.nextSteps.step2)}
                  </div>
                </div>
                <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                  <div style={{
                    backgroundColor: '#10b981',
                    color: '#ffffff',
                    width: '28px',
                    height: '28px',
                    borderRadius: '50%',
                    textAlign: 'center',
                    lineHeight: '28px',
                    fontSize: '12px',
                    fontWeight: 700,
                    marginRight: '15px',
                    flexShrink: 0
                  }}>3</div>
                  <div style={{ fontSize: '12px', color: '#065f46', lineHeight: 1.5 }}>
                    {replaceDynamicValues(data.nextSteps.step3)}
                  </div>
                </div>
                <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                  <div style={{
                    backgroundColor: '#10b981',
                    color: '#ffffff',
                    width: '28px',
                    height: '28px',
                    borderRadius: '50%',
                    textAlign: 'center',
                    lineHeight: '28px',
                    fontSize: '12px',
                    fontWeight: 700,
                    marginRight: '15px',
                    flexShrink: 0
                  }}>4</div>
                  <div style={{ fontSize: '12px', color: '#065f46', lineHeight: 1.5 }}>
                    {replaceDynamicValues(data.nextSteps.step4)}
                  </div>
                </div>
              </div>
            </div>

            {/* Payment Options */}
            <div style={{
              backgroundColor: '#f0f9ff',
              borderRadius: '8px',
              margin: '25px 0',
              borderLeft: '3px solid #0ea5e9',
              padding: '15px'
            }}>
              <h3 style={{ margin: '0 0 10px 0', color: '#0c4a6e', fontSize: '14px', fontWeight: 600, textAlign: 'center' }}>
                {language === 'nl' ? 'BETAALOPTIES AANBETALING' : 'DEPOSIT PAYMENT OPTIONS'} (€ {depositAmount.toFixed(2)})
              </h3>

              <div style={{
                backgroundColor: '#ffffff',
                borderRadius: '6px',
                boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                display: 'flex'
              }}>
                <div style={{
                  width: '30%',
                  padding: '12px',
                  textAlign: 'center',
                  borderRight: '1px solid #f3f4f6'
                }}>
                  <h4 style={{ margin: '0 0 5px 0', color: '#111827', fontSize: '12px', fontWeight: 600 }}>
                    {language === 'nl' ? 'iDEAL Betaling' : 'iDEAL Payment'}
                  </h4>
                  <a
                    href={fullPaymentLink}
                    style={{
                      backgroundColor: '#10b981',
                      color: '#ffffff',
                      padding: '8px 15px',
                      fontSize: '12px',
                      fontWeight: 600,
                      display: 'inline-block',
                      borderRadius: '4px',
                      margin: '5px 0',
                      textDecoration: 'none'
                    }}
                  >
                    {language === 'nl' ? 'NU BETALEN' : 'PAY NOW'}
                  </a>
                  <p style={{ margin: '5px 0 0 0', fontSize: '10px', color: '#6b7280' }}>
                    {language === 'nl' ? 'Snel en veilig online betalen' : 'Fast and secure online payment'}
                  </p>
                </div>
                <div style={{
                  width: '30%',
                  padding: '12px',
                  textAlign: 'center',
                  borderRight: '1px solid #f3f4f6'
                }}>
                  <h4 style={{ margin: '0 0 5px 0', color: '#111827', fontSize: '12px', fontWeight: 600 }}>
                    {language === 'nl' ? 'QR Code Betaling' : 'QR Code Payment'}
                  </h4>
                  <div style={{
                    border: '2px solid #10b981',
                    borderRadius: '8px',
                    padding: '5px',
                    backgroundColor: '#ffffff',
                    boxShadow: '0 2px 6px rgba(16, 185, 129, 0.2)',
                    width: '60px',
                    height: '60px',
                    margin: '0 auto',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    {data.payment.paymentLink ? (
                      <img
                        src={generateQRCode()}
                        width="50"
                        height="50"
                        alt="QR Code"
                        style={{ display: 'block' }}
                      />
                    ) : (
                      <div style={{
                        width: '50px',
                        height: '50px',
                        backgroundColor: '#f3f4f6',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '8px',
                        color: '#6b7280'
                      }}>QR</div>
                    )}
                  </div>
                  <p style={{ margin: '5px 0 0 0', fontSize: '10px', color: '#6b7280' }}>
                    {language === 'nl' ? 'Scan met uw bank app' : 'Scan with your bank app'}
                  </p>
                </div>
                <div style={{ width: '40%', padding: '12px', textAlign: 'center' }}>
                  <h4 style={{ margin: '0 0 5px 0', color: '#111827', fontSize: '12px', fontWeight: 600 }}>
                    {language === 'nl' ? 'Handmatig Overmaken' : 'Manual Transfer'}
                  </h4>
                  <div style={{ fontSize: '11px' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', padding: '2px 0' }}>
                      <span style={{ fontWeight: 600, color: '#111827' }}>IBAN:</span>
                      <span style={{ color: '#4b5563' }}>{data.payment.iban}</span>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', padding: '2px 0' }}>
                      <span style={{ fontWeight: 600, color: '#111827' }}>
                        {language === 'nl' ? 'T.n.v.:' : 'To:'}
                      </span>
                      <span style={{ color: '#4b5563' }}>{data.payment.accountName}</span>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', padding: '2px 0' }}>
                      <span style={{ fontWeight: 600, color: '#111827' }}>
                        {language === 'nl' ? 'O.v.v.:' : 'Ref:'}
                      </span>
                      <span style={{ color: '#3b82f6', fontWeight: 600 }}>{data.quote.number}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Contact */}
            <div style={{ 
              backgroundColor: '#ecfdf5', 
              borderRadius: '8px', 
              margin: '25px 0', 
              borderLeft: '3px solid #10b981',
              padding: '15px'
            }}>
              <h3 style={{ margin: '0 0 10px 0', color: '#065f46', fontSize: '14px', fontWeight: 600, textAlign: 'center' }}>
                {data.contactTitle}
              </h3>
              <p style={{ fontSize: '12px', color: '#065f46', lineHeight: 1.5, margin: '0 0 12px 0', textAlign: 'center' }}>
                {data.contactDescription}
              </p>
              <div style={{ display: 'flex', justifyContent: 'space-around', textAlign: 'center' }}>
                <div>
                  <div style={{ fontWeight: 600, marginBottom: '3px', fontSize: '12px', color: '#065f46' }}>
                    {language === 'nl' ? 'Telefoon' : 'Phone'}
                  </div>
                  <div style={{ color: '#065f46', fontWeight: 600, fontSize: '12px' }}>
                    {data.company.phone}
                  </div>
                </div>
                <div>
                  <div style={{ fontWeight: 600, marginBottom: '3px', fontSize: '12px', color: '#065f46' }}>
                    Email
                  </div>
                  <div style={{ color: '#065f46', fontWeight: 600, fontSize: '12px' }}>
                    {data.company.email}
                  </div>
                </div>
                <div>
                  <div style={{ fontWeight: 600, marginBottom: '3px', fontSize: '12px', color: '#065f46' }}>
                    WhatsApp
                  </div>
                  <div style={{ color: '#065f46', fontWeight: 600, fontSize: '12px' }}>
                    {data.company.phone}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div style={{ backgroundColor: '#111827', color: '#9ca3af', padding: '20px', textAlign: 'center', fontSize: '12px', lineHeight: 1.5 }}>
            <div style={{ color: '#ffffff', fontWeight: 600, marginBottom: '5px', fontSize: '14px' }}>
              {data.company.name}
            </div>
            <div>
              KvK: {data.company.kvk} | BTW: {data.company.btw}<br />
              {data.company.address}, {data.company.city}<br />
              <span style={{ color: '#60a5fa', fontWeight: 600 }}>{data.company.website}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
