{"version": 3, "sources": ["../../../src/lib/eslint/writeOutputFile.ts"], "sourcesContent": ["import { promises as fs } from 'fs'\nimport path from 'path'\nimport * as Log from '../../build/output/log'\nimport isError from '../../lib/is-error'\n\n/**\n * Check if a given file path is a directory or not.\n * Returns `true` if the path is a directory.\n */\nfunction isDirectory(\n  /**  The path to a file to check. */\n  filePath: string\n): Promise<boolean> {\n  return fs\n    .stat(filePath)\n    .then((stat) => stat.isDirectory())\n    .catch((error) => {\n      if (\n        isError(error) &&\n        (error.code === 'ENOENT' || error.code === 'ENOTDIR')\n      ) {\n        return false\n      }\n      throw error\n    })\n}\n/**\n * Create a file with eslint output data\n */\nexport async function writeOutputFile(\n  /** The name file that needs to be created */\n  outputFile: string,\n  /** The data that needs to be inserted into the file */\n  outputData: string\n): Promise<void> {\n  const filePath = path.resolve(process.cwd(), outputFile)\n\n  if (await isDirectory(filePath)) {\n    Log.error(\n      `Cannot write to output file path, it is a directory: ${filePath}`\n    )\n  } else {\n    try {\n      await fs.mkdir(path.dirname(filePath), { recursive: true })\n      await fs.writeFile(filePath, outputData)\n      Log.info(`The output file has been created: ${filePath}`)\n    } catch (err) {\n      Log.error(`There was a problem writing the output file: ${filePath}`)\n      console.error(err)\n    }\n  }\n}\n"], "names": ["writeOutputFile", "isDirectory", "filePath", "fs", "stat", "then", "catch", "error", "isError", "code", "outputFile", "outputData", "path", "resolve", "process", "cwd", "Log", "mkdir", "dirname", "recursive", "writeFile", "info", "err", "console"], "mappings": ";;;;+BA6BsBA;;;eAAAA;;;oBA7BS;6DACd;6DACI;gEACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEpB;;;CAGC,GACD,SAASC,YACP,kCAAkC,GAClCC,QAAgB;IAEhB,OAAOC,YAAE,CACNC,IAAI,CAACF,UACLG,IAAI,CAAC,CAACD,OAASA,KAAKH,WAAW,IAC/BK,KAAK,CAAC,CAACC;QACN,IACEC,IAAAA,gBAAO,EAACD,UACPA,CAAAA,MAAME,IAAI,KAAK,YAAYF,MAAME,IAAI,KAAK,SAAQ,GACnD;YACA,OAAO;QACT;QACA,MAAMF;IACR;AACJ;AAIO,eAAeP,gBACpB,2CAA2C,GAC3CU,UAAkB,EAClB,qDAAqD,GACrDC,UAAkB;IAElB,MAAMT,WAAWU,aAAI,CAACC,OAAO,CAACC,QAAQC,GAAG,IAAIL;IAE7C,IAAI,MAAMT,YAAYC,WAAW;QAC/Bc,KAAIT,KAAK,CACP,CAAC,qDAAqD,EAAEL,UAAU;IAEtE,OAAO;QACL,IAAI;YACF,MAAMC,YAAE,CAACc,KAAK,CAACL,aAAI,CAACM,OAAO,CAAChB,WAAW;gBAAEiB,WAAW;YAAK;YACzD,MAAMhB,YAAE,CAACiB,SAAS,CAAClB,UAAUS;YAC7BK,KAAIK,IAAI,CAAC,CAAC,kCAAkC,EAAEnB,UAAU;QAC1D,EAAE,OAAOoB,KAAK;YACZN,KAAIT,KAAK,CAAC,CAAC,6CAA6C,EAAEL,UAAU;YACpEqB,QAAQhB,KAAK,CAACe;QAChB;IACF;AACF", "ignoreList": [0]}