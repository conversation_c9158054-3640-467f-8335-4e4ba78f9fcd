{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/webpackModuleError.ts"], "sourcesContent": ["import { readFileSync } from 'fs'\nimport * as path from 'path'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\n\nimport { getBabelError } from './parseBabel'\nimport { getCssError } from './parseCss'\nimport { getScssError } from './parseScss'\nimport { getNotFoundError, getImageError } from './parseNotFoundError'\nimport type { SimpleWebpackError } from './simpleWebpackError'\nimport isError from '../../../../lib/is-error'\nimport { getNextFontError } from './parseNextFontError'\nimport { getNextAppLoaderError } from './parseNextAppLoaderError'\nimport { getNextInvalidImportError } from './parseNextInvalidImportError'\n\nfunction getFileData(\n  compilation: webpack.Compilation,\n  m: any\n): [string, string | null] {\n  let resolved: string\n  let ctx: string | null = compilation.compiler?.context ?? null\n  if (ctx !== null && typeof m.resource === 'string') {\n    const res = path.relative(ctx, m.resource).replace(/\\\\/g, path.posix.sep)\n    resolved = res.startsWith('.') ? res : `.${path.posix.sep}${res}`\n  } else {\n    const requestShortener = compilation.requestShortener\n    if (typeof m?.readableIdentifier === 'function') {\n      resolved = m.readableIdentifier(requestShortener)\n    } else {\n      resolved = m.request ?? m.userRequest\n    }\n  }\n\n  if (resolved) {\n    let content: string | null = null\n    try {\n      content = readFileSync(\n        ctx ? path.resolve(ctx, resolved) : resolved,\n        'utf8'\n      )\n    } catch {}\n    return [resolved, content]\n  }\n\n  return ['<unknown>', null]\n}\n\nexport async function getModuleBuildError(\n  compiler: webpack.Compiler,\n  compilation: webpack.Compilation,\n  input: any\n): Promise<SimpleWebpackError | false> {\n  if (\n    !(\n      typeof input === 'object' &&\n      (input?.name === 'ModuleBuildError' ||\n        input?.name === 'ModuleNotFoundError') &&\n      Boolean(input.module) &&\n      isError(input.error)\n    )\n  ) {\n    return false\n  }\n\n  const err: Error = input.error\n  const [sourceFilename, sourceContent] = getFileData(compilation, input.module)\n\n  const notFoundError = await getNotFoundError(\n    compilation,\n    input,\n    sourceFilename,\n    input.module\n  )\n  if (notFoundError !== false) {\n    return notFoundError\n  }\n\n  const imageError = await getImageError(compilation, input, err)\n  if (imageError !== false) {\n    return imageError\n  }\n\n  const babel = getBabelError(sourceFilename, err)\n  if (babel !== false) {\n    return babel\n  }\n\n  const css = getCssError(sourceFilename, err)\n  if (css !== false) {\n    return css\n  }\n\n  const scss = getScssError(sourceFilename, sourceContent, err)\n  if (scss !== false) {\n    return scss\n  }\n\n  const nextFont = getNextFontError(err, input.module)\n  if (nextFont !== false) {\n    return nextFont\n  }\n\n  const nextAppLoader = getNextAppLoaderError(err, input.module, compiler)\n  if (nextAppLoader !== false) {\n    return nextAppLoader\n  }\n\n  const invalidImportError = getNextInvalidImportError(\n    err,\n    input.module,\n    compilation,\n    compiler\n  )\n  if (invalidImportError !== false) {\n    return invalidImportError\n  }\n\n  return false\n}\n"], "names": ["getModuleBuildError", "getFileData", "compilation", "m", "resolved", "ctx", "compiler", "context", "resource", "res", "path", "relative", "replace", "posix", "sep", "startsWith", "requestShortener", "readableIdentifier", "request", "userRequest", "content", "readFileSync", "resolve", "input", "name", "Boolean", "module", "isError", "error", "err", "sourceFilename", "sourceContent", "notFoundError", "getNotFoundError", "imageError", "getImageError", "babel", "getBabelError", "css", "getCssError", "scss", "getScssError", "nextFont", "getNextFontError", "nextApp<PERSON><PERSON>der", "getNextAppLoaderError", "invalidImportError", "getNextInvalidImportError"], "mappings": ";;;;+BA8CsBA;;;eAAAA;;;oBA9CO;8DACP;4BAGQ;0BACF;2BACC;oCACmB;gEAE5B;oCACa;yCACK;6CACI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE1C,SAASC,YACPC,WAAgC,EAChCC,CAAM;QAGmBD;IADzB,IAAIE;IACJ,IAAIC,MAAqBH,EAAAA,wBAAAA,YAAYI,QAAQ,qBAApBJ,sBAAsBK,OAAO,KAAI;IAC1D,IAAIF,QAAQ,QAAQ,OAAOF,EAAEK,QAAQ,KAAK,UAAU;QAClD,MAAMC,MAAMC,MAAKC,QAAQ,CAACN,KAAKF,EAAEK,QAAQ,EAAEI,OAAO,CAAC,OAAOF,MAAKG,KAAK,CAACC,GAAG;QACxEV,WAAWK,IAAIM,UAAU,CAAC,OAAON,MAAM,CAAC,CAAC,EAAEC,MAAKG,KAAK,CAACC,GAAG,GAAGL,KAAK;IACnE,OAAO;QACL,MAAMO,mBAAmBd,YAAYc,gBAAgB;QACrD,IAAI,QAAOb,qBAAAA,EAAGc,kBAAkB,MAAK,YAAY;YAC/Cb,WAAWD,EAAEc,kBAAkB,CAACD;QAClC,OAAO;YACLZ,WAAWD,EAAEe,OAAO,IAAIf,EAAEgB,WAAW;QACvC;IACF;IAEA,IAAIf,UAAU;QACZ,IAAIgB,UAAyB;QAC7B,IAAI;YACFA,UAAUC,IAAAA,gBAAY,EACpBhB,MAAMK,MAAKY,OAAO,CAACjB,KAAKD,YAAYA,UACpC;QAEJ,EAAE,OAAM,CAAC;QACT,OAAO;YAACA;YAAUgB;SAAQ;IAC5B;IAEA,OAAO;QAAC;QAAa;KAAK;AAC5B;AAEO,eAAepB,oBACpBM,QAA0B,EAC1BJ,WAAgC,EAChCqB,KAAU;IAEV,IACE,CACE,CAAA,OAAOA,UAAU,YAChBA,CAAAA,CAAAA,yBAAAA,MAAOC,IAAI,MAAK,sBACfD,CAAAA,yBAAAA,MAAOC,IAAI,MAAK,qBAAoB,KACtCC,QAAQF,MAAMG,MAAM,KACpBC,IAAAA,gBAAO,EAACJ,MAAMK,KAAK,CAAA,GAErB;QACA,OAAO;IACT;IAEA,MAAMC,MAAaN,MAAMK,KAAK;IAC9B,MAAM,CAACE,gBAAgBC,cAAc,GAAG9B,YAAYC,aAAaqB,MAAMG,MAAM;IAE7E,MAAMM,gBAAgB,MAAMC,IAAAA,oCAAgB,EAC1C/B,aACAqB,OACAO,gBACAP,MAAMG,MAAM;IAEd,IAAIM,kBAAkB,OAAO;QAC3B,OAAOA;IACT;IAEA,MAAME,aAAa,MAAMC,IAAAA,iCAAa,EAACjC,aAAaqB,OAAOM;IAC3D,IAAIK,eAAe,OAAO;QACxB,OAAOA;IACT;IAEA,MAAME,QAAQC,IAAAA,yBAAa,EAACP,gBAAgBD;IAC5C,IAAIO,UAAU,OAAO;QACnB,OAAOA;IACT;IAEA,MAAME,MAAMC,IAAAA,qBAAW,EAACT,gBAAgBD;IACxC,IAAIS,QAAQ,OAAO;QACjB,OAAOA;IACT;IAEA,MAAME,OAAOC,IAAAA,uBAAY,EAACX,gBAAgBC,eAAeF;IACzD,IAAIW,SAAS,OAAO;QAClB,OAAOA;IACT;IAEA,MAAME,WAAWC,IAAAA,oCAAgB,EAACd,KAAKN,MAAMG,MAAM;IACnD,IAAIgB,aAAa,OAAO;QACtB,OAAOA;IACT;IAEA,MAAME,gBAAgBC,IAAAA,8CAAqB,EAAChB,KAAKN,MAAMG,MAAM,EAAEpB;IAC/D,IAAIsC,kBAAkB,OAAO;QAC3B,OAAOA;IACT;IAEA,MAAME,qBAAqBC,IAAAA,sDAAyB,EAClDlB,KACAN,MAAMG,MAAM,EACZxB,aACAI;IAEF,IAAIwC,uBAAuB,OAAO;QAChC,OAAOA;IACT;IAEA,OAAO;AACT", "ignoreList": [0]}