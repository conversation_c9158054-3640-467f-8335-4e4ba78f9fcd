{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseNextAppLoaderError.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { relative } from 'path'\nimport { SimpleWebpackError } from './simpleWebpackError'\nimport { getAppLoader } from '../../../entries'\n\nexport function getNextAppLoaderError(\n  err: Error,\n  module: any,\n  compiler: webpack.Compiler\n): SimpleWebpackError | false {\n  try {\n    if (!module.loaders[0].loader.includes(getAppLoader())) {\n      return false\n    }\n\n    const file = relative(\n      compiler.context,\n      module.buildInfo.route.absolutePagePath\n    )\n\n    return new SimpleWebpackError(file, err.message)\n  } catch {\n    return false\n  }\n}\n"], "names": ["getNextAppLoaderError", "err", "module", "compiler", "loaders", "loader", "includes", "getApp<PERSON><PERSON>der", "file", "relative", "context", "buildInfo", "route", "absolutePagePath", "SimpleWebpackError", "message"], "mappings": ";;;;+BAKgBA;;;eAAAA;;;sBAJS;oCACU;yBACN;AAEtB,SAASA,sBACdC,GAAU,EACVC,MAAW,EACXC,QAA0B;IAE1B,IAAI;QACF,IAAI,CAACD,OAAOE,OAAO,CAAC,EAAE,CAACC,MAAM,CAACC,QAAQ,CAACC,IAAAA,qBAAY,MAAK;YACtD,OAAO;QACT;QAEA,MAAMC,OAAOC,IAAAA,cAAQ,EACnBN,SAASO,OAAO,EAChBR,OAAOS,SAAS,CAACC,KAAK,CAACC,gBAAgB;QAGzC,OAAO,IAAIC,sCAAkB,CAACN,MAAMP,IAAIc,OAAO;IACjD,EAAE,OAAM;QACN,OAAO;IACT;AACF", "ignoreList": [0]}