{"version": 3, "sources": ["../../../src/build/webpack-build/index.ts"], "sourcesContent": ["import type { COMPILER_INDEXES } from '../../shared/lib/constants'\nimport * as Log from '../output/log'\nimport { NextBuildContext } from '../build-context'\nimport type { BuildTraceContext } from '../webpack/plugins/next-trace-entrypoints-plugin'\nimport { Worker } from '../../lib/worker'\nimport origDebug from 'next/dist/compiled/debug'\nimport path from 'path'\nimport { exportTraceState, recordTraceEvents } from '../../trace'\nimport { mergeUseCacheTrackers } from '../webpack/plugins/telemetry-plugin/use-cache-tracker-utils'\nimport { durationToString } from '../duration-to-string'\n\nconst debug = origDebug('next:build:webpack-build')\n\nconst ORDERED_COMPILER_NAMES = [\n  'server',\n  'edge-server',\n  'client',\n] as (keyof typeof COMPILER_INDEXES)[]\n\nlet pluginState: Record<any, any> = {}\n\nfunction deepMerge(target: any, source: any) {\n  const result = { ...target, ...source }\n  for (const key of Object.keys(result)) {\n    result[key] = Array.isArray(target[key])\n      ? (target[key] = [...target[key], ...(source[key] || [])])\n      : typeof target[key] == 'object' && typeof source[key] == 'object'\n        ? deepMerge(target[key], source[key])\n        : result[key]\n  }\n  return result\n}\n\nasync function webpackBuildWithWorker(\n  compilerNamesArg: typeof ORDERED_COMPILER_NAMES | null\n) {\n  const compilerNames = compilerNamesArg || ORDERED_COMPILER_NAMES\n  const { nextBuildSpan, ...prunedBuildContext } = NextBuildContext\n\n  prunedBuildContext.pluginState = pluginState\n\n  const combinedResult = {\n    duration: 0,\n    buildTraceContext: {} as BuildTraceContext,\n  }\n\n  for (const compilerName of compilerNames) {\n    const worker = new Worker(path.join(__dirname, 'impl.js'), {\n      exposedMethods: ['workerMain'],\n      debuggerPortOffset: -1,\n      isolatedMemory: false,\n      numWorkers: 1,\n      maxRetries: 0,\n      forkOptions: {\n        env: {\n          NEXT_PRIVATE_BUILD_WORKER: '1',\n        },\n      },\n    }) as Worker & typeof import('./impl')\n\n    const curResult = await worker.workerMain({\n      buildContext: prunedBuildContext,\n      compilerName,\n      traceState: {\n        ...exportTraceState(),\n        defaultParentSpanId: nextBuildSpan?.getId(),\n        shouldSaveTraceEvents: true,\n      },\n    })\n    if (nextBuildSpan && curResult.debugTraceEvents) {\n      recordTraceEvents(curResult.debugTraceEvents)\n    }\n    // destroy worker so it's not sticking around using memory\n    await worker.end()\n\n    // Update plugin state\n    pluginState = deepMerge(pluginState, curResult.pluginState)\n    prunedBuildContext.pluginState = pluginState\n\n    if (curResult.telemetryState) {\n      NextBuildContext.telemetryState = {\n        ...curResult.telemetryState,\n        useCacheTracker: mergeUseCacheTrackers(\n          NextBuildContext.telemetryState?.useCacheTracker,\n          curResult.telemetryState.useCacheTracker\n        ),\n      }\n    }\n\n    combinedResult.duration += curResult.duration\n\n    if (curResult.buildTraceContext?.entriesTrace) {\n      const { entryNameMap } = curResult.buildTraceContext.entriesTrace!\n\n      if (entryNameMap) {\n        combinedResult.buildTraceContext.entriesTrace =\n          curResult.buildTraceContext.entriesTrace\n        combinedResult.buildTraceContext.entriesTrace!.entryNameMap =\n          entryNameMap\n      }\n\n      if (curResult.buildTraceContext?.chunksTrace) {\n        const { entryNameFilesMap } = curResult.buildTraceContext.chunksTrace!\n\n        if (entryNameFilesMap) {\n          combinedResult.buildTraceContext.chunksTrace =\n            curResult.buildTraceContext.chunksTrace!\n\n          combinedResult.buildTraceContext.chunksTrace!.entryNameFilesMap =\n            entryNameFilesMap\n        }\n      }\n    }\n  }\n\n  if (compilerNames.length === 3) {\n    const durationString = durationToString(combinedResult.duration)\n    Log.event(`Compiled successfully in ${durationString}`)\n  }\n\n  return combinedResult\n}\n\nexport async function webpackBuild(\n  withWorker: boolean,\n  compilerNames: typeof ORDERED_COMPILER_NAMES | null\n): Promise<\n  | Awaited<ReturnType<typeof webpackBuildWithWorker>>\n  | Awaited<ReturnType<typeof import('./impl').webpackBuildImpl>>\n> {\n  if (withWorker) {\n    debug('using separate compiler workers')\n    return await webpackBuildWithWorker(compilerNames)\n  } else {\n    debug('building all compilers in same process')\n    const webpackBuildImpl = (require('./impl') as typeof import('./impl'))\n      .webpackBuildImpl\n    const curResult = await webpackBuildImpl(null)\n\n    // Mirror what happens in webpackBuildWithWorker\n    if (curResult.telemetryState) {\n      NextBuildContext.telemetryState = {\n        ...curResult.telemetryState,\n        useCacheTracker: mergeUseCacheTrackers(\n          NextBuildContext.telemetryState?.useCacheTracker,\n          curResult.telemetryState.useCacheTracker\n        ),\n      }\n    }\n\n    return curResult\n  }\n}\n"], "names": ["webpackBuild", "debug", "origDebug", "ORDERED_COMPILER_NAMES", "pluginState", "deepMerge", "target", "source", "result", "key", "Object", "keys", "Array", "isArray", "webpackBuildWithWorker", "compilerNamesArg", "compilerNames", "nextBuildSpan", "prunedBuildContext", "NextBuildContext", "combinedResult", "duration", "buildTraceContext", "compilerName", "curR<PERSON>ult", "worker", "Worker", "path", "join", "__dirname", "exposedMethods", "debuggerPortOffset", "isolated<PERSON><PERSON><PERSON>", "numWorkers", "maxRetries", "forkOptions", "env", "NEXT_PRIVATE_BUILD_WORKER", "worker<PERSON>ain", "buildContext", "traceState", "exportTraceState", "defaultParentSpanId", "getId", "shouldSaveTraceEvents", "debugTraceEvents", "recordTraceEvents", "end", "telemetryState", "useCacheTracker", "mergeUseCacheTrackers", "entriesTrace", "entryNameMap", "chunksTrace", "entryNameFilesMap", "length", "durationString", "durationToString", "Log", "event", "with<PERSON><PERSON>ker", "webpackBuildImpl", "require"], "mappings": ";;;;+BA2HsBA;;;eAAAA;;;6DA1HD;8BACY;wBAEV;8DACD;6DACL;uBACmC;sCACd;kCACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEjC,MAAMC,QAAQC,IAAAA,cAAS,EAAC;AAExB,MAAMC,yBAAyB;IAC7B;IACA;IACA;CACD;AAED,IAAIC,cAAgC,CAAC;AAErC,SAASC,UAAUC,MAAW,EAAEC,MAAW;IACzC,MAAMC,SAAS;QAAE,GAAGF,MAAM;QAAE,GAAGC,MAAM;IAAC;IACtC,KAAK,MAAME,OAAOC,OAAOC,IAAI,CAACH,QAAS;QACrCA,MAAM,CAACC,IAAI,GAAGG,MAAMC,OAAO,CAACP,MAAM,CAACG,IAAI,IAClCH,MAAM,CAACG,IAAI,GAAG;eAAIH,MAAM,CAACG,IAAI;eAAMF,MAAM,CAACE,IAAI,IAAI,EAAE;SAAE,GACvD,OAAOH,MAAM,CAACG,IAAI,IAAI,YAAY,OAAOF,MAAM,CAACE,IAAI,IAAI,WACtDJ,UAAUC,MAAM,CAACG,IAAI,EAAEF,MAAM,CAACE,IAAI,IAClCD,MAAM,CAACC,IAAI;IACnB;IACA,OAAOD;AACT;AAEA,eAAeM,uBACbC,gBAAsD;IAEtD,MAAMC,gBAAgBD,oBAAoBZ;IAC1C,MAAM,EAAEc,aAAa,EAAE,GAAGC,oBAAoB,GAAGC,8BAAgB;IAEjED,mBAAmBd,WAAW,GAAGA;IAEjC,MAAMgB,iBAAiB;QACrBC,UAAU;QACVC,mBAAmB,CAAC;IACtB;IAEA,KAAK,MAAMC,gBAAgBP,cAAe;YA6CpCQ;QA5CJ,MAAMC,SAAS,IAAIC,cAAM,CAACC,aAAI,CAACC,IAAI,CAACC,WAAW,YAAY;YACzDC,gBAAgB;gBAAC;aAAa;YAC9BC,oBAAoB,CAAC;YACrBC,gBAAgB;YAChBC,YAAY;YACZC,YAAY;YACZC,aAAa;gBACXC,KAAK;oBACHC,2BAA2B;gBAC7B;YACF;QACF;QAEA,MAAMb,YAAY,MAAMC,OAAOa,UAAU,CAAC;YACxCC,cAAcrB;YACdK;YACAiB,YAAY;gBACV,GAAGC,IAAAA,uBAAgB,GAAE;gBACrBC,mBAAmB,EAAEzB,iCAAAA,cAAe0B,KAAK;gBACzCC,uBAAuB;YACzB;QACF;QACA,IAAI3B,iBAAiBO,UAAUqB,gBAAgB,EAAE;YAC/CC,IAAAA,wBAAiB,EAACtB,UAAUqB,gBAAgB;QAC9C;QACA,0DAA0D;QAC1D,MAAMpB,OAAOsB,GAAG;QAEhB,sBAAsB;QACtB3C,cAAcC,UAAUD,aAAaoB,UAAUpB,WAAW;QAC1Dc,mBAAmBd,WAAW,GAAGA;QAEjC,IAAIoB,UAAUwB,cAAc,EAAE;gBAIxB7B;YAHJA,8BAAgB,CAAC6B,cAAc,GAAG;gBAChC,GAAGxB,UAAUwB,cAAc;gBAC3BC,iBAAiBC,IAAAA,2CAAqB,GACpC/B,mCAAAA,8BAAgB,CAAC6B,cAAc,qBAA/B7B,iCAAiC8B,eAAe,EAChDzB,UAAUwB,cAAc,CAACC,eAAe;YAE5C;QACF;QAEA7B,eAAeC,QAAQ,IAAIG,UAAUH,QAAQ;QAE7C,KAAIG,+BAAAA,UAAUF,iBAAiB,qBAA3BE,6BAA6B2B,YAAY,EAAE;gBAUzC3B;YATJ,MAAM,EAAE4B,YAAY,EAAE,GAAG5B,UAAUF,iBAAiB,CAAC6B,YAAY;YAEjE,IAAIC,cAAc;gBAChBhC,eAAeE,iBAAiB,CAAC6B,YAAY,GAC3C3B,UAAUF,iBAAiB,CAAC6B,YAAY;gBAC1C/B,eAAeE,iBAAiB,CAAC6B,YAAY,CAAEC,YAAY,GACzDA;YACJ;YAEA,KAAI5B,gCAAAA,UAAUF,iBAAiB,qBAA3BE,8BAA6B6B,WAAW,EAAE;gBAC5C,MAAM,EAAEC,iBAAiB,EAAE,GAAG9B,UAAUF,iBAAiB,CAAC+B,WAAW;gBAErE,IAAIC,mBAAmB;oBACrBlC,eAAeE,iBAAiB,CAAC+B,WAAW,GAC1C7B,UAAUF,iBAAiB,CAAC+B,WAAW;oBAEzCjC,eAAeE,iBAAiB,CAAC+B,WAAW,CAAEC,iBAAiB,GAC7DA;gBACJ;YACF;QACF;IACF;IAEA,IAAItC,cAAcuC,MAAM,KAAK,GAAG;QAC9B,MAAMC,iBAAiBC,IAAAA,kCAAgB,EAACrC,eAAeC,QAAQ;QAC/DqC,KAAIC,KAAK,CAAC,CAAC,yBAAyB,EAAEH,gBAAgB;IACxD;IAEA,OAAOpC;AACT;AAEO,eAAepB,aACpB4D,UAAmB,EACnB5C,aAAmD;IAKnD,IAAI4C,YAAY;QACd3D,MAAM;QACN,OAAO,MAAMa,uBAAuBE;IACtC,OAAO;QACLf,MAAM;QACN,MAAM4D,mBAAmB,AAACC,QAAQ,UAC/BD,gBAAgB;QACnB,MAAMrC,YAAY,MAAMqC,iBAAiB;QAEzC,gDAAgD;QAChD,IAAIrC,UAAUwB,cAAc,EAAE;gBAIxB7B;YAHJA,8BAAgB,CAAC6B,cAAc,GAAG;gBAChC,GAAGxB,UAAUwB,cAAc;gBAC3BC,iBAAiBC,IAAAA,2CAAqB,GACpC/B,mCAAAA,8BAAgB,CAAC6B,cAAc,qBAA/B7B,iCAAiC8B,eAAe,EAChDzB,UAAUwB,cAAc,CAACC,eAAe;YAE5C;QACF;QAEA,OAAOzB;IACT;AACF", "ignoreList": [0]}