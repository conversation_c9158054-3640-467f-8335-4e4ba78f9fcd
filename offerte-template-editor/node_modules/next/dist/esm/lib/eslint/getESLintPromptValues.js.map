{"version": 3, "sources": ["../../../src/lib/eslint/getESLintPromptValues.ts"], "sourcesContent": ["import findUp from 'next/dist/compiled/find-up'\n\nexport const getESLintStrictValue = async (cwd: string) => {\n  const tsConfigLocation = await findUp('tsconfig.json', { cwd })\n  const hasTSConfig = tsConfigLocation !== undefined\n\n  return {\n    title: 'Strict',\n    recommended: true,\n    config: {\n      extends: hasTSConfig\n        ? ['next/core-web-vitals', 'next/typescript']\n        : 'next/core-web-vitals',\n    },\n  }\n}\n\nexport const getESLintPromptValues = async (cwd: string) => {\n  return [\n    await getESLintStrictValue(cwd),\n    {\n      title: 'Base',\n      config: {\n        extends: 'next',\n      },\n    },\n    {\n      title: 'Cancel',\n      config: null,\n    },\n  ]\n}\n"], "names": ["findUp", "getESLintStrictValue", "cwd", "tsConfigLocation", "hasTSConfig", "undefined", "title", "recommended", "config", "extends", "getESLintPromptValues"], "mappings": "AAAA,OAAOA,YAAY,6BAA4B;AAE/C,OAAO,MAAMC,uBAAuB,OAAOC;IACzC,MAAMC,mBAAmB,MAAMH,OAAO,iBAAiB;QAAEE;IAAI;IAC7D,MAAME,cAAcD,qBAAqBE;IAEzC,OAAO;QACLC,OAAO;QACPC,aAAa;QACbC,QAAQ;YACNC,SAASL,cACL;gBAAC;gBAAwB;aAAkB,GAC3C;QACN;IACF;AACF,EAAC;AAED,OAAO,MAAMM,wBAAwB,OAAOR;IAC1C,OAAO;QACL,MAAMD,qBAAqBC;QAC3B;YACEI,OAAO;YACPE,QAAQ;gBACNC,SAAS;YACX;QACF;QACA;YACEH,OAAO;YACPE,QAAQ;QACV;KACD;AACH,EAAC", "ignoreList": [0]}