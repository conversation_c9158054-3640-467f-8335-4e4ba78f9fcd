{"version": 3, "sources": ["../../../src/server/request/headers.ts"], "sourcesContent": ["import {\n  HeadersAdapter,\n  type ReadonlyHeaders,\n} from '../web/spec-extension/adapters/headers'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { getExpectedRequestStore } from '../app-render/work-unit-async-storage.external'\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport {\n  postponeWithTracking,\n  throwToInterruptStaticGeneration,\n  trackDynamicDataInDynamicRender,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { scheduleImmediate } from '../../lib/scheduler'\nimport { isRequestAPICallableInsideAfter } from './utils'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\n\n/**\n * In this version of Next.js `headers()` returns a Promise however you can still reference the properties of the underlying Headers instance\n * synchronously to facilitate migration. The `UnsafeUnwrappedHeaders` type is added to your code by a codemod that attempts to automatically\n * updates callsites to reflect the new Promise return type. There are some cases where `headers()` cannot be automatically converted, namely\n * when it is used inside a synchronous function and we can't be sure the function can be made async automatically. In these cases we add an\n * explicit type case to `UnsafeUnwrappedHeaders` to enable typescript to allow for the synchronous usage only where it is actually necessary.\n *\n * You should should update these callsites to either be async functions where the `headers()` value can be awaited or you should call `headers()`\n * from outside and await the return value before passing it into this function.\n *\n * You can find instances that require manual migration by searching for `UnsafeUnwrappedHeaders` in your codebase or by search for a comment that\n * starts with `@next-codemod-error`.\n *\n * In a future version of Next.js `headers()` will only return a Promise and you will not be able to access the underlying Headers instance\n * without awaiting the return value first. When this change happens the type `UnsafeUnwrappedHeaders` will be updated to reflect that is it no longer\n * usable.\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedHeaders = ReadonlyHeaders\n\n/**\n * This function allows you to read the HTTP incoming request headers in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers) and\n * [Middleware](https://nextjs.org/docs/app/building-your-application/routing/middleware).\n *\n * Read more: [Next.js Docs: `headers`](https://nextjs.org/docs/app/api-reference/functions/headers)\n */\nexport function headers(): Promise<ReadonlyHeaders> {\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workStore) {\n    if (\n      workUnitStore &&\n      workUnitStore.phase === 'after' &&\n      !isRequestAPICallableInsideAfter()\n    ) {\n      throw new Error(\n        `Route ${workStore.route} used \"headers\" inside \"after(...)\". This is not supported. If you need this data inside an \"after\" callback, use \"headers\" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`\n      )\n    }\n\n    if (workStore.forceStatic) {\n      // When using forceStatic we override all other logic and always just return an empty\n      // headers object without tracking\n      const underlyingHeaders = HeadersAdapter.seal(new Headers({}))\n      return makeUntrackedExoticHeaders(underlyingHeaders)\n    }\n\n    if (workUnitStore) {\n      if (workUnitStore.type === 'cache') {\n        throw new Error(\n          `Route ${workStore.route} used \"headers\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n        )\n      } else if (workUnitStore.type === 'unstable-cache') {\n        throw new Error(\n          `Route ${workStore.route} used \"headers\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n        )\n      }\n    }\n    if (workStore.dynamicShouldError) {\n      throw new StaticGenBailoutError(\n        `Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`headers\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore) {\n      switch (workUnitStore.type) {\n        case 'prerender':\n          return makeHangingHeaders(workUnitStore)\n        case 'prerender-client':\n          const exportName = '`headers`'\n          throw new InvariantError(\n            `${exportName} must not be used within a client component. Next.js should be preventing ${exportName} from being included in client components statically, but did not in this case.`\n          )\n        case 'prerender-ppr':\n          // PPR Prerender (no dynamicIO)\n          // We are prerendering with PPR. We need track dynamic access here eagerly\n          // to keep continuity with how headers has worked in PPR without dynamicIO.\n          // TODO consider switching the semantic to throw on property access instead\n          postponeWithTracking(\n            workStore.route,\n            'headers',\n            workUnitStore.dynamicTracking\n          )\n          break\n        case 'prerender-legacy':\n          // Legacy Prerender\n          // We are in a legacy static generation mode while prerendering\n          // We track dynamic access here so we don't need to wrap the headers in\n          // individual property access tracking.\n          throwToInterruptStaticGeneration('headers', workStore, workUnitStore)\n          break\n        default:\n        // fallthrough\n      }\n    }\n    // We fall through to the dynamic context below but we still track dynamic access\n    // because in dev we can still error for things like using headers inside a cache context\n    trackDynamicDataInDynamicRender(workStore, workUnitStore)\n  }\n\n  const requestStore = getExpectedRequestStore('headers')\n  if (process.env.NODE_ENV === 'development' && !workStore?.isPrefetchRequest) {\n    if (process.env.__NEXT_DYNAMIC_IO) {\n      return makeUntrackedHeadersWithDevWarnings(\n        requestStore.headers,\n        workStore?.route\n      )\n    }\n\n    return makeUntrackedExoticHeadersWithDevWarnings(\n      requestStore.headers,\n      workStore?.route\n    )\n  } else {\n    return makeUntrackedExoticHeaders(requestStore.headers)\n  }\n}\n\ninterface CacheLifetime {}\nconst CachedHeaders = new WeakMap<CacheLifetime, Promise<ReadonlyHeaders>>()\n\nfunction makeHangingHeaders(\n  prerenderStore: PrerenderStoreModern\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(prerenderStore)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = makeHangingPromise<ReadonlyHeaders>(\n    prerenderStore.renderSignal,\n    '`headers()`'\n  )\n  CachedHeaders.set(prerenderStore, promise)\n\n  return promise\n}\n\nfunction makeUntrackedExoticHeaders(\n  underlyingHeaders: ReadonlyHeaders\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(underlyingHeaders)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = Promise.resolve(underlyingHeaders)\n  CachedHeaders.set(underlyingHeaders, promise)\n\n  Object.defineProperties(promise, {\n    append: {\n      value: underlyingHeaders.append.bind(underlyingHeaders),\n    },\n    delete: {\n      value: underlyingHeaders.delete.bind(underlyingHeaders),\n    },\n    get: {\n      value: underlyingHeaders.get.bind(underlyingHeaders),\n    },\n    has: {\n      value: underlyingHeaders.has.bind(underlyingHeaders),\n    },\n    set: {\n      value: underlyingHeaders.set.bind(underlyingHeaders),\n    },\n    getSetCookie: {\n      value: underlyingHeaders.getSetCookie.bind(underlyingHeaders),\n    },\n    forEach: {\n      value: underlyingHeaders.forEach.bind(underlyingHeaders),\n    },\n    keys: {\n      value: underlyingHeaders.keys.bind(underlyingHeaders),\n    },\n    values: {\n      value: underlyingHeaders.values.bind(underlyingHeaders),\n    },\n    entries: {\n      value: underlyingHeaders.entries.bind(underlyingHeaders),\n    },\n    [Symbol.iterator]: {\n      value: underlyingHeaders[Symbol.iterator].bind(underlyingHeaders),\n    },\n  } satisfies HeadersExtensions)\n\n  return promise\n}\n\nfunction makeUntrackedExoticHeadersWithDevWarnings(\n  underlyingHeaders: ReadonlyHeaders,\n  route?: string\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(underlyingHeaders)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = new Promise<ReadonlyHeaders>((resolve) =>\n    scheduleImmediate(() => resolve(underlyingHeaders))\n  )\n\n  CachedHeaders.set(underlyingHeaders, promise)\n\n  Object.defineProperties(promise, {\n    append: {\n      value: function append() {\n        const expression = `\\`headers().append(${describeNameArg(arguments[0])}, ...)\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.append.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    delete: {\n      value: function _delete() {\n        const expression = `\\`headers().delete(${describeNameArg(arguments[0])})\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.delete.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    get: {\n      value: function get() {\n        const expression = `\\`headers().get(${describeNameArg(arguments[0])})\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.get.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    has: {\n      value: function has() {\n        const expression = `\\`headers().has(${describeNameArg(arguments[0])})\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.has.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    set: {\n      value: function set() {\n        const expression = `\\`headers().set(${describeNameArg(arguments[0])}, ...)\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.set.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    getSetCookie: {\n      value: function getSetCookie() {\n        const expression = '`headers().getSetCookie()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.getSetCookie.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    forEach: {\n      value: function forEach() {\n        const expression = '`headers().forEach(...)`'\n        syncIODev(route, expression)\n        return underlyingHeaders.forEach.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    keys: {\n      value: function keys() {\n        const expression = '`headers().keys()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.keys.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    values: {\n      value: function values() {\n        const expression = '`headers().values()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.values.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    entries: {\n      value: function entries() {\n        const expression = '`headers().entries()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.entries.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    [Symbol.iterator]: {\n      value: function () {\n        const expression = '`...headers()` or similar iteration'\n        syncIODev(route, expression)\n        return underlyingHeaders[Symbol.iterator].apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n  } satisfies HeadersExtensions)\n\n  return promise\n}\n\n// Similar to `makeUntrackedExoticHeadersWithDevWarnings`, but just logging the\n// sync access without actually defining the headers properties on the promise.\nfunction makeUntrackedHeadersWithDevWarnings(\n  underlyingHeaders: ReadonlyHeaders,\n  route?: string\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(underlyingHeaders)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = new Promise<ReadonlyHeaders>((resolve) =>\n    scheduleImmediate(() => resolve(underlyingHeaders))\n  )\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      switch (prop) {\n        case Symbol.iterator: {\n          warnForSyncAccess(route, '`...headers()` or similar iteration')\n          break\n        }\n        case 'append':\n        case 'delete':\n        case 'get':\n        case 'has':\n        case 'set':\n        case 'getSetCookie':\n        case 'forEach':\n        case 'keys':\n        case 'values':\n        case 'entries': {\n          warnForSyncAccess(route, `\\`headers().${prop}\\``)\n          break\n        }\n        default: {\n          // We only warn for well-defined properties of the headers object.\n        }\n      }\n\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n  })\n\n  CachedHeaders.set(underlyingHeaders, proxiedPromise)\n\n  return proxiedPromise\n}\n\nfunction describeNameArg(arg: unknown) {\n  return typeof arg === 'string' ? `'${arg}'` : '...'\n}\n\nfunction syncIODev(route: string | undefined, expression: string) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (\n    workUnitStore &&\n    workUnitStore.type === 'request' &&\n    workUnitStore.prerenderPhase === true\n  ) {\n    // When we're rendering dynamically in dev we need to advance out of the\n    // Prerender environment when we read Request data synchronously\n    const requestStore = workUnitStore\n    trackSynchronousRequestDataAccessInDev(requestStore)\n  }\n  // In all cases we warn normally\n  warnForSyncAccess(route, expression)\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createHeadersAccessError\n)\n\nfunction createHeadersAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`headers()\\` should be awaited before using its value. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\ntype HeadersExtensions = {\n  [K in keyof ReadonlyHeaders]: unknown\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "workAsyncStorage", "getExpectedRequestStore", "workUnitAsyncStorage", "postponeWithTracking", "throwToInterruptStaticGeneration", "trackDynamicDataInDynamicRender", "trackSynchronousRequestDataAccessInDev", "StaticGenBailoutError", "makeHangingPromise", "createDedupedByCallsiteServerErrorLoggerDev", "scheduleImmediate", "isRequestAPICallableInsideAfter", "InvariantError", "ReflectAdapter", "headers", "workStore", "getStore", "workUnitStore", "phase", "Error", "route", "forceStatic", "underlyingHeaders", "seal", "Headers", "makeUntrackedExoticHeaders", "type", "dynamicShouldError", "makeHangingHeaders", "exportName", "dynamicTracking", "requestStore", "process", "env", "NODE_ENV", "isPrefetchRequest", "__NEXT_DYNAMIC_IO", "makeUntrackedHeadersWithDevWarnings", "makeUntrackedExoticHeadersWithDevWarnings", "CachedHeaders", "WeakMap", "prerenderStore", "cachedHeaders", "get", "promise", "renderSignal", "set", "Promise", "resolve", "Object", "defineProperties", "append", "value", "bind", "delete", "has", "getSetCookie", "for<PERSON>ach", "keys", "values", "entries", "Symbol", "iterator", "expression", "describeNameArg", "arguments", "syncIODev", "apply", "_delete", "proxiedPromise", "Proxy", "target", "prop", "receiver", "warnForSyncAccess", "arg", "prerenderPhase", "createHeadersAccessError", "prefix"], "mappings": "AAAA,SACEA,cAAc,QAET,yCAAwC;AAC/C,SAASC,gBAAgB,QAAQ,4CAA2C;AAC5E,SAASC,uBAAuB,QAAQ,iDAAgD;AACxF,SACEC,oBAAoB,QAEf,iDAAgD;AACvD,SACEC,oBAAoB,EACpBC,gCAAgC,EAChCC,+BAA+B,EAC/BC,sCAAsC,QACjC,kCAAiC;AACxC,SAASC,qBAAqB,QAAQ,oDAAmD;AACzF,SAASC,kBAAkB,QAAQ,6BAA4B;AAC/D,SAASC,2CAA2C,QAAQ,oDAAmD;AAC/G,SAASC,iBAAiB,QAAQ,sBAAqB;AACvD,SAASC,+BAA+B,QAAQ,UAAS;AACzD,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SAASC,cAAc,QAAQ,yCAAwC;AAyBvE;;;;;;;;CAQC,GACD,OAAO,SAASC;IACd,MAAMC,YAAYf,iBAAiBgB,QAAQ;IAC3C,MAAMC,gBAAgBf,qBAAqBc,QAAQ;IAEnD,IAAID,WAAW;QACb,IACEE,iBACAA,cAAcC,KAAK,KAAK,WACxB,CAACP,mCACD;YACA,MAAM,qBAEL,CAFK,IAAIQ,MACR,CAAC,MAAM,EAAEJ,UAAUK,KAAK,CAAC,yOAAyO,CAAC,GAD/P,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIL,UAAUM,WAAW,EAAE;YACzB,qFAAqF;YACrF,kCAAkC;YAClC,MAAMC,oBAAoBvB,eAAewB,IAAI,CAAC,IAAIC,QAAQ,CAAC;YAC3D,OAAOC,2BAA2BH;QACpC;QAEA,IAAIL,eAAe;YACjB,IAAIA,cAAcS,IAAI,KAAK,SAAS;gBAClC,MAAM,qBAEL,CAFK,IAAIP,MACR,CAAC,MAAM,EAAEJ,UAAUK,KAAK,CAAC,0UAA0U,CAAC,GADhW,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,OAAO,IAAIH,cAAcS,IAAI,KAAK,kBAAkB;gBAClD,MAAM,qBAEL,CAFK,IAAIP,MACR,CAAC,MAAM,EAAEJ,UAAUK,KAAK,CAAC,mXAAmX,CAAC,GADzY,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QACA,IAAIL,UAAUY,kBAAkB,EAAE;YAChC,MAAM,qBAEL,CAFK,IAAIpB,sBACR,CAAC,MAAM,EAAEQ,UAAUK,KAAK,CAAC,iNAAiN,CAAC,GADvO,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIH,eAAe;YACjB,OAAQA,cAAcS,IAAI;gBACxB,KAAK;oBACH,OAAOE,mBAAmBX;gBAC5B,KAAK;oBACH,MAAMY,aAAa;oBACnB,MAAM,qBAEL,CAFK,IAAIjB,eACR,GAAGiB,WAAW,0EAA0E,EAAEA,WAAW,+EAA+E,CAAC,GADjL,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,KAAK;oBACH,+BAA+B;oBAC/B,0EAA0E;oBAC1E,2EAA2E;oBAC3E,2EAA2E;oBAC3E1B,qBACEY,UAAUK,KAAK,EACf,WACAH,cAAca,eAAe;oBAE/B;gBACF,KAAK;oBACH,mBAAmB;oBACnB,+DAA+D;oBAC/D,uEAAuE;oBACvE,uCAAuC;oBACvC1B,iCAAiC,WAAWW,WAAWE;oBACvD;gBACF;YAEF;QACF;QACA,iFAAiF;QACjF,yFAAyF;QACzFZ,gCAAgCU,WAAWE;IAC7C;IAEA,MAAMc,eAAe9B,wBAAwB;IAC7C,IAAI+B,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiB,EAACnB,6BAAAA,UAAWoB,iBAAiB,GAAE;QAC3E,IAAIH,QAAQC,GAAG,CAACG,iBAAiB,EAAE;YACjC,OAAOC,oCACLN,aAAajB,OAAO,EACpBC,6BAAAA,UAAWK,KAAK;QAEpB;QAEA,OAAOkB,0CACLP,aAAajB,OAAO,EACpBC,6BAAAA,UAAWK,KAAK;IAEpB,OAAO;QACL,OAAOK,2BAA2BM,aAAajB,OAAO;IACxD;AACF;AAGA,MAAMyB,gBAAgB,IAAIC;AAE1B,SAASZ,mBACPa,cAAoC;IAEpC,MAAMC,gBAAgBH,cAAcI,GAAG,CAACF;IACxC,IAAIC,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAUpC,mBACdiC,eAAeI,YAAY,EAC3B;IAEFN,cAAcO,GAAG,CAACL,gBAAgBG;IAElC,OAAOA;AACT;AAEA,SAASnB,2BACPH,iBAAkC;IAElC,MAAMoB,gBAAgBH,cAAcI,GAAG,CAACrB;IACxC,IAAIoB,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAUG,QAAQC,OAAO,CAAC1B;IAChCiB,cAAcO,GAAG,CAACxB,mBAAmBsB;IAErCK,OAAOC,gBAAgB,CAACN,SAAS;QAC/BO,QAAQ;YACNC,OAAO9B,kBAAkB6B,MAAM,CAACE,IAAI,CAAC/B;QACvC;QACAgC,QAAQ;YACNF,OAAO9B,kBAAkBgC,MAAM,CAACD,IAAI,CAAC/B;QACvC;QACAqB,KAAK;YACHS,OAAO9B,kBAAkBqB,GAAG,CAACU,IAAI,CAAC/B;QACpC;QACAiC,KAAK;YACHH,OAAO9B,kBAAkBiC,GAAG,CAACF,IAAI,CAAC/B;QACpC;QACAwB,KAAK;YACHM,OAAO9B,kBAAkBwB,GAAG,CAACO,IAAI,CAAC/B;QACpC;QACAkC,cAAc;YACZJ,OAAO9B,kBAAkBkC,YAAY,CAACH,IAAI,CAAC/B;QAC7C;QACAmC,SAAS;YACPL,OAAO9B,kBAAkBmC,OAAO,CAACJ,IAAI,CAAC/B;QACxC;QACAoC,MAAM;YACJN,OAAO9B,kBAAkBoC,IAAI,CAACL,IAAI,CAAC/B;QACrC;QACAqC,QAAQ;YACNP,OAAO9B,kBAAkBqC,MAAM,CAACN,IAAI,CAAC/B;QACvC;QACAsC,SAAS;YACPR,OAAO9B,kBAAkBsC,OAAO,CAACP,IAAI,CAAC/B;QACxC;QACA,CAACuC,OAAOC,QAAQ,CAAC,EAAE;YACjBV,OAAO9B,iBAAiB,CAACuC,OAAOC,QAAQ,CAAC,CAACT,IAAI,CAAC/B;QACjD;IACF;IAEA,OAAOsB;AACT;AAEA,SAASN,0CACPhB,iBAAkC,EAClCF,KAAc;IAEd,MAAMsB,gBAAgBH,cAAcI,GAAG,CAACrB;IACxC,IAAIoB,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAU,IAAIG,QAAyB,CAACC,UAC5CtC,kBAAkB,IAAMsC,QAAQ1B;IAGlCiB,cAAcO,GAAG,CAACxB,mBAAmBsB;IAErCK,OAAOC,gBAAgB,CAACN,SAAS;QAC/BO,QAAQ;YACNC,OAAO,SAASD;gBACd,MAAMY,aAAa,CAAC,mBAAmB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAChFC,UAAU9C,OAAO2C;gBACjB,OAAOzC,kBAAkB6B,MAAM,CAACgB,KAAK,CACnC7C,mBACA2C;YAEJ;QACF;QACAX,QAAQ;YACNF,OAAO,SAASgB;gBACd,MAAML,aAAa,CAAC,mBAAmB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBAC3EC,UAAU9C,OAAO2C;gBACjB,OAAOzC,kBAAkBgC,MAAM,CAACa,KAAK,CACnC7C,mBACA2C;YAEJ;QACF;QACAtB,KAAK;YACHS,OAAO,SAAST;gBACd,MAAMoB,aAAa,CAAC,gBAAgB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACxEC,UAAU9C,OAAO2C;gBACjB,OAAOzC,kBAAkBqB,GAAG,CAACwB,KAAK,CAAC7C,mBAAmB2C;YACxD;QACF;QACAV,KAAK;YACHH,OAAO,SAASG;gBACd,MAAMQ,aAAa,CAAC,gBAAgB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACxEC,UAAU9C,OAAO2C;gBACjB,OAAOzC,kBAAkBiC,GAAG,CAACY,KAAK,CAAC7C,mBAAmB2C;YACxD;QACF;QACAnB,KAAK;YACHM,OAAO,SAASN;gBACd,MAAMiB,aAAa,CAAC,gBAAgB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAC7EC,UAAU9C,OAAO2C;gBACjB,OAAOzC,kBAAkBwB,GAAG,CAACqB,KAAK,CAAC7C,mBAAmB2C;YACxD;QACF;QACAT,cAAc;YACZJ,OAAO,SAASI;gBACd,MAAMO,aAAa;gBACnBG,UAAU9C,OAAO2C;gBACjB,OAAOzC,kBAAkBkC,YAAY,CAACW,KAAK,CACzC7C,mBACA2C;YAEJ;QACF;QACAR,SAAS;YACPL,OAAO,SAASK;gBACd,MAAMM,aAAa;gBACnBG,UAAU9C,OAAO2C;gBACjB,OAAOzC,kBAAkBmC,OAAO,CAACU,KAAK,CACpC7C,mBACA2C;YAEJ;QACF;QACAP,MAAM;YACJN,OAAO,SAASM;gBACd,MAAMK,aAAa;gBACnBG,UAAU9C,OAAO2C;gBACjB,OAAOzC,kBAAkBoC,IAAI,CAACS,KAAK,CAAC7C,mBAAmB2C;YACzD;QACF;QACAN,QAAQ;YACNP,OAAO,SAASO;gBACd,MAAMI,aAAa;gBACnBG,UAAU9C,OAAO2C;gBACjB,OAAOzC,kBAAkBqC,MAAM,CAACQ,KAAK,CACnC7C,mBACA2C;YAEJ;QACF;QACAL,SAAS;YACPR,OAAO,SAASQ;gBACd,MAAMG,aAAa;gBACnBG,UAAU9C,OAAO2C;gBACjB,OAAOzC,kBAAkBsC,OAAO,CAACO,KAAK,CACpC7C,mBACA2C;YAEJ;QACF;QACA,CAACJ,OAAOC,QAAQ,CAAC,EAAE;YACjBV,OAAO;gBACL,MAAMW,aAAa;gBACnBG,UAAU9C,OAAO2C;gBACjB,OAAOzC,iBAAiB,CAACuC,OAAOC,QAAQ,CAAC,CAACK,KAAK,CAC7C7C,mBACA2C;YAEJ;QACF;IACF;IAEA,OAAOrB;AACT;AAEA,+EAA+E;AAC/E,+EAA+E;AAC/E,SAASP,oCACPf,iBAAkC,EAClCF,KAAc;IAEd,MAAMsB,gBAAgBH,cAAcI,GAAG,CAACrB;IACxC,IAAIoB,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAU,IAAIG,QAAyB,CAACC,UAC5CtC,kBAAkB,IAAMsC,QAAQ1B;IAGlC,MAAM+C,iBAAiB,IAAIC,MAAM1B,SAAS;QACxCD,KAAI4B,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,OAAQD;gBACN,KAAKX,OAAOC,QAAQ;oBAAE;wBACpBY,kBAAkBtD,OAAO;wBACzB;oBACF;gBACA,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBAAW;wBACdsD,kBAAkBtD,OAAO,CAAC,YAAY,EAAEoD,KAAK,EAAE,CAAC;wBAChD;oBACF;gBACA;oBAAS;oBACP,kEAAkE;oBACpE;YACF;YAEA,OAAO3D,eAAe8B,GAAG,CAAC4B,QAAQC,MAAMC;QAC1C;IACF;IAEAlC,cAAcO,GAAG,CAACxB,mBAAmB+C;IAErC,OAAOA;AACT;AAEA,SAASL,gBAAgBW,GAAY;IACnC,OAAO,OAAOA,QAAQ,WAAW,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,GAAG;AAChD;AAEA,SAAST,UAAU9C,KAAyB,EAAE2C,UAAkB;IAC9D,MAAM9C,gBAAgBf,qBAAqBc,QAAQ;IACnD,IACEC,iBACAA,cAAcS,IAAI,KAAK,aACvBT,cAAc2D,cAAc,KAAK,MACjC;QACA,wEAAwE;QACxE,gEAAgE;QAChE,MAAM7C,eAAed;QACrBX,uCAAuCyB;IACzC;IACA,gCAAgC;IAChC2C,kBAAkBtD,OAAO2C;AAC3B;AAEA,MAAMW,oBAAoBjE,4CACxBoE;AAGF,SAASA,yBACPzD,KAAyB,EACzB2C,UAAkB;IAElB,MAAMe,SAAS1D,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,qBAIN,CAJM,IAAID,MACT,GAAG2D,OAAO,KAAK,EAAEf,WAAW,EAAE,CAAC,GAC7B,CAAC,wDAAwD,CAAC,GAC1D,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF", "ignoreList": [0]}