{"version": 3, "sources": ["../../../../src/client/dev/hot-reloader/get-socket-url.ts"], "sourcesContent": ["import { normalizedAssetPrefix } from '../../../shared/lib/normalized-asset-prefix'\n\nfunction getSocketProtocol(assetPrefix: string): string {\n  let protocol = window.location.protocol\n\n  try {\n    // assetPrefix is a url\n    protocol = new URL(assetPrefix).protocol\n  } catch {}\n\n  return protocol === 'http:' ? 'ws:' : 'wss:'\n}\n\nexport function getSocketUrl(assetPrefix: string | undefined): string {\n  const prefix = normalizedAssetPrefix(assetPrefix)\n  const protocol = getSocketProtocol(assetPrefix || '')\n\n  if (URL.canParse(prefix)) {\n    // since normalized asset prefix is ensured to be a URL format,\n    // we can safely replace the protocol\n    return prefix.replace(/^http/, 'ws')\n  }\n\n  const { hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? `:${port}` : ''}${prefix}`\n}\n"], "names": ["normalizedAssetPrefix", "getSocketProtocol", "assetPrefix", "protocol", "window", "location", "URL", "getSocketUrl", "prefix", "canParse", "replace", "hostname", "port"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,8CAA6C;AAEnF,SAASC,kBAAkBC,WAAmB;IAC5C,IAAIC,WAAWC,OAAOC,QAAQ,CAACF,QAAQ;IAEvC,IAAI;QACF,uBAAuB;QACvBA,WAAW,IAAIG,IAAIJ,aAAaC,QAAQ;IAC1C,EAAE,UAAM,CAAC;IAET,OAAOA,aAAa,UAAU,QAAQ;AACxC;AAEA,OAAO,SAASI,aAAaL,WAA+B;IAC1D,MAAMM,SAASR,sBAAsBE;IACrC,MAAMC,WAAWF,kBAAkBC,eAAe;IAElD,IAAII,IAAIG,QAAQ,CAACD,SAAS;QACxB,+DAA+D;QAC/D,qCAAqC;QACrC,OAAOA,OAAOE,OAAO,CAAC,SAAS;IACjC;IAEA,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAE,GAAGR,OAAOC,QAAQ;IAC1C,OAAO,AAAGF,WAAS,OAAIQ,WAAWC,CAAAA,OAAO,AAAC,MAAGA,OAAS,EAAC,IAAIJ;AAC7D", "ignoreList": [0]}