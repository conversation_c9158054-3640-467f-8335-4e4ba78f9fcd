{"version": 3, "sources": ["../../../../src/next-devtools/userspace/pages/hydration-error-state.ts"], "sourcesContent": ["import {\n  getHydrationWarningType,\n  isHydrationError as isReact18HydrationError,\n  isHydrationWarning as isReact18HydrationWarning,\n} from '../../shared/react-18-hydration-error'\nimport {\n  isHydrationError as isReact19HydrationError,\n  isErrorMessageWithComponentStackDiff as isReact19HydrationWarning,\n} from '../../shared/react-19-hydration-error'\nimport type { HydrationErrorState } from '../../shared/hydration-error'\n\n// We only need this for React 18 or hydration console errors in React 19.\n// Once we surface console.error in the dev overlay in pages router, we should only\n// use this for React 18.\nlet hydrationErrorState: HydrationErrorState = {}\n\nconst squashedHydrationErrorDetails = new WeakMap<Error, HydrationErrorState>()\n\nexport function getSquashedHydrationErrorDetails(\n  error: Error\n): HydrationErrorState | null {\n  return squashedHydrationErrorDetails.has(error)\n    ? squashedHydrationErrorDetails.get(error)!\n    : null\n}\n\nexport function attachHydrationErrorState(error: Error) {\n  if (!isReact18HydrationError(error) && !isReact19HydrationError(error)) {\n    return\n  }\n\n  let parsedHydrationErrorState: typeof hydrationErrorState = {}\n\n  // If there's any extra information in the error message to display,\n  // append it to the error message details property\n  if (hydrationErrorState.warning) {\n    // The patched console.error found hydration errors logged by React\n    // Append the logged warning to the error message\n    parsedHydrationErrorState = {\n      // It contains the warning, component stack, server and client tag names\n      ...hydrationErrorState,\n    }\n\n    // Consume the cached hydration diff.\n    // This is only required for now when we still squashed the hydration diff log into hydration error.\n    // Once the all error is logged to dev overlay in order, this will go away.\n    if (hydrationErrorState.reactOutputComponentDiff) {\n      parsedHydrationErrorState.reactOutputComponentDiff =\n        hydrationErrorState.reactOutputComponentDiff\n    }\n\n    squashedHydrationErrorDetails.set(error, parsedHydrationErrorState)\n  }\n}\n\n// TODO: Only handle React 18. Once we surface console.error in the dev overlay in pages router,\n// we can use the same behavior as App Router.\nexport function storeHydrationErrorStateFromConsoleArgs(...args: any[]) {\n  let [message, firstContent, secondContent, ...rest] = args\n  if (isReact18HydrationWarning(message)) {\n    // Some hydration warnings has 4 arguments, some has 3, fallback to the last argument\n    // when the 3rd argument is not the component stack but an empty string\n    // For some warnings, there's only 1 argument for template.\n    // The second argument is the diff or component stack.\n    if (args.length === 3) {\n      secondContent = ''\n    }\n\n    const warning = message\n      .replace(/Warning: /, '')\n      .replace('%s', firstContent)\n      .replace('%s', secondContent)\n      // remove the last %s from the message\n      .replace(/%s/g, '')\n\n    const lastArg = (rest[rest.length - 1] || '').trim()\n\n    hydrationErrorState.reactOutputComponentDiff = generateHydrationDiffReact18(\n      message,\n      firstContent,\n      secondContent,\n      lastArg\n    )\n\n    hydrationErrorState.warning = warning\n  } else if (isReact19HydrationWarning(message)) {\n    // Some hydration warnings has 4 arguments, some has 3, fallback to the last argument\n    // when the 3rd argument is not the component stack but an empty string\n    // For some warnings, there's only 1 argument for template.\n    // The second argument is the diff or component stack.\n    if (args.length === 3) {\n      secondContent = ''\n    }\n\n    const warning = message\n      .replace('%s', firstContent)\n      .replace('%s', secondContent)\n      // remove the last %s from the message\n      .replace(/%s/g, '')\n\n    const lastArg = (args[args.length - 1] || '').trim()\n\n    hydrationErrorState.reactOutputComponentDiff = lastArg\n    hydrationErrorState.warning = warning\n  }\n}\n\n/*\n * Some hydration errors in React 18 does not have the diff in the error message.\n * Instead it has the error stack trace which is component stack that we can leverage.\n * Will parse the diff from the error stack trace\n *  e.g.\n *  Warning: Expected server HTML to contain a matching <div> in <p>.\n *    at div\n *    at p\n *    at div\n *    at div\n *    at Page\n *  output:\n *    <Page>\n *      <div>\n *        <p>\n *  >       <div>\n *\n */\nfunction generateHydrationDiffReact18(\n  message: string,\n  firstContent: string,\n  secondContent: string,\n  lastArg: string\n) {\n  const componentStack = lastArg\n  let firstIndex = -1\n  let secondIndex = -1\n  const hydrationWarningType = getHydrationWarningType(message)\n\n  // at div\\n at Foo\\n at Bar (....)\\n -> [div, Foo]\n  const components = componentStack\n    .split('\\n')\n    // .reverse()\n    .map((line: string, index: number) => {\n      // `<space>at <component> (<location>)` -> `at <component> (<location>)`\n      line = line.trim()\n      // extract `<space>at <component>` to `<<component>>`\n      // e.g. `  at Foo` -> `<Foo>`\n      const [, component, location] = /at (\\w+)( \\((.*)\\))?/.exec(line) || []\n      // If there's no location then it's user-land stack frame\n      if (!location) {\n        if (component === firstContent && firstIndex === -1) {\n          firstIndex = index\n        } else if (component === secondContent && secondIndex === -1) {\n          secondIndex = index\n        }\n      }\n      return location ? '' : component\n    })\n    .filter(Boolean)\n    .reverse()\n\n  let diff = ''\n  for (let i = 0; i < components.length; i++) {\n    const component = components[i]\n    const matchFirstContent =\n      hydrationWarningType === 'tag' && i === components.length - firstIndex - 1\n    const matchSecondContent =\n      hydrationWarningType === 'tag' &&\n      i === components.length - secondIndex - 1\n    if (matchFirstContent || matchSecondContent) {\n      const spaces = ' '.repeat(Math.max(i * 2 - 2, 0) + 2)\n      diff += `> ${spaces}<${component}>\\n`\n    } else {\n      const spaces = ' '.repeat(i * 2 + 2)\n      diff += `${spaces}<${component}>\\n`\n    }\n  }\n  if (hydrationWarningType === 'text') {\n    const spaces = ' '.repeat(components.length * 2)\n    diff += `+ ${spaces}\"${firstContent}\"\\n`\n    diff += `- ${spaces}\"${secondContent}\"\\n`\n  } else if (hydrationWarningType === 'text-in-tag') {\n    const spaces = ' '.repeat(components.length * 2)\n    diff += `> ${spaces}<${secondContent}>\\n`\n    diff += `>   ${spaces}\"${firstContent}\"\\n`\n  }\n  return diff\n}\n"], "names": ["getHydrationWarningType", "isHydrationError", "isReact18HydrationError", "isHydrationWarning", "isReact18HydrationWarning", "isReact19HydrationError", "isErrorMessageWithComponentStackDiff", "isReact19HydrationWarning", "hydrationErrorState", "squashedHydrationErrorDetails", "WeakMap", "getSquashedHydrationErrorDetails", "error", "has", "get", "attachHydrationErrorState", "parsedHydrationErrorState", "warning", "reactOutputComponentDiff", "set", "storeHydrationErrorStateFromConsoleArgs", "args", "message", "firstContent", "second<PERSON><PERSON>nt", "rest", "length", "replace", "lastArg", "trim", "generateHydrationDiffReact18", "componentStack", "firstIndex", "secondIndex", "hydrationWarningType", "components", "split", "map", "line", "index", "component", "location", "exec", "filter", "Boolean", "reverse", "diff", "i", "match<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "matchSecondContent", "spaces", "repeat", "Math", "max"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,oBAAoBC,uBAAuB,EAC3CC,sBAAsBC,yBAAyB,QAC1C,wCAAuC;AAC9C,SACEH,oBAAoBI,uBAAuB,EAC3CC,wCAAwCC,yBAAyB,QAC5D,wCAAuC;AAG9C,0EAA0E;AAC1E,mFAAmF;AACnF,yBAAyB;AACzB,IAAIC,sBAA2C,CAAC;AAEhD,MAAMC,gCAAgC,IAAIC;AAE1C,OAAO,SAASC,iCACdC,KAAY;IAEZ,OAAOH,8BAA8BI,GAAG,CAACD,SACrCH,8BAA8BK,GAAG,CAACF,SAClC;AACN;AAEA,OAAO,SAASG,0BAA0BH,KAAY;IACpD,IAAI,CAACV,wBAAwBU,UAAU,CAACP,wBAAwBO,QAAQ;QACtE;IACF;IAEA,IAAII,4BAAwD,CAAC;IAE7D,oEAAoE;IACpE,kDAAkD;IAClD,IAAIR,oBAAoBS,OAAO,EAAE;QAC/B,mEAAmE;QACnE,iDAAiD;QACjDD,4BAA4B;YAC1B,wEAAwE;YACxE,GAAGR,mBAAmB;QACxB;QAEA,qCAAqC;QACrC,oGAAoG;QACpG,2EAA2E;QAC3E,IAAIA,oBAAoBU,wBAAwB,EAAE;YAChDF,0BAA0BE,wBAAwB,GAChDV,oBAAoBU,wBAAwB;QAChD;QAEAT,8BAA8BU,GAAG,CAACP,OAAOI;IAC3C;AACF;AAEA,gGAAgG;AAChG,8CAA8C;AAC9C,OAAO,SAASI;IAAwC,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGC,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,KAAH,QAAA,SAAA,CAAA,KAAc;;IACpE,IAAI,CAACC,SAASC,cAAcC,eAAe,GAAGC,KAAK,GAAGJ;IACtD,IAAIjB,0BAA0BkB,UAAU;QACtC,qFAAqF;QACrF,uEAAuE;QACvE,2DAA2D;QAC3D,sDAAsD;QACtD,IAAID,KAAKK,MAAM,KAAK,GAAG;YACrBF,gBAAgB;QAClB;QAEA,MAAMP,UAAUK,QACbK,OAAO,CAAC,aAAa,IACrBA,OAAO,CAAC,MAAMJ,cACdI,OAAO,CAAC,MAAMH,cACf,sCAAsC;SACrCG,OAAO,CAAC,OAAO;QAElB,MAAMC,UAAU,AAACH,CAAAA,IAAI,CAACA,KAAKC,MAAM,GAAG,EAAE,IAAI,EAAC,EAAGG,IAAI;QAElDrB,oBAAoBU,wBAAwB,GAAGY,6BAC7CR,SACAC,cACAC,eACAI;QAGFpB,oBAAoBS,OAAO,GAAGA;IAChC,OAAO,IAAIV,0BAA0Be,UAAU;QAC7C,qFAAqF;QACrF,uEAAuE;QACvE,2DAA2D;QAC3D,sDAAsD;QACtD,IAAID,KAAKK,MAAM,KAAK,GAAG;YACrBF,gBAAgB;QAClB;QAEA,MAAMP,UAAUK,QACbK,OAAO,CAAC,MAAMJ,cACdI,OAAO,CAAC,MAAMH,cACf,sCAAsC;SACrCG,OAAO,CAAC,OAAO;QAElB,MAAMC,UAAU,AAACP,CAAAA,IAAI,CAACA,KAAKK,MAAM,GAAG,EAAE,IAAI,EAAC,EAAGG,IAAI;QAElDrB,oBAAoBU,wBAAwB,GAAGU;QAC/CpB,oBAAoBS,OAAO,GAAGA;IAChC;AACF;AAEA;;;;;;;;;;;;;;;;;CAiBC,GACD,SAASa,6BACPR,OAAe,EACfC,YAAoB,EACpBC,aAAqB,EACrBI,OAAe;IAEf,MAAMG,iBAAiBH;IACvB,IAAII,aAAa,CAAC;IAClB,IAAIC,cAAc,CAAC;IACnB,MAAMC,uBAAuBlC,wBAAwBsB;IAErD,kDAAkD;IAClD,MAAMa,aAAaJ,eAChBK,KAAK,CAAC,KACP,aAAa;KACZC,GAAG,CAAC,CAACC,MAAcC;QAClB,wEAAwE;QACxED,OAAOA,KAAKT,IAAI;QAChB,qDAAqD;QACrD,6BAA6B;QAC7B,MAAM,GAAGW,WAAWC,SAAS,GAAG,uBAAuBC,IAAI,CAACJ,SAAS,EAAE;QACvE,yDAAyD;QACzD,IAAI,CAACG,UAAU;YACb,IAAID,cAAcjB,gBAAgBS,eAAe,CAAC,GAAG;gBACnDA,aAAaO;YACf,OAAO,IAAIC,cAAchB,iBAAiBS,gBAAgB,CAAC,GAAG;gBAC5DA,cAAcM;YAChB;QACF;QACA,OAAOE,WAAW,KAAKD;IACzB,GACCG,MAAM,CAACC,SACPC,OAAO;IAEV,IAAIC,OAAO;IACX,IAAK,IAAIC,IAAI,GAAGA,IAAIZ,WAAWT,MAAM,EAAEqB,IAAK;QAC1C,MAAMP,YAAYL,UAAU,CAACY,EAAE;QAC/B,MAAMC,oBACJd,yBAAyB,SAASa,MAAMZ,WAAWT,MAAM,GAAGM,aAAa;QAC3E,MAAMiB,qBACJf,yBAAyB,SACzBa,MAAMZ,WAAWT,MAAM,GAAGO,cAAc;QAC1C,IAAIe,qBAAqBC,oBAAoB;YAC3C,MAAMC,SAAS,IAAIC,MAAM,CAACC,KAAKC,GAAG,CAACN,IAAI,IAAI,GAAG,KAAK;YACnDD,QAAQ,AAAC,OAAII,SAAO,MAAGV,YAAU;QACnC,OAAO;YACL,MAAMU,SAAS,IAAIC,MAAM,CAACJ,IAAI,IAAI;YAClCD,QAAQ,AAAGI,SAAO,MAAGV,YAAU;QACjC;IACF;IACA,IAAIN,yBAAyB,QAAQ;QACnC,MAAMgB,SAAS,IAAIC,MAAM,CAAChB,WAAWT,MAAM,GAAG;QAC9CoB,QAAQ,AAAC,OAAII,SAAO,MAAG3B,eAAa;QACpCuB,QAAQ,AAAC,OAAII,SAAO,MAAG1B,gBAAc;IACvC,OAAO,IAAIU,yBAAyB,eAAe;QACjD,MAAMgB,SAAS,IAAIC,MAAM,CAAChB,WAAWT,MAAM,GAAG;QAC9CoB,QAAQ,AAAC,OAAII,SAAO,MAAG1B,gBAAc;QACrCsB,QAAQ,AAAC,SAAMI,SAAO,MAAG3B,eAAa;IACxC;IACA,OAAOuB;AACT", "ignoreList": [0]}