{"version": 3, "sources": ["../../../src/server/request/fallback-params.ts"], "sourcesContent": ["import { getRouteMatcher } from '../../shared/lib/router/utils/route-matcher'\nimport { getRouteRegex } from '../../shared/lib/router/utils/route-regex'\n\nexport type FallbackRouteParams = ReadonlyMap<string, string>\n\nfunction getParamKeys(page: string) {\n  const pattern = getRouteRegex(page)\n  const matcher = getRouteMatcher(pattern)\n\n  // Get the default list of allowed params.\n  return Object.keys(matcher(page))\n}\n\nexport function getFallbackRouteParams(\n  pageOrKeys: string | readonly string[]\n): FallbackRouteParams | null {\n  let keys: readonly string[]\n  if (typeof pageOrKeys === 'string') {\n    keys = getParamKeys(pageOrKeys)\n  } else {\n    keys = pageOrKeys\n  }\n\n  // If there are no keys, we can return early.\n  if (keys.length === 0) return null\n\n  const params = new Map<string, string>()\n\n  // As we're creating unique keys for each of the dynamic route params, we only\n  // need to generate a unique ID once per request because each of the keys will\n  // be also be unique.\n  const uniqueID = Math.random().toString(16).slice(2)\n\n  for (const key of keys) {\n    params.set(key, `%%drp:${key}:${uniqueID}%%`)\n  }\n\n  return params\n}\n"], "names": ["getFallbackRouteParams", "get<PERSON>ara<PERSON><PERSON><PERSON><PERSON>", "page", "pattern", "getRouteRegex", "matcher", "getRouteMatcher", "Object", "keys", "page<PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "params", "Map", "uniqueID", "Math", "random", "toString", "slice", "key", "set"], "mappings": ";;;;+BAagBA;;;eAAAA;;;8BAbgB;4BACF;AAI9B,SAASC,aAAaC,IAAY;IAChC,MAAMC,UAAUC,IAAAA,yBAAa,EAACF;IAC9B,MAAMG,UAAUC,IAAAA,6BAAe,EAACH;IAEhC,0CAA0C;IAC1C,OAAOI,OAAOC,IAAI,CAACH,QAAQH;AAC7B;AAEO,SAASF,uBACdS,UAAsC;IAEtC,IAAID;IACJ,IAAI,OAAOC,eAAe,UAAU;QAClCD,OAAOP,aAAaQ;IACtB,OAAO;QACLD,OAAOC;IACT;IAEA,6CAA6C;IAC7C,IAAID,KAAKE,MAAM,KAAK,GAAG,OAAO;IAE9B,MAAMC,SAAS,IAAIC;IAEnB,8EAA8E;IAC9E,8EAA8E;IAC9E,qBAAqB;IACrB,MAAMC,WAAWC,KAAKC,MAAM,GAAGC,QAAQ,CAAC,IAAIC,KAAK,CAAC;IAElD,KAAK,MAAMC,OAAOV,KAAM;QACtBG,OAAOQ,GAAG,CAACD,KAAK,CAAC,MAAM,EAAEA,IAAI,CAAC,EAAEL,SAAS,EAAE,CAAC;IAC9C;IAEA,OAAOF;AACT", "ignoreList": [0]}