{"version": 3, "sources": ["../../../../src/experimental/testing/server/utils.ts"], "sourcesContent": ["import type { IncomingHttpHeaders } from 'http'\nimport { MockedRequest } from '../../../server/lib/mock-request'\nimport { NodeNextRequest } from '../../../server/base-http/node'\nimport type { BaseNextRequest } from '../../../server/base-http'\nimport type { NextResponse } from '../../../server/web/exports'\nimport { parseUrl } from '../../../lib/url'\n\nexport function constructRequest({\n  url,\n  headers = {},\n  cookies = {},\n}: {\n  url: string\n  headers?: IncomingHttpHeaders\n  cookies?: Record<string, string>\n}): BaseNextRequest {\n  if (!headers) {\n    headers = {}\n  }\n  if (!headers.host) {\n    headers.host = parseUrl(url)?.host\n  }\n  if (cookies) {\n    headers = {\n      ...headers,\n      cookie: Object.entries(cookies)\n        .map(([name, value]) => `${name}=${value}`)\n        .join(';'),\n    }\n  }\n  return new NodeNextRequest(new MockedRequest({ url, headers, method: 'GET' }))\n}\n\n/**\n * Returns the URL of the redirect if the response is a redirect response or\n * returns null if the response is not.\n */\nexport function getRedirectUrl(response: NextResponse): string | null {\n  return response.headers.get('location')\n}\n\n/**\n * Checks whether the provided response is a rewrite response to a different\n * URL.\n */\nexport function isRewrite(response: NextResponse): boolean {\n  return Boolean(getRewrittenUrl(response))\n}\n\n/**\n * Returns the URL of the response rewrite if the response is a rewrite, or\n * returns null if the response is not.\n */\nexport function getRewrittenUrl(response: NextResponse): string | null {\n  return response.headers.get('x-middleware-rewrite')\n}\n"], "names": ["constructRequest", "getRedirectUrl", "getRewrittenUrl", "isRewrite", "url", "headers", "cookies", "host", "parseUrl", "cookie", "Object", "entries", "map", "name", "value", "join", "NodeNextRequest", "MockedRequest", "method", "response", "get", "Boolean"], "mappings": ";;;;;;;;;;;;;;;;;IAOgBA,gBAAgB;eAAhBA;;IA8BAC,cAAc;eAAdA;;IAgBAC,eAAe;eAAfA;;IARAC,SAAS;eAATA;;;6BA5Cc;sBACE;qBAGP;AAElB,SAASH,iBAAiB,EAC/BI,GAAG,EACHC,UAAU,CAAC,CAAC,EACZC,UAAU,CAAC,CAAC,EAKb;IACC,IAAI,CAACD,SAAS;QACZA,UAAU,CAAC;IACb;IACA,IAAI,CAACA,QAAQE,IAAI,EAAE;YACFC;QAAfH,QAAQE,IAAI,IAAGC,YAAAA,IAAAA,aAAQ,EAACJ,yBAATI,UAAeD,IAAI;IACpC;IACA,IAAID,SAAS;QACXD,UAAU;YACR,GAAGA,OAAO;YACVI,QAAQC,OAAOC,OAAO,CAACL,SACpBM,GAAG,CAAC,CAAC,CAACC,MAAMC,MAAM,GAAK,GAAGD,KAAK,CAAC,EAAEC,OAAO,EACzCC,IAAI,CAAC;QACV;IACF;IACA,OAAO,IAAIC,qBAAe,CAAC,IAAIC,0BAAa,CAAC;QAAEb;QAAKC;QAASa,QAAQ;IAAM;AAC7E;AAMO,SAASjB,eAAekB,QAAsB;IACnD,OAAOA,SAASd,OAAO,CAACe,GAAG,CAAC;AAC9B;AAMO,SAASjB,UAAUgB,QAAsB;IAC9C,OAAOE,QAAQnB,gBAAgBiB;AACjC;AAMO,SAASjB,gBAAgBiB,QAAsB;IACpD,OAAOA,SAASd,OAAO,CAACe,GAAG,CAAC;AAC9B", "ignoreList": [0]}