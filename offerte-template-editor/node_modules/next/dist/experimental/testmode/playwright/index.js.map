{"version": 3, "sources": ["../../../../src/experimental/testmode/playwright/index.ts"], "sourcesContent": ["// eslint-disable-next-line import/no-extraneous-dependencies\nimport * as base from '@playwright/test'\nimport type { NextFixture } from './next-fixture'\nimport type { NextOptions, NextOptionsConfig } from './next-options'\nimport type { NextWorkerFixture } from './next-worker-fixture'\nimport { applyNextWorkerFixture } from './next-worker-fixture'\nimport { applyNextFixture } from './next-fixture'\nimport { defaultPlaywrightConfig } from './default-config'\n\nexport { defaultPlaywrightConfig }\n\n// eslint-disable-next-line import/no-extraneous-dependencies\nexport * from '@playwright/test'\n\n// Export this second so it overrides the one from `@playwright/test`\nexport function defineConfig<T extends NextOptionsConfig, W>(\n  config: base.PlaywrightTestConfig<T, W>\n): base.PlaywrightTestConfig<T, W>\nexport function defineConfig<T extends NextOptionsConfig = NextOptionsConfig>(\n  config: base.PlaywrightTestConfig<T>\n): base.PlaywrightTestConfig<T> {\n  if (config.webServer !== undefined) {\n    // <PERSON><PERSON> doesn't merge the `webServer` field as we'd expect, so remove our default if the user specifies one.\n    const { webServer, ...partialDefaultPlaywrightConfig } =\n      defaultPlaywrightConfig as base.PlaywrightTestConfig<T>\n    return base.defineConfig<T>(partialDefaultPlaywrightConfig, config)\n  } else {\n    return base.defineConfig<T>(\n      defaultPlaywrightConfig as base.PlaywrightTestConfig<T>,\n      config\n    )\n  }\n}\n\nexport type { NextFixture, NextOptions }\nexport type { FetchHandlerResult } from '../proxy'\n\nexport const test = base.test.extend<\n  { next: NextFixture; nextOptions: NextOptions },\n  { _nextWorker: NextWorkerFixture }\n>({\n  nextOptions: [{ fetchLoopback: false }, { option: true }],\n\n  _nextWorker: [\n    // eslint-disable-next-line no-empty-pattern\n    async ({}, use) => {\n      await applyNextWorkerFixture(use)\n    },\n    { scope: 'worker', auto: true },\n  ],\n\n  next: async ({ nextOptions, _nextWorker, page }, use, testInfo) => {\n    await applyNextFixture(use, {\n      testInfo,\n      nextWorker: _nextWorker,\n      page,\n      nextOptions,\n    })\n  },\n})\n\nexport default test\n"], "names": ["defaultPlaywrightConfig", "defineConfig", "test", "config", "webServer", "undefined", "partialDefaultPlaywrightConfig", "base", "extend", "nextOptions", "fetch<PERSON><PERSON><PERSON>", "option", "_next<PERSON><PERSON><PERSON>", "use", "applyNextWorkerFixture", "scope", "auto", "next", "page", "testInfo", "applyNextFixture", "nextWorker"], "mappings": "AAAA,6DAA6D;;;;;;;;;;;;;;;;;;IA6D7D,OAAmB;eAAnB;;IApDSA,uBAAuB;eAAvBA,sCAAuB;;IAShBC,YAAY;eAAZA;;IAmBHC,IAAI;eAAJA;;;;2EApCS;mCAIiB;6BACN;+BACO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWjC,SAASD,aACdE,MAAoC;IAEpC,IAAIA,OAAOC,SAAS,KAAKC,WAAW;QAClC,kHAAkH;QAClH,MAAM,EAAED,SAAS,EAAE,GAAGE,gCAAgC,GACpDN,sCAAuB;QACzB,OAAOO,MAAKN,YAAY,CAAIK,gCAAgCH;IAC9D,OAAO;QACL,OAAOI,MAAKN,YAAY,CACtBD,sCAAuB,EACvBG;IAEJ;AACF;AAKO,MAAMD,OAAOK,MAAKL,IAAI,CAACM,MAAM,CAGlC;IACAC,aAAa;QAAC;YAAEC,eAAe;QAAM;QAAG;YAAEC,QAAQ;QAAK;KAAE;IAEzDC,aAAa;QACX,4CAA4C;QAC5C,OAAO,EAAE,EAAEC;YACT,MAAMC,IAAAA,yCAAsB,EAACD;QAC/B;QACA;YAAEE,OAAO;YAAUC,MAAM;QAAK;KAC/B;IAEDC,MAAM,OAAO,EAAER,WAAW,EAAEG,WAAW,EAAEM,IAAI,EAAE,EAAEL,KAAKM;QACpD,MAAMC,IAAAA,6BAAgB,EAACP,KAAK;YAC1BM;YACAE,YAAYT;YACZM;YACAT;QACF;IACF;AACF;MAEA,WAAeP", "ignoreList": [0]}